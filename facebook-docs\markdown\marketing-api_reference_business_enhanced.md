# Facebook Marketing API Business Reference

## Summary
Complete reference documentation for the Facebook Marketing API Business object, which represents a specific business on Facebook. Covers reading business information, creating new businesses, updating business details, and managing business relationships.

## Key Points
- Business object represents a Facebook business entity with comprehensive asset management capabilities
- Creating businesses requires BUSINESS_MANAGEMENT permission or development mode limitations
- Businesses can have complex relationships including agencies, clients, and child businesses
- Extensive edge connections allow management of pages, ad accounts, pixels, and other assets
- Two-factor authentication and verification status provide security and trust indicators

## API Endpoints
- `GET /v23.0/{business-id}`
- `POST /{user_id}/businesses`
- `POST /{business_id}/owned_businesses`
- `POST /{business_id}`
- `DELETE /{business_id}/agencies`
- `DELETE /{business_id}/clients`
- `DELETE /{business_id}/pages`
- `DELETE /{business_id}/ad_accounts`

## Parameters
- business-id
- name
- vertical
- email
- primary_page
- timezone_id
- two_factor_type
- survey_business_type
- should_generate_name
- shared_page_id
- page_permitted_tasks

## Content
# Facebook Marketing API Business Reference

## Overview

The Business object represents a specific business on Facebook. You can make API calls to the business ID to access business information and manage business assets.

**Finding Business ID**: Go to Business Manager > Business Settings > Business Info to find your business ID.

## Reading Business Information

### Endpoint
```http
GET /v23.0/{business-id} HTTP/1.1
Host: graph.facebook.com
```

### Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | numeric string | The business account ID (Default) |
| `name` | string | The name of the business (Default) |
| `block_offline_analytics` | bool | Whether offline analytics is blocked |
| `created_by` | BusinessUser\|SystemUser | The creator of this business |
| `created_time` | datetime | Business creation time |
| `extended_updated_time` | datetime | Extended credits update time |
| `is_hidden` | bool | Whether the business is hidden |
| `link` | string | URI for business profile page |
| `payment_account_id` | numeric string | Payment account ID |
| `primary_page` | Page | Primary Facebook Page |
| `profile_picture_uri` | string | Profile picture URI |
| `timezone_id` | unsigned int32 | Business timezone |
| `two_factor_type` | enum | Two factor authentication type |
| `updated_by` | BusinessUser\|SystemUser | Last updater |
| `updated_time` | datetime | Last update time |
| `verification_status` | enum | Verification status |
| `vertical` | string | Industry vertical |
| `vertical_id` | unsigned int32 | Vertical industry ID |

### Key Edges

- **Assets**: `adspixels`, `owned_pages`, `owned_product_catalogs`, `instagram_accounts`
- **Users**: `business_users`, `system_users`, `pending_users`
- **Relationships**: `agencies`, `clients`, `owned_businesses`
- **Analytics**: `ad_studies`, `ads_reporting_mmm_reports`
- **Commerce**: `commerce_merchant_settings`

## Creating Businesses

### Requirements
- `BUSINESS_MANAGEMENT` permission (via app review)
- Development mode: limited to 2 child businesses

### Create via User
```http
POST /{user_id}/businesses
```

**Required Parameters:**
- `name`: Business name
- `vertical`: Industry vertical

**Optional Parameters:**
- `email`: Business admin email
- `primary_page`: Primary Page ID
- `timezone_id`: Timezone ID
- `survey_business_type`: Business type (AGENCY, ADVERTISER, etc.)

### Create Child Business
```http
POST /{business_id}/owned_businesses
```

**Required Parameters:**
- `name`: Business name
- `vertical`: Industry vertical

**Special Parameters:**
- `should_generate_name`: Auto-generate cleaned name
- `shared_page_id`: Share a page with child business

## Updating Businesses

```http
POST /{business_id}
```

**Parameters:**
- `name`: Business name
- `primary_page`: Primary page ID
- `timezone_id`: Timezone ID
- `two_factor_type`: 2FA requirements
- `vertical`: Industry vertical

## Managing Relationships

### Remove Agency
```http
DELETE /{business_id}/agencies
```

### Remove Client
```http
DELETE /{business_id}/clients
```

### Remove Page
```http
DELETE /{business_id}/pages
```

### Remove Ad Account
```http
DELETE /{business_id}/ad_accounts
```

## Common Error Codes

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | Action deemed abusive or disallowed |
| 415 | Two factor authentication required |
| 3910 | Need permission to edit Business Manager |
| 3911 | Need permission to set up Business Manager |
| 3912 | Technical issue, changes not saved |
| 3913 | No permission to create Business Manager |
| 3914 | Cannot remove last admin |
| 3918 | Page already owned by another Business Manager |
| 3947 | Business Manager name already in use |
| 3973/3974 | Invalid Business Manager name |

## Best Practices

1. **Permissions**: Ensure proper permissions before creating/modifying businesses
2. **Naming**: Use unique, descriptive business names
3. **2FA**: Consider enabling two-factor authentication for security
4. **Asset Management**: Properly organize assets using business asset groups
5. **Error Handling**: Implement robust error handling for common scenarios

## Examples
GET /v23.0/{business-id} HTTP/1.1

Finding business ID in Business Manager > Business Settings > Business Info

Creating child business with auto-generated name using should_generate_name parameter

---
**Tags:** Facebook Marketing API, Business Management, Graph API, Business Manager, Asset Management, Permissions, Authentication
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/business
**Processed:** 2025-06-25T15:44:15.683Z