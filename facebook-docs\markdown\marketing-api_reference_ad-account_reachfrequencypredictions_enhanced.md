# Facebook Marketing API - Ad Account Reach and Frequency Predictions

## Summary
This endpoint allows creating reach and frequency predictions for Facebook ad campaigns. It helps advertisers estimate reach, frequency, and budget requirements for their campaigns before launching them.

## Key Points
- Only POST (create) operations are supported - no reading, updating, or deleting
- Two prediction modes: budget-based (mode 0) or reach-based (mode 1)
- Instagram destination IDs now use ig_user_id instead of instagram_actor_id (v23.0+)
- Minimum reach requirement is typically 1,000,000 for most countries
- Campaign duration cannot exceed 8 weeks from current time

## API Endpoints
- `POST /act_{ad_account_id}/reachfrequencypredictions`

## Parameters
- budget
- target_spec
- prediction_mode
- objective
- destination_ids
- frequency_cap
- start_time
- stop_time
- reach
- day_parting_schedule
- campaign_group_id
- deal_id
- optimization_goal
- story_event_type
- instream_packages

## Content
# Ad Account Reach and Frequency Predictions

**Important Update (v23.0):** Beginning with v23.0, the `instagram_destination_id` field will return the `ig_user_id` rather than the `instagram_actor_id`. The `instagram_actor_id` is also no longer supported in the `destination_ids` parameter; update your API calls to use the `ig_user_id` instead.

## Overview

The Reach and Frequency Predictions endpoint allows you to create predictions for reach and frequency campaigns before launching them. This helps in planning and budgeting for your advertising campaigns.

## Supported Operations

- **Reading**: Not supported
- **Creating**: ✅ Supported via POST request
- **Updating**: Not supported
- **Deleting**: Not supported

## Creating Predictions

Make a POST request to the `reachfrequencypredictions` edge:

```
POST /act_{ad_account_id}/reachfrequencypredictions
```

This will create a [ReachFrequencyPrediction](/docs/marketing-api/reference/reach-frequency-prediction/) object.

## Key Parameters

### Required Parameters
- `budget`: Expected lifetime budget in cents
- `target_spec`: Targeting specification for the prediction
- `start_time`: Unix timestamp for campaign start
- `stop_time`: Unix timestamp for campaign end

### Important Parameters
- `prediction_mode`: Set to `0` for budget-based prediction (requires `reach`), or `1` for reach-based prediction (requires `budget`)
- `objective`: Campaign objective (default: `REACH`)
- `destination_ids`: Array of Facebook Page or App IDs
- `frequency_cap`: Lifetime frequency cap for the campaign
- `day_parting_schedule`: Delivery schedule configuration

### Example Day Parting Schedule
```json
[{"start_minute":360,"end_minute":1440,"days":[0,1,2,3,4,5,6]}]
```

## Supported Objectives
- `BRAND_AWARENESS`
- `LINK_CLICKS`
- `POST_ENGAGEMENT`
- `MOBILE_APP_INSTALLS`
- `WEBSITE_CONVERSIONS`
- `REACH`
- `VIDEO_VIEWS`

## Response Format

Returns a struct with:
```json
{
  "id": "numeric_string"
}
```

## Common Error Codes
- `100`: Invalid parameter
- `2625`: Invalid reach frequency campaign request
- `105`: Too many parameters
- `2641`: Restricted locations in targeting
- `2628`: Error updating prediction state
- `613`: Rate limit exceeded

## Important Notes

1. **Targeting Restrictions**: Cannot use `rightcolumn` with feed placements, specify more than one country, or use Website Custom Audiences
2. **Minimum Reach**: Must be at least 1,000,000 in most cases
3. **Campaign Duration**: Maximum 8 weeks ahead of current time
4. **Day Parting**: Must have at least 3 hours of delivery each day
5. **Targeting Spec Limit**: JSON serialized targeting spec should not exceed 65,000 characters

## Examples
Day parting schedule: [{"start_minute":360,"end_minute":1440,"days":[0,1,2,3,4,5,6]}]

POST /act_{ad_account_id}/reachfrequencypredictions

Response: {"id": "numeric_string"}

---
**Tags:** Facebook Marketing API, Reach and Frequency, Campaign Prediction, Ad Account, Budget Planning, Targeting
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/reachfrequencypredictions/
**Processed:** 2025-06-25T15:38:50.325Z