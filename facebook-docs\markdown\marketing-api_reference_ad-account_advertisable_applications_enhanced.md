# Facebook Marketing API - Ad Account Advertisable Applications

## Summary
This endpoint returns the advertisable applications associated with an Ad Account. It provides access to applications that can be promoted through Facebook advertising, with special restrictions for apps in developer mode.

## Key Points
- Returns advertisable applications associated with an Ad Account
- Apps in developer mode require admin access token for visibility
- Supports filtering by app_id or business_id parameters
- Returns Application nodes with additional advertising-specific fields
- Read-only endpoint - no create, update, or delete operations supported

## API Endpoints
- `GET /v23.0/{ad-account-id}/advertisable_applications`

## Parameters
- app_id
- business_id
- advertisable_app_events
- cpa_access

## Content
# Ad Account Advertisable Applications

A call to this edge returns the Ad Account's advertisable applications.

## Restrictions

Applications in developer mode are only returned if the access token belongs to a user who is an admin on the app. The restrictions do not apply to apps that are live.

## Reading

### Endpoint
```
GET /v23.0/{ad-account-id}/advertisable_applications
```

### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `app_id` | numeric string or integer | Specify App ID for the specific application |
| `business_id` | numeric string or integer | Specify Business ID for applications under a specific business |

### Response Format

Reading from this edge will return a JSON formatted result:

```json
{
    "data": [],
    "paging": {}
}
```

#### Data Fields

A list of Application nodes with the following additional fields:

| Field | Type | Description |
|-------|------|--------------|
| `advertisable_app_events` | list<string> | Available app events to be advertised |
| `cpa_access` | CPA | CPA Access for apps |

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/advertisable_applications HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/advertisable_applications',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/advertisable_applications",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 3000 | Reading insights of a Page, business, app, domain or event source group not owned by the querying user or application |
| 190 | Invalid OAuth 2.0 Access Token |
| 80004 | Too many calls to this ad-account. Rate limiting applied |
| 100 | Invalid parameter |

## Operations

- **Creating**: Not supported on this endpoint
- **Updating**: Not supported on this endpoint  
- **Deleting**: Not supported on this endpoint

## Examples
HTTP GET request example

PHP SDK implementation

JavaScript SDK usage

Android SDK example

iOS SDK example

---
**Tags:** Facebook Marketing API, Ad Account, Applications, Advertising, Graph API, Mobile Apps  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/advertisable_applications/  
**Processed:** 2025-06-25T16:22:31.943Z