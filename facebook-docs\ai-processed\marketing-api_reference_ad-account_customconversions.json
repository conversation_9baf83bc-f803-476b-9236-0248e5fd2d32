{"title": "Facebook Marketing API - Ad Account Custom Conversions Reference", "summary": "Complete reference documentation for managing custom conversions in Facebook Ad Accounts through the Marketing API. Covers reading, creating, and managing custom conversion events from event sources like Meta Pixel for ad optimization and measurement.", "content": "# Ad Account Custom Conversions\n\nData on custom conversion events from event sources, such as a Meta Pixel. You can query this data to measure the effectiveness of ads or use it to optimize ad delivery to target people who converted as defined by your customization and rules.\n\n## Reading Custom Conversions\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/customconversions\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Response Format\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Response Fields\n- **data**: A list of CustomConversion nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/customconversions HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/customconversions',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/customconversions\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Error Codes (Reading)\n- **200**: Permissions error\n- **100**: Invalid parameter\n- **80004**: Too many calls to this ad-account. Rate limiting applied.\n- **190**: Invalid OAuth 2.0 Access Token\n- **368**: Action deemed abusive or disallowed\n\n## Creating Custom Conversions\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/customconversions\n```\n\n### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|----------|\n| `action_source_type` | enum | Action source type: app, chat, email, other, phone_call, physical_store, system_generated, website, business_messaging | No |\n| `advanced_rule` | string | Advanced ruleset allowing multiple sources | No |\n| `custom_event_type` | enum | Event type: ADD_PAYMENT_INFO, ADD_TO_CART, ADD_TO_WISHLIST, COMPLETE_REGISTRATION, CONTENT_VIEW, INITIATED_CHECKOUT, LEAD, PURCHASE, SEARCH, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, LISTING_INTERACTION, FACEBOOK_SELECTED, OTHER | No |\n| `default_conversion_value` | float | Default conversion value (default: 0) | No |\n| `description` | string | Description of the conversion | No |\n| `event_source_id` | numeric string/integer | Event source ID (Pixel, offline event sets, etc.) | No |\n| `name` | string | Name of the conversion | **Yes** |\n| `rule` | string | Rule that events must fulfill to count as custom conversion | No |\n\n### Return Type\nSupports read-after-write and returns:\n```json\n{\n  \"id\": \"numeric string\",\n  \"is_custom_event_type_predicted\": \"numeric string\"\n}\n```\n\n### Error Codes (Creating)\n- **200**: Permissions error\n- **100**: Invalid parameter\n\n## Updating and Deleting\n\nBoth updating and deleting operations are **not supported** on this endpoint.\n\n## API Version\nCurrent version: **v23.0**", "keyPoints": ["Custom conversions track events from sources like Meta Pixel for ad measurement and optimization", "Reading custom conversions requires no parameters and returns paginated CustomConversion nodes", "Creating custom conversions requires only a name parameter, with many optional configuration options", "The endpoint supports various event types and action source types for flexible conversion tracking", "Update and delete operations are not supported on this endpoint"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/customconversions", "POST /act_{ad_account_id}/customconversions"], "parameters": ["action_source_type", "advanced_rule", "custom_event_type", "default_conversion_value", "description", "event_source_id", "name", "rule"], "examples": ["HTTP GET request for reading custom conversions", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest example", "iOS SDK FBSDKGraphRequest example"], "tags": ["Facebook Marketing API", "Custom Conversions", "Ad Account", "<PERSON><PERSON>", "Event Tracking", "API Reference"], "relatedTopics": ["CustomConversion object reference", "Graph API pagination", "Meta Pixel events", "Ad optimization", "Event sources", "OAuth 2.0 Access Tokens", "Rate limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/customconversions/", "processedAt": "2025-06-25T16:26:55.612Z", "processor": "openrouter-claude-sonnet-4"}