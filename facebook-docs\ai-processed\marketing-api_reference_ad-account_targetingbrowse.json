{"title": "Facebook Marketing API - Ad Account Targeting Browse Reference", "summary": "Reference documentation for the Ad Account Targetingbrowse endpoint, which provides a unified browse tree as a flat list for audience targeting options. This read-only endpoint allows developers to retrieve targeting categories with optional filtering and pagination.", "content": "# Ad Account Targetingbrowse\n\n## Overview\n\nThe Ad Account Targetingbrowse endpoint provides a unified browse tree as a flat list for audience targeting options. Use the parent key to recreate the tree structure. This is a read-only endpoint that supports filtering by targeting type and regulated categories.\n\n**Graph API Version:** v23.0\n\n## Reading\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/targetingbrowse\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `include_nodes` | boolean | Include searchable nodes (e.g., work/edu entries). Default: false. Internal use only. |\n| `limit_type` | enum | Limit the type of audience to retrieve. Options include: interests, education_schools, education_majors, work_positions, work_employers, relationship_statuses, interested_in, user_adclusters, college_years, education_statuses, family_statuses, industries, life_events, politics, behaviors, income, net_worth, home_type, home_ownership, home_value, ethnic_affinity, generation, household_composition, moms, office_type, location_categories |\n| `regulated_categories` | array<enum> | The regulated categories of the campaign. Options: NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES |\n\n### Response Format\n\nThe endpoint returns a JSON formatted result:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n- **data**: A list of AdAccountTargetingUnified nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 80004 | Too many calls to this ad-account. Wait and try again. |\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n\n## Limitations\n\n- **Creating**: Not supported\n- **Updating**: Not supported  \n- **Deleting**: Not supported\n\nThis is a read-only endpoint for browsing available targeting options.", "keyPoints": ["Provides a unified browse tree of targeting options as a flat list", "Read-only endpoint - no create, update, or delete operations supported", "Supports filtering by targeting type and regulated categories", "Returns AdAccountTargetingUnified nodes with pagination support", "Subject to rate limiting - error 80004 indicates too many calls"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/targetingbrowse"], "parameters": ["include_nodes", "limit_type", "regulated_categories", "ad-account-id"], "examples": ["GET /v23.0/{ad-account-id}/targetingbrowse HTTP/1.1\nHost: graph.facebook.com"], "tags": ["Facebook Marketing API", "Ad Account", "Targeting", "Audience", "Browse", "Graph API", "Reference"], "relatedTopics": ["Graph API pagination", "OAuth 2.0 Access Tokens", "Rate limiting", "AdAccountTargetingUnified", "Audience targeting", "Regulated categories", "Marketing API"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/targetingbrowse/", "processedAt": "2025-06-25T15:40:03.857Z", "processor": "openrouter-claude-sonnet-4"}