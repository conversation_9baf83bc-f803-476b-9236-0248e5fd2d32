# Ad, Image

Graph API Version

[v23.0](#)

# 

Ad, Image

Upload and manage images to later use in [ad creative](/docs/marketing-api/adcreative/). Image formats, sizes and design guidelines depend up on your type of ad, see [Ads Guide](https://www.facebook.com/business/ads-guide/). See [Image Crop](/docs/marketing-api/image-crops/) and [Ads Guide](https://www.facebook.com/business/ads-guide/).

For example, provide an image file such as `.bmp`, `.jpeg`, or `.gif`:

PHP Business SDKPython Business SDKJava Business SDKcURL

```
`use FacebookAds\Object\AdImage;
use FacebookAds\Object\Fields\AdImageFields;

$image = new AdImage(null, 'act_<AD_ACCOUNT_ID>');
$image->{AdImageFields::FILENAME} = '<IMAGE_PATH>';

$image->create();
echo 'Image Hash: '.$image->{AdImageFields::HASH}.PHP_EOL;`
```

Once you have the image hash, you can use in in an ad creative:

PHP Business SDKPython Business SDKcURL

```
`use FacebookAds\Object\AdCreative;
use FacebookAds\Object\Ad;
use FacebookAds\Object\Fields\AdCreativeFields;
use FacebookAds\Object\Fields\AdFields;

// First, upload the ad image that you will use in your ad creative
// Please refer to Ad Image Create for details.

// Then, use the image hash returned from above
$creative = new AdCreative(null, 'act_<AD_ACCOUNT_ID>');
$creative->setData(array(
  AdCreativeFields::TITLE => 'My Test Creative',
  AdCreativeFields::BODY => 'My Test Ad Creative Body',
  AdCreativeFields::OBJECT_URL => 'https://www.facebook.com/facebook',
  AdCreativeFields::IMAGE_HASH => '<IMAGE_HASH>',
));

// Finally, create your ad along with ad creative.
// Please note that the ad creative is not created independently, rather its
// data structure is appended to the ad group
$ad = new Ad(null, 'act_<AD_ACCOUNT_ID>');
$ad->setData(array(
  AdFields::NAME => 'My Ad',
  AdFields::ADSET_ID => <AD_SET_ID>,
  AdFields::CREATIVE => $creative,
));
$ad->create(array(
  Ad::STATUS_PARAM_NAME => Ad::STATUS_PAUSED,
));`
```

## Reading

Image for use in ad creatives can be uploaded and managed independently of the ad itself. The image used in an ad creative can be specified in the following ways:

*   By image hash value of a previously uploaded image.
*   By uploading the image at ad or ad creative creation time.

### Examples

#### By Ad Account

Get available images for an ad account. Images used in every creative for the account should appear in the list.

PHP Business SDKPython Business SDKJava Business SDKcURL

```
`use FacebookAds\Object\AdAccount;

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$images = $account->getAdImages();`
```

#### By Image

To get specific images, specify hashes of the images in a `hashes`:

PHP Business SDKPython Business SDKJava Business SDKcURL

```
`use FacebookAds\Object\AdAccount;

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$images = $account->getAdImages(
  array(),
  array(
    'hashes' => array(
      '<IMAGE_1_HASH>',
      '<IMAGE_2_HASH>',
    ),
  ));`
```

### Parameters

This endpoint doesn't have any parameters.

### Fields

Field

Description

`id`

token with structure: ID

The ID of the image.

`account_id`

numeric string

The ad account that owns the image.

`created_time`

datetime

Time the image was created.

`creatives`

list<numeric string>

A list of ad creative IDs that this ad image is being used in. Not applicable for creatives using `object_story_spec` and a URL in the `picture` field.

`hash`

string

The hash which uniquely identifies the image.

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

`height`

unsigned int32

The height of the image.

`is_associated_creatives_in_adgroups`

bool

SELF\_EXPLANATORY

`name`

string

The filename of the image. The maximum length of this string is 100 characters.

`original_height`

unsigned int32

The height of the image that was originally uploaded.

`original_width`

unsigned int32

The width of the image that was originally uploaded.

`permalink_url`

string

A permanent URL of the image to use in story creatives.

`status`

enum {ACTIVE, INTERNAL, DELETED}

Status of the image.

`updated_time`

datetime

Time the image was updated.

`url`

string

A temporary URL which the image can be retrieved at. **Do not use this URL in ad creative creation**.

`url_128`

string

A temporary URL pointing to a version of the image resized to fit within a 128x128 pixel box

`width`

unsigned int32

The width of the image.

### Error Codes

Error

Description

100

Invalid parameter

## Creating

Upload an image or zip file, get back a hash, and use the hash in an ad or creative. You must include a filename extension such as `sample.jpg`, not `sample` or `sample.tmp`.

### Examples

#### Zip file

PHP Business SDKPython Business SDKJava Business SDKcURL

```
`use FacebookAds\Object\AdImage;
use FacebookAds\Object\Fields\AdImageFields;

$images = AdImage::createFromZip('<ZIP_PATH>', 'act_<AD_ACCOUNT_ID>');

foreach ($images as $image) {
  echo $image->{AdImageFields::HASH}.PHP_EOL;
}`
```

#### bytes

```
curl \\
  \-F 'bytes=iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAAOVBMVEX///87WZg7WZg7WZg7WZg7WZg7WZg7WZg7WZg7WZg7WZhMeMJEaa5Xi9tKdb0+Xp5Wi9tXjNxThNH+wk/7AAAACnRSTlMAsHIoaM7g/fx9Zr/g5QAAAGlJREFUeNplkFsOwCAIBPGJrtbX/Q/bqm1qwnxuJrBAE6OVD15pQy/WYePsDiIjp9FGyuC4DK7l6pOrVH4s41D6R4EzpJGXsa0MTQqp/yQo8hhHMuApoB1JQ5COnCN3yT6ys7xL3i7/cwMYsAveYa+MxAAAAABJRU5ErkJggg=='
  \-F 'access\_token=<ACCESS\_TOKEN>' \\
"https://graph.facebook.com/<API\_VERSION>/act\_<ACCOUNT\_ID>/adimages"
```

#### Upload an Image on Create

You can upload an image instead of using an image hash when you create an ad or ad creative. Add the image file to the multi-part MIME POST and specify the file name. For example:

```
curl \\
  \-F 'campaign\_id=<AD\_SET\_ID>' \\
  \-F 'creative={"title":"test title","body":"test","object\_url":"http:\\/\\/www.test.com","image\_file":"test.jpg"}' \\
  \-F 'test.jpg=@test.jpg'
  \-F 'name=My ad' \\
  \-F 'access\_token=<ACCESS\_TOKEN>' \\
"https://graph.facebook.com/<API\_VERSION>/act\_<ACCOUNT\_ID>/ads"
```

The response contains:

Name

Description

id

ID of the ad

  

#### Copying Images

To copy an ad image from one account to another, make a `POST` request to `/act_{DESTINATION_ACCOUNT_ID}/adimages`. Provide the source account ID without the `act_` prefix and a hash of the image in `copy_from`. This copies the image from the source to the destination account. **Your app's user must have access to read the creatives from the source account** or you cannot copy images from the account.

```
curl \\
  \-F 'copy\_from={"source\_account\_id":"<SOURCE\_ACCOUNT\_ID>", "hash":"02bee5277ec507b6fd0f9b9ff2f22d9c"}'
  \-F 'access\_token=<ACCESS\_TOKEN>' 
"https://graph.facebook.com/<API\_VERSION>/act\_<DESTINATION\_ACCOUNT\_ID>/adimages"
```

  

You can make a POST request to `adimages` edge from the following paths:

*   [`/act_{ad_account_id}/adimages`](/docs/marketing-api/reference/ad-account/adimages/)

When posting to this edge, an [AdImage](/docs/marketing-api/reference/ad-image/) will be created.

### Parameters

Parameter

Description

`bytes`

Base64 UTF-8 string

Image file. Example: `bytes = <image content in bytes format>`

`copy_from`

JSON or object-like arrays

This copies the Ad Image from the source to the destination account.  
`{"source_account_id":"<SOURCE_ACCOUNT_ID>"`, `"hash":"02bee5277ec507b6fd0f9b9ff2f22d9c"}`

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `images` in the return type.

Map {

string: Map {

string: Struct {

`hash`: string,

`url`: string,

`url_128`: string,

`url_256`: string,

`url_256_height`: string,

`url_256_width`: string,

`height`: int32,

`width`: int32,

`name`: string,

}

}

}

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

368

The action attempted has been deemed abusive or is otherwise disallowed

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

190

Invalid OAuth 2.0 Access Token

2635

You are calling a deprecated version of the Ads API. Please update to the latest version.

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can only delete ad images **not currently being used** in an ad creative.

PHP Business SDKPython Business SDKcURL

```
`use FacebookAds\Object\AdImage;
use FacebookAds\Object\Fields\AdImageFields;

$image = new AdImage(<IMAGE_ID>, 'act_<AD_ACCOUNT_ID>');
$image->{AdImageFields::HASH} = '<IMAGE_HASH>';
$image->deleteSelf();`
```

You can dissociate an [AdImage](/docs/marketing-api/reference/ad-image/) from an [AdAccount](/docs/marketing-api/reference/ad-account/) by making a DELETE request to [`/act_{ad_account_id}/adimages`](/docs/marketing-api/reference/ad-account/adimages/).

### Parameters

Parameter

Description

`hash`

string

Hash of the image you wish to delete.

Required

### Return Type

Struct {

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.