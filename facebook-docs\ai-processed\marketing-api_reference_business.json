{"title": "Facebook Marketing API Business Reference", "summary": "Complete reference documentation for the Facebook Marketing API Business object, which represents a specific business on Facebook. Covers reading business information, creating new businesses, updating business details, and managing business relationships.", "content": "# Facebook Marketing API Business Reference\n\n## Overview\n\nThe Business object represents a specific business on Facebook. You can make API calls to the business ID to access business information and manage business assets.\n\n**Finding Business ID**: Go to Business Manager > Business Settings > Business Info to find your business ID.\n\n## Reading Business Information\n\n### Endpoint\n```http\nGET /v23.0/{business-id} HTTP/1.1\nHost: graph.facebook.com\n```\n\n### Fields\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `id` | numeric string | The business account ID (Default) |\n| `name` | string | The name of the business (Default) |\n| `block_offline_analytics` | bool | Whether offline analytics is blocked |\n| `created_by` | BusinessUser\\|SystemUser | The creator of this business |\n| `created_time` | datetime | Business creation time |\n| `extended_updated_time` | datetime | Extended credits update time |\n| `is_hidden` | bool | Whether the business is hidden |\n| `link` | string | URI for business profile page |\n| `payment_account_id` | numeric string | Payment account ID |\n| `primary_page` | Page | Primary Facebook Page |\n| `profile_picture_uri` | string | Profile picture URI |\n| `timezone_id` | unsigned int32 | Business timezone |\n| `two_factor_type` | enum | Two factor authentication type |\n| `updated_by` | BusinessUser\\|SystemUser | Last updater |\n| `updated_time` | datetime | Last update time |\n| `verification_status` | enum | Verification status |\n| `vertical` | string | Industry vertical |\n| `vertical_id` | unsigned int32 | Vertical industry ID |\n\n### Key Edges\n\n- **Assets**: `adspixels`, `owned_pages`, `owned_product_catalogs`, `instagram_accounts`\n- **Users**: `business_users`, `system_users`, `pending_users`\n- **Relationships**: `agencies`, `clients`, `owned_businesses`\n- **Analytics**: `ad_studies`, `ads_reporting_mmm_reports`\n- **Commerce**: `commerce_merchant_settings`\n\n## Creating Businesses\n\n### Requirements\n- `BUSINESS_MANAGEMENT` permission (via app review)\n- Development mode: limited to 2 child businesses\n\n### Create via User\n```http\nPOST /{user_id}/businesses\n```\n\n**Required Parameters:**\n- `name`: Business name\n- `vertical`: Industry vertical\n\n**Optional Parameters:**\n- `email`: Business admin email\n- `primary_page`: Primary Page ID\n- `timezone_id`: Timezone ID\n- `survey_business_type`: Business type (AGENCY, ADVERTISER, etc.)\n\n### Create Child Business\n```http\nPOST /{business_id}/owned_businesses\n```\n\n**Required Parameters:**\n- `name`: Business name\n- `vertical`: Industry vertical\n\n**Special Parameters:**\n- `should_generate_name`: Auto-generate cleaned name\n- `shared_page_id`: Share a page with child business\n\n## Updating Businesses\n\n```http\nPOST /{business_id}\n```\n\n**Parameters:**\n- `name`: Business name\n- `primary_page`: Primary page ID\n- `timezone_id`: Timezone ID\n- `two_factor_type`: 2FA requirements\n- `vertical`: Industry vertical\n\n## Managing Relationships\n\n### Remove Agency\n```http\nDELETE /{business_id}/agencies\n```\n\n### Remove Client\n```http\nDELETE /{business_id}/clients\n```\n\n### Remove Page\n```http\nDELETE /{business_id}/pages\n```\n\n### Remove Ad Account\n```http\nDELETE /{business_id}/ad_accounts\n```\n\n## Common Error Codes\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | Action deemed abusive or disallowed |\n| 415 | Two factor authentication required |\n| 3910 | Need permission to edit Business Manager |\n| 3911 | Need permission to set up Business Manager |\n| 3912 | Technical issue, changes not saved |\n| 3913 | No permission to create Business Manager |\n| 3914 | Cannot remove last admin |\n| 3918 | Page already owned by another Business Manager |\n| 3947 | Business Manager name already in use |\n| 3973/3974 | Invalid Business Manager name |\n\n## Best Practices\n\n1. **Permissions**: Ensure proper permissions before creating/modifying businesses\n2. **Naming**: Use unique, descriptive business names\n3. **2FA**: Consider enabling two-factor authentication for security\n4. **Asset Management**: Properly organize assets using business asset groups\n5. **Error Handling**: Implement robust error handling for common scenarios", "keyPoints": ["Business object represents a Facebook business entity with comprehensive asset management capabilities", "Creating businesses requires BUSINESS_MANAGEMENT permission or development mode limitations", "Businesses can have complex relationships including agencies, clients, and child businesses", "Extensive edge connections allow management of pages, ad accounts, pixels, and other assets", "Two-factor authentication and verification status provide security and trust indicators"], "apiEndpoints": ["GET /v23.0/{business-id}", "POST /{user_id}/businesses", "POST /{business_id}/owned_businesses", "POST /{business_id}", "DELETE /{business_id}/agencies", "DELETE /{business_id}/clients", "DELETE /{business_id}/pages", "DELETE /{business_id}/ad_accounts"], "parameters": ["business-id", "name", "vertical", "email", "primary_page", "timezone_id", "two_factor_type", "survey_business_type", "should_generate_name", "shared_page_id", "page_permitted_tasks"], "examples": ["GET /v23.0/{business-id} HTTP/1.1", "Finding business ID in Business Manager > Business Settings > Business Info", "Creating child business with auto-generated name using should_generate_name parameter"], "tags": ["Facebook Marketing API", "Business Management", "Graph API", "Business Manager", "Asset Management", "Permissions", "Authentication"], "relatedTopics": ["Business Manager", "Ad Accounts", "Pages", "Pixels", "System Users", "Business Users", "Product Catalogs", "Instagram Accounts", "Two-Factor Authentication", "Business Asset Groups"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/business", "processedAt": "2025-06-25T15:44:15.683Z", "processor": "openrouter-claude-sonnet-4"}