# Facebook Marketing API - Ad Set Reference

## Summary
Complete reference documentation for Facebook Marketing API Ad Sets, including creation, reading, updating, and deletion operations. Ad sets group ads with shared budget, schedule, bid type, and targeting data.

## Key Points
- Ad sets group ads with shared budget, schedule, bid type, and targeting data
- EU-targeted ad sets require dsa_payor and dsa_beneficiary fields starting August 2023
- Minimum budget requirements vary by billing event and bid strategy
- Budget updates must be at least 10% greater than amount already spent
- Special ad categories (housing, employment, credit) have restricted targeting options

## API Endpoints
- `GET /v23.0/<AD_SET_ID>/`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/adsets`
- `POST /v23.0/<AD_SET_ID>/`
- `DELETE /v23.0/<AD_SET_ID>/`
- `POST /v23.0/<AD_SET_ID>/copies`

## Parameters
- name
- campaign_id
- targeting
- optimization_goal
- billing_event
- daily_budget
- lifetime_budget
- bid_amount
- bid_strategy
- start_time
- end_time
- status
- promoted_object
- dsa_payor
- dsa_beneficiary

## Content
# Facebook Marketing API - Ad Set Reference

## Overview

An ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data. Ad sets enable you to group ads according to your criteria, and you can retrieve the ad-related statistics that apply to a set.

## Creating Ad Sets

### Daily Budget Example

```bash
curl -X POST \
  -F 'name="My Reach Ad Set"' \
  -F 'optimization_goal="REACH"' \
  -F 'billing_event="IMPRESSIONS"' \
  -F 'bid_amount=2' \
  -F 'daily_budget=1000' \
  -F 'campaign_id="<AD_CAMPAIGN_ID>"' \
  -F 'targeting={
       "geo_locations": {
         "countries": [
           "US"
         ]
       },
       "facebook_positions": [
         "feed"
       ]
     }' \
  -F 'status="PAUSED"' \
  -F 'promoted_object={
       "page_id": "<PAGE_ID>"
     }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Lifetime Budget Example

```bash
curl -X POST \
  -F 'name="My First Adset"' \
  -F 'lifetime_budget=20000' \
  -F 'start_time="2025-06-25T08:51:33-0700"' \
  -F 'end_time="2025-07-05T08:51:33-0700"' \
  -F 'campaign_id="<AD_CAMPAIGN_ID>"' \
  -F 'bid_amount=100' \
  -F 'billing_event="LINK_CLICKS"' \
  -F 'optimization_goal="LINK_CLICKS"' \
  -F 'targeting={
       "facebook_positions": [
         "feed"
       ],
       "geo_locations": {
         "countries": [
           "US"
         ]
       },
       "publisher_platforms": [
         "facebook",
         "audience_network"
       ]
     }' \
  -F 'status="PAUSED"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets
```

## Limits

| Limit | Value |
|-------|-------|
| Maximum ad sets per regular ad account | 5000 non-deleted ad sets |
| Maximum ad sets per bulk ad account | 10000 non-deleted ad sets |
| Maximum ads per ad set | 50 non-archived ads |

## Special Requirements

### Housing, Employment and Credit Ads

Advertisers must specify a `special_ad_category` for ad campaigns that market housing, employment, and credit. This restricts available targeting options.

### European Union Targeting Requirements

Beginning May 16, 2023, advertisers targeting the EU must provide:
- **Beneficiary information** (`dsa_beneficiary`): Who benefits from the ad
- **Payor information** (`dsa_payor`): Who pays for the ad

Without this information, ads will not be published starting August 16, 2023.

## Reading Ad Sets

### Basic Read Example

```bash
curl -X GET \
  -d 'fields="name,status"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/
```

### Read with UNIX Timestamp Format

```bash
curl -X GET \
  -d 'fields="id,name,start_time,end_time"' \
  -d 'date_format="U"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/
```

### Read Multiple Ad Sets (PHP)

```php
use FacebookAds\Object\AdSet;
use FacebookAds\Object\Fields\AdSetFields;

$ad_set_ids = array(<AD_SET_1_ID>, <AD_SET_2_ID>, <AD_SET_3_ID>);
$fields = array(
  AdSetFields::NAME,
  AdSetFields::CONFIGURED_STATUS,
  AdSetFields::EFFECTIVE_STATUS,
);
$adsets = AdSet::readIds($ad_set_ids, $fields);

foreach ($adsets as $adset) {
  echo $adset->{AdSetFields::NAME}.PHP_EOL;
}
```

## Key Fields

### Required Fields
- `name`: Ad set name (max 400 characters)
- `campaign_id`: Parent campaign ID
- `targeting`: Targeting specification (countries required)
- `optimization_goal`: What to optimize for
- `billing_event`: When to charge
- Budget: Either `daily_budget` or `lifetime_budget`

### Budget Fields
- `daily_budget`: Daily budget in account currency
- `lifetime_budget`: Total budget in account currency
- `daily_min_spend_target`: Minimum daily spend target
- `daily_spend_cap`: Maximum daily spend
- `lifetime_min_spend_target`: Minimum lifetime spend target
- `lifetime_spend_cap`: Maximum lifetime spend

### Bid Strategy Options
- `LOWEST_COST_WITHOUT_CAP`: Automatic bidding
- `LOWEST_COST_WITH_BID_CAP`: Manual maximum-cost bidding
- `COST_CAP`: Target cost bidding
- `LOWEST_COST_WITH_MIN_ROAS`: Minimum ROAS bidding

### Optimization Goals
- `APP_INSTALLS`: Optimize for app installations
- `LINK_CLICKS`: Optimize for link clicks
- `CONVERSIONS`: Optimize for conversions
- `REACH`: Optimize for unique reach
- `IMPRESSIONS`: Maximize impressions
- `PAGE_LIKES`: Optimize for page likes
- `POST_ENGAGEMENT`: Optimize for post engagement
- `THRUPLAY`: Optimize for video completion
- `VALUE`: Optimize for purchase value

### Billing Events
- `IMPRESSIONS`: Pay per impression
- `CLICKS`: Pay per click
- `LINK_CLICKS`: Pay per link click
- `APP_INSTALLS`: Pay per app install
- `THRUPLAY`: Pay per video completion
- `POST_ENGAGEMENT`: Pay per engagement

## Updating Ad Sets

```bash
curl -X POST \
  -F 'billing_event="IMPRESSIONS"' \
  -F 'optimization_goal="LINK_CLICKS"' \
  -F 'bid_amount=200' \
  -F 'targeting={
       "geo_locations": {
         "countries": [
           "US"
         ]
       },
       "facebook_positions": [
         "feed"
       ]
     }' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/
```

### Budget Update Restrictions
- New budget must be at least 10% greater than amount already spent
- Must meet minimum budget requirements
- Archived ad sets can only update `name` and `campaign_status`

## Deleting Ad Sets

```bash
curl -X DELETE \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<AD_SET_ID>/
```

## Minimum Budget Requirements

### LOWEST_COST_WITHOUT_CAP Strategy

| Billing Event | Minimum Daily Budget |
|---------------|---------------------|
| Impressions | $0.50 |
| Clicks/Likes/Video Views | $2.50 |
| Low-frequency Actions | $40 |

### LOWEST_COST_WITH_BID_CAP Strategy

| Billing Event | Minimum Daily Budget |
|---------------|---------------------|
| Impressions | At least the bid_amount |
| Clicks/Actions | 5x the bid_amount |

**Note**: For certain countries (US, UK, Canada, etc.), minimum values are 2x the standard amounts.

## Error Codes

| Error | Description |
|-------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 2500 | Error parsing graph query |
| 2635 | Deprecated API version |
| 80004 | Too many calls to ad account |

## Outcome-Driven Ads Experiences (ODAX)

New campaign structure with specific objective mappings:

### ODAX Objectives
- **Awareness**: Reach, Brand Awareness
- **Traffic**: Drive traffic to destinations
- **Engagement**: Encourage interactions
- **Leads**: Generate lead information
- **App Promotion**: Drive app installs
- **Sales**: Drive purchases and conversions

Each objective has specific destination types, optimization goals, and promoted objects.

## Examples
Daily budget ad set creation

Lifetime budget ad set creation

Reading ad set with specific fields

Reading multiple ad sets

Updating ad set targeting and bid

Deleting an ad set

---
**Tags:** Facebook Marketing API, Ad Sets, Advertising, Budget Management, Targeting, Optimization, EU Compliance, ODAX
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-campaign
**Processed:** 2025-06-25T15:52:27.531Z