# Marketing API Reference

On This Page

[Marketing API Reference](#marketing-api-reference)

[User](#user)

[Edges](#user_edges)

[Ad Account](#account)

[Edges](#account_edges)

[Ad](#ad)

[Edges](#ad_edges)

[Ad Set](#adset)

[Edges](#adset_edges)

[Ad Campaign](#campaign)

[Edges](#campaign_edges)

[Ad Creative](#creative)

[Edges](#creative_edges)

Marketing API Version

[v23.0](#)

# Marketing API Reference

#### Marketing API Root Nodes

This is a full list of root nodes for the Facebook Marketing API with links to reference docs for each. For background on the API's architecture how to call root nodes and their edges, see [Using the Graph API](/docs/graph-api/using-graph-api).

To access all reference information you will need to be logged in to Facebook.

Node

Description

[`/{AD_ACCOUNT_USER_ID}`](/docs/marketing-api/reference/ad-account-user)

Someone on Facebook who creates ads. Each ad user can have a role on several ad accounts.

[`/act_{AD_ACCOUNT_ID}`](/docs/marketing-api/reference/ad-account)

Represents the business entity managing ads.

[`/{AD_ID}`](https://developers.facebook.com/docs/marketing-api/reference/adgroup)

Contains information for an ad, such as creative elements and measurement information.

[`/{AD_CREATIVE_ID}`](/docs/marketing-api/reference/ad-creative)

Format for your image, carousel, collection, or video ad.

[`/{AD_SET_ID}`](/docs/marketing-api/reference/ad-campaign)

Contains all ads that share the same budget, schedule, bid, and targeting.

[`/{AD_CAMPAIGN_ID}`](/docs/marketing-api/reference/ad-campaign-group)

Defines your ad campaigns' objective. Contains one or more ad set.

[](#)

## User

### Edges

Edge

Description

[`/adaccounts`](https://developers.facebook.com/docs/graph-api/reference/user/adaccounts)

All ad accounts associated with this person

[`/accounts`](https://developers.facebook.com/docs/graph-api/reference/user/accounts/)

All pages and places that someone is an admin of

[`/promotable_events`](https://developers.facebook.com/docs/graph-api/reference/user/promotable_events/)

All promotable events you created or promotable page events that belong to pages you are an admin for

[](#)

## Ad Account

All collections of ad objects in Marketing APIs belong to an [ad account](/docs/reference/ads-api/adaccount).

### Edges

The most popular edges of the Ad Account node. Visit the [Ad Account Edges reference](https://developers.facebook.com/docs/marketing-api/reference/ad-account#edges) for a complete list of all edges.

Edge

Description

[`/adcreatives`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/adcreatives/)

Defines your ad's appearance and content

[`/adimages`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/adimages/)

Library of images to use in ad creatives. Can be uploaded and managed independently

[`/ads`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/ads/)

Data for an ad, such as creative elements and measurement information

[`/adsets`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/)

Contain all ads that share the same budget, schedule, bid, and targeting

[`/advideos`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/)

Library of videos for use in ad creatives. Can be uploaded and managed independently

[`/campaigns`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/campaigns/)

Define your campaigns' objective and contain one or more ad sets

[`/customaudiences`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiences)

The custom audiences owned by/shared with this ad account

[`/insights`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/insights/)

Interface for insights. De-dupes results across child objects, provides sorting, and async reports.

[`/users`](https://developers.facebook.com/docs/marketing-api/reference/ad-account/users/)

List of people assocated with an ad account

[](#)

## Ad

An individual ad associated with an ad set.

### Edges

The most popular edges of the Ad node. Visit the [Ad Edges reference](https://developers.facebook.com/docs/marketing-api/reference/adgroup#edges) for a complete list of all edges.

Edge

Description

[`/adcreatives`](https://developers.facebook.com/docs/marketing-api/reference/adgroup/adcreatives/)

Defines your ad's appearance and content

[`/insights`](https://developers.facebook.com/docs/marketing-api/reference/adgroup/insights/)

Insights on your advertising performance.

[`/leads`](https://developers.facebook.com/docs/marketing-api/reference/adgroup/leads/)

Any leads associated with with a Lead Ad.

[`/previews`](https://developers.facebook.com/docs/marketing-api/reference/adgroup/previews/)

Generate ad previews from an existing ad

[](#)

## Ad Set

An ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data.

### Edges

The most popular edges of the Ad Set node. Visit the [Ad Set Edges reference](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign) for a complete list of all edges.

Edge

Description

[`/activities`](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/activities/)

Log of actions taken on the ad set

[`/adcreatives`](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/adcreatives/)

Defines your ad's content and appearance

[`/ads`](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/ads/)

Data necessary for an ad, such as creative elements and measurement information

[`/insights`](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/insights/)

Insights on your advertising performance.

[](#)

## Ad Campaign

A campaign is the highest level organizational structure within an ad account and should represent a single objective for an advertiser.

### Edges

The most popular edges of the Ad Campaign node. Visit the [Ad Campaign Edges reference](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group) for a complete list of all edges.

Edge

Description

[`/ads`](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/ads)

Data necessary for an ad, such as creative elements and measurement information

[`/adsets`](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group)

Contain all ads that share the same budget, schedule, bid, and targeting.

[`/insights`](https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/insights)

Insights on your advertising performance.

[](#)

## Ad Creative

The format which provides layout and contains content for the ad.

### Edges

The most popular edges of the Ad Creative node. Visit the [Ad Creative Edges reference](#) for a complete list of all edges.

Edge

Description

[`/previews`](https://developers.facebook.com/docs/marketing-api/reference/ad-creative/previews/)

Generate ad previews from the existing ad creative object

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)