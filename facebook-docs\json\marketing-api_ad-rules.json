{"title": "Ad Rules Engine", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_5_dD\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad-rules-engine\">Ad Rules Engine</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#documentation-contents\">Documentation Contents</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Overview</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#guides\">Guides</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_0_qj\"><div class=\"_4cel\"><span data-click-area=\"main\"><div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8\"><div class=\"_4-u3 _588p\"><h1 id=\"ad-rules-engine\">Ad Rules Engine</h1>\n\n<p>A central rule management service that helps you easily, efficiently and intelligently manage ads. Without it, you must query the Marketing API to monitor an ad's performance and manually take actions on certain conditions. Since we can express most conditions as logical expressions, we can automate management two ways: using <a href=\"/docs/marketing-api/ad-rules/scheduled-based-rules\"><em>Schedule-based</em></a> or <a href=\"/docs/marketing-api/ad-rules/trigger-based-rules\"><em>Trigger-based</em></a> Based rules.</p>\n<div class=\"_57yz _57z0 _3-8p\"><div class=\"_57y-\"><p>New to this? Try the rules-based notification quickstart in your <a href=\"/apps/\">App Dashboard, Quickstarts</a>.</p>\n</div></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"documentation-contents\">Documentation Contents</h2>\n<table class=\"uiGrid _51mz _57v1\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr class=\"_51mx\"><td class=\"_51m- vTop _57v2\"><h3 id=\"overview\"><a href=\"/docs/marketing-api/ad-rules/overview\">Overview</a></h3>\n\n<p>Core concepts and usage requirements. Learn about <a href=\"/docs/marketing-api/ad-rules/overview/evaluation-spec\"><strong>Evaluation Spec</strong></a>, <a href=\"/docs/marketing-api/ad-rules/overview/execution-spec\"><strong>Execution Spec</strong></a>, and <a href=\"/docs/marketing-api/ad-rules/overview/change-spec\"><strong>Change Spec</strong></a>.</p>\n</td><td class=\"_51m- vTop _57v2 _51mw\"><h3 id=\"guides\"><a href=\"/docs/marketing-api/ad-rules/guides\">Guides</a></h3>\n\n<p>Use case based guides: <a href=\"/docs/marketing-api/ad-rules/guides/trigger-based-rules\"><strong>Trigger Based Ad Rules</strong></a>, <a href=\"/docs/marketing-api/ad-rules/guides/scheduled-based-rules\"><strong>Schedule Based Rules</strong></a>, <a href=\"/docs/marketing-api/ad-rules/guides/advanced-scheduling\"><strong>Advanced Scheduling</strong></a>, <a href=\"/docs/marketing-api/ad-rules/guides/rebalance-budget\"><strong>Rebalance Budget Ad Rules</strong></a>, <a href=\"/docs/marketing-api/ad-rules/guides/roas-ad-rules\"><strong>ROAS Ad Rules</strong></a>, and <a href=\"/docs/marketing-api/ad-rules/guides/api-calls\"><strong>API Calls</strong></a>.</p>\n</td></tr></tbody></table><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div></span><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p _4_k\"><fb:like href=\"https://developers.facebook.com/docs/marketing-api/ad-rules/\" layout=\"button_count\" share=\"1\"></fb:like><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div id=\"developer_documentation_toolbar\" data-referrer=\"developer_documentation_toolbar\" data-click-area=\"toolbar\"></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '217404712025032');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/ad-rules", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules/overview", "/docs/marketing-api/ad-rules/guides/trigger-based-rules", "/docs/marketing-api/ad-rules/guides/scheduled-based-rules", "/docs/marketing-api/ad-rules/guides/advanced-scheduling", "/docs/marketing-api/ad-rules/guides/evaluation-spec-filters", "/docs/marketing-api/ad-rules/guides/rebalance-budget", "/docs/marketing-api/ad-rules/guides/roas-ad-rules", "/docs/marketing-api/ad-rules/guides/api-calls", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/ad-rules/scheduled-based-rules", "/docs/marketing-api/ad-rules/trigger-based-rules", "/docs/marketing-api/ad-rules/overview/evaluation-spec", "/docs/marketing-api/ad-rules/overview/execution-spec", "/docs/marketing-api/ad-rules/overview/change-spec", "/docs/marketing-api/ad-rules/guides"], "url": "https://developers.facebook.com/docs/marketing-api/ad-rules", "timestamp": "2025-06-25T15:06:34.152Z"}