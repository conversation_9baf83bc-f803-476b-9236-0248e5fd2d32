{"title": "Facebook Marketing API - Ad Account Broad Targeting Categories", "summary": "This endpoint allows you to retrieve broad targeting categories from a Facebook Ad Account. It provides read-only access to targeting categories that can be used for ad targeting, with an option to filter for custom categories only.", "content": "# Ad Account Broad Targeting Categories\n\nThe broad targeting categories endpoint provides access to targeting categories available for a specific Ad Account in the Facebook Marketing API.\n\n## Reading\n\nRetrieve the broad targeting categories from an Ad Account.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/broadtargetingcategories\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `custom_categories_only` | boolean | If `true`, returns only custom categories |\n\n### Response Format\n\nThe response returns a JSON formatted result:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Response Fields\n\n- **data**: A list of BroadTargetingCategories nodes\n- **paging**: Pagination information for navigating through results\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/broadtargetingcategories HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/broadtargetingcategories',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/broadtargetingcategories\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n#### Android SDK\n```java\nnew GraphRequest(\n    AccessToken.getCurrentAccessToken(),\n    \"/{ad-account-id}/broadtargetingcategories\",\n    null,\n    HttpMethod.GET,\n    new GraphRequest.Callback() {\n        public void onCompleted(GraphResponse response) {\n            /* handle the result */\n        }\n    }\n).executeAsync();\n```\n\n#### iOS SDK\n```objc\nFBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]\n                               initWithGraphPath:@\"/{ad-account-id}/broadtargetingcategories\"\n                                      parameters:params\n                                      HTTPMethod:@\"GET\"];\n[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,\n                                      id result,\n                                      NSError *error) {\n    // Handle the result\n}];\n```\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n\n## Limitations\n\n- **Creating**: Not supported on this endpoint\n- **Updating**: Not supported on this endpoint  \n- **Deleting**: Not supported on this endpoint\n\nThis endpoint is read-only and only supports GET operations.", "keyPoints": ["Read-only endpoint for retrieving broad targeting categories from an Ad Account", "Supports filtering with custom_categories_only parameter to return only custom categories", "Returns paginated results with BroadTargetingCategories nodes", "Only GET operations are supported - no create, update, or delete functionality", "Requires proper permissions to access ad account data"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/broadtargetingcategories"], "parameters": ["custom_categories_only (boolean) - Filter for custom categories only", "ad-account-id (path parameter) - The ID of the ad account"], "examples": ["HTTP GET request to retrieve broad targeting categories", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest implementation", "iOS SDK FBSDKGraphRequest implementation"], "tags": ["Facebook Marketing API", "Ad Account", "Targeting", "Broad Targeting Categories", "Graph API", "Read-only endpoint"], "relatedTopics": ["Graph API pagination", "Facebook SDK implementations", "Ad targeting categories", "Marketing API permissions", "BroadTargetingCategories nodes"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "processedAt": "2025-06-25T16:25:34.483Z", "processor": "openrouter-claude-sonnet-4"}