# Facebook Marketing API - Ad Account Impacting Ad Studies

## Summary
This endpoint allows you to read ad studies that impact a specific ad account or any descendant ad objects. It's a read-only endpoint that returns a list of AdStudy nodes with pagination support.

## Key Points
- Read-only endpoint that returns ad studies impacting an ad account
- Returns a list of AdStudy nodes with pagination support
- No parameters required for the request
- Does not support create, update, or delete operations
- Requires proper OAuth 2.0 access token and permissions

## API Endpoints
- `GET /v23.0/{ad-account-id}/impacting_ad_studies`

## Content
# Ad Account Impacting Ad Studies

## Overview

The impacting ad studies endpoint provides access to ad studies that impact a specific ad account or any descendant ad objects. This is a read-only endpoint that does not support creating, updating, or deleting operations.

## Reading

Retrieve the ad studies that impact this ad account or any descendant ad objects.

### HTTP Request

```http
GET /v23.0/{ad-account-id}/impacting_ad_studies HTTP/1.1
Host: graph.facebook.com
```

### Code Examples

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/impacting_ad_studies',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/impacting_ad_studies",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

#### Android SDK
```java
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/impacting_ad_studies",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();
```

#### iOS SDK
```objc
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/impacting_ad_studies"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];
```

### Parameters

This endpoint doesn't have any parameters.

### Response Format

The response returns a JSON formatted result:

```json
{
    "data": [],
    "paging": {}
}
```

#### Fields

- **data**: A list of AdStudy nodes
- **paging**: Pagination information for navigating through results

### Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |

## Unsupported Operations

- **Creating**: You can't perform this operation on this endpoint
- **Updating**: You can't perform this operation on this endpoint
- **Deleting**: You can't perform this operation on this endpoint

## Examples
PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest implementation

iOS SDK FBSDKGraphRequest implementation

---
**Tags:** Facebook Marketing API, Ad Studies, Ad Account, Graph API, Read-only endpoint  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/impacting_ad_studies/  
**Processed:** 2025-06-25T16:14:20.440Z