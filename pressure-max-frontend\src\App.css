/* Import Orbitron font */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* Orbitron font class */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

/* Circuit pattern background */
.circuit-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, #06b6d4 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, #06b6d4 1px, transparent 1px);
  background-size: 50px 50px;
  background-position: 0 0, 25px 25px;
  opacity: 0.1;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Main Content */
.app-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.sections-container {
  display: grid;
  gap: 2rem;
}

.section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
}

.section h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #1e293b;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

/* Button Styles */
button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.submit-btn {
  background: #667eea;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.submit-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.oauth-btn {
  background: #1877f2;
  color: white;
}

.oauth-btn:hover:not(:disabled) {
  background: #166fe5;
}

.logout-btn {
  background: #ef4444;
  color: white;
}

.logout-btn:hover {
  background: #dc2626;
}

.test-btn {
  background: #10b981;
  color: white;
}

.test-btn:hover:not(:disabled) {
  background: #059669;
}

.clear-btn {
  background: #f59e0b;
  color: white;
}

.clear-btn:hover {
  background: #d97706;
}

.create-btn {
  background: #8b5cf6;
  color: white;
}

.create-btn:hover {
  background: #7c3aed;
}

.cancel-btn {
  background: #6b7280;
  color: white;
}

.cancel-btn:hover {
  background: #4b5563;
}

.refresh-btn {
  background: #06b6d4;
  color: white;
}

.refresh-btn:hover {
  background: #0891b2;
}

/* Auth Section */
.auth-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.auth-tabs button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-tabs button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

/* Facebook Login Section */
.facebook-login-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}

.facebook-login-info {
  margin-bottom: 2rem;
}

.facebook-login-info h3 {
  color: #1877f2;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.facebook-login-info ul {
  text-align: left;
  max-width: 400px;
  margin: 1rem auto;
  padding-left: 1rem;
}

.facebook-login-info li {
  margin-bottom: 0.5rem;
  color: #374151;
}

.facebook-login-btn {
  background: #1877f2;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 auto 1rem;
  transition: all 0.2s;
}

.facebook-login-btn:hover:not(:disabled) {
  background: #166fe5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
}

.facebook-login-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.facebook-note {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 1rem;
}

.facebook-note code {
  background: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.auth-status.authenticated {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 1.5rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.token-section {
  margin: 1rem 0;
}

.token-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.toggle-token {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  color: #6b7280;
}

.token-display {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.token-display code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  word-break: break-all;
  color: #1e293b;
}

/* Facebook Section */
.facebook-status {
  margin-bottom: 1.5rem;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-icon.connected {
  color: #10b981;
}

.status-icon.disconnected {
  color: #ef4444;
}

.status-text.connected {
  color: #10b981;
  font-weight: 500;
}

.status-text.disconnected {
  color: #ef4444;
  font-weight: 500;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 0.75rem;
  margin-top: 0.5rem;
}

.oauth-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.facebook-data {
  margin-top: 1.5rem;
}

.data-section {
  margin-bottom: 2rem;
}

.data-section h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #374151;
}

.accounts-list,
.pages-list {
  display: grid;
  gap: 1rem;
}

.account-item,
.page-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
}

.account-info,
.page-info {
  margin-bottom: 0.5rem;
}

.account-info strong,
.page-info strong {
  display: block;
  margin-bottom: 0.25rem;
}

.account-id,
.page-id {
  font-size: 0.875rem;
  color: #6b7280;
  display: block;
}

.account-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  margin-top: 0.25rem;
}

.account-status.active {
  background: #dcfce7;
  color: #166534;
}

.account-status.disabled,
.account-status.closed {
  background: #fee2e2;
  color: #991b1b;
}

.account-status.pending,
.account-status.unsettled {
  background: #fef3c7;
  color: #92400e;
}

.account-status.grace {
  background: #dbeafe;
  color: #1e40af;
}

.account-status.unknown {
  background: #f3f4f6;
  color: #374151;
}

.account-details {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.page-category {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

.no-data {
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

/* Campaign Section */
.account-selector {
  margin-bottom: 1.5rem;
}

.account-selector label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.account-selector select {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

/* Facebook Tabs */
.facebook-tabs {
  display: flex;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.facebook-tabs button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.facebook-tabs button.active {
  color: #667eea;
  border-bottom-color: #667eea;
}

.campaigns-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.campaigns-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.create-campaign-form {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.create-campaign-form h4 {
  margin-bottom: 1rem;
  color: #374151;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.campaigns-list {
  display: grid;
  gap: 1rem;
}

.campaign-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1.5rem;
}

.campaign-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.campaign-header h4 {
  margin: 0;
  color: #1e293b;
}

.campaign-status {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.campaign-status.active {
  background: #dcfce7;
  color: #166534;
}

.campaign-status.paused {
  background: #fef3c7;
  color: #92400e;
}

.campaign-details {
  display: grid;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.no-campaigns,
.no-adsets,
.no-ads {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

/* Ad Sets Section */
.adsets-section h3,
.ads-section h3 {
  margin-bottom: 1rem;
  color: #374151;
}

.adset-item,
.ad-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.adset-header,
.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.adset-header strong,
.ad-header strong {
  color: #374151;
  font-size: 1rem;
}

.adset-details,
.ad-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

/* API Testing Section */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.health-status {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.health-status.healthy {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.health-status.unhealthy {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.health-details p {
  margin-bottom: 0.25rem;
}

.profile-data {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.profile-item:last-child {
  border-bottom: none;
}

.test-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.logs-header h3 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.log-entry {
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.success {
  background: #f0fdf4;
}

.log-entry.error {
  background: #fef2f2;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.log-method {
  background: #374151;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
}

.log-endpoint {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #1e293b;
}

.log-status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
}

.log-status.status-2 {
  background: #dcfce7;
  color: #166534;
}

.log-status.status-4,
.log-status.status-5,
.log-status.status-0 {
  background: #fecaca;
  color: #dc2626;
}

.log-timestamp {
  color: #6b7280;
  margin-left: auto;
}

.log-error {
  color: #dc2626;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.log-response {
  margin-top: 0.5rem;
}

.log-response summary {
  cursor: pointer;
  font-weight: 500;
  color: #374151;
}

.log-response pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 0.5rem;
  overflow-x: auto;
  font-size: 0.875rem;
}

.no-logs {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.auth-required,
.no-accounts,
.no-profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

/* Footer */
.app-footer {
  background: #1e293b;
  color: #94a3b8;
  text-align: center;
  padding: 1.5rem;
  margin-top: 2rem;
}

.app-footer p {
  margin-bottom: 0.5rem;
}

.app-footer code {
  background: #334155;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 1.5rem;
  }
  
  .app-header h1 {
    font-size: 2rem;
  }
  
  .app-main {
    padding: 1rem;
  }
  
  .section {
    padding: 1.5rem;
  }
  
  .campaigns-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .test-buttons {
    flex-direction: column;
  }
  
  .log-header {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .logs-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}

/* Targeting Display Styles */
.targeting-display {
  margin-top: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #f8fafc;
}

.targeting-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #f1f5f9;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  color: #475569;
  transition: background-color 0.2s;
}

.targeting-header:hover {
  background-color: #e2e8f0;
}

.targeting-details {
  padding: 1rem;
}

.targeting-section {
  margin-bottom: 1rem;
}

.targeting-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.9rem;
}

.section-content {
  margin-left: 1.25rem;
}

.targeting-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.targeting-item .label {
  font-weight: 500;
  color: #6b7280;
  min-width: 60px;
}

.targeting-item .value {
  color: #374151;
}

.interests-list,
.behaviors-list,
.platforms-list,
.placements-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.interest-tag,
.behavior-tag,
.platform-tag,
.placement-tag {
  background-color: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.behavior-tag {
  background-color: #fef3c7;
  color: #92400e;
}

.platform-tag {
  background-color: #d1fae5;
  color: #065f46;
}

.placement-tag {
  background-color: #ede9fe;
  color: #5b21b6;
}

.interest-tag.more,
.behavior-tag.more {
  background-color: #f3f4f6;
  color: #6b7280;
  font-style: italic;
}

/* Enhanced Targeting Display Styles */
.targeting-item .value.enabled {
  color: #059669;
  font-weight: 600;
}

.targeting-item .value.disabled {
  color: #dc2626;
  font-weight: 600;
}

/* Geographic Targeting Styles */
.geographic-subsection {
  margin-bottom: 0.75rem;
}

.subsection-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 0.375rem;
}

.subsection-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.geographic-tag {
  background-color: #d1fae5;
  color: #065f46;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid #a7f3d0;
}

/* Detailed Interest Styles */
.interests-detailed {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.interest-item {
  padding: 0.5rem;
  background-color: #fdf4ff;
  border-radius: 6px;
  border-left: 3px solid #a855f7;
}

.interest-name {
  font-weight: 600;
  color: #7c2d12;
  font-size: 0.8rem;
}

.interest-topic {
  margin-left: 0.5rem;
  font-size: 0.75rem;
  color: #a855f7;
  font-style: italic;
}

.interest-path {
  margin-top: 0.25rem;
  font-size: 0.7rem;
  color: #6b7280;
}

/* Detailed Behavior Styles */
.behaviors-detailed {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.behavior-item {
  padding: 0.5rem;
  background-color: #eff6ff;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.behavior-name {
  font-weight: 600;
  color: #1e40af;
  font-size: 0.8rem;
}

.behavior-path {
  margin-top: 0.25rem;
  font-size: 0.7rem;
  color: #6b7280;
}

/* Enhanced Placement Styles */
.placement-subsection {
  margin-bottom: 0.75rem;
}

.placement-tag.platform {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.placement-tag.facebook {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #60a5fa;
}

.placement-tag.instagram {
  background-color: #fce7f3;
  color: #be185d;
  border: 1px solid #f472b6;
}

.placement-tag.messenger {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #34d399;
}

/* Automation Styles */
.automation-item {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background-color: #fef7ed;
  border-radius: 6px;
  border-left: 3px solid #f59e0b;
}

.automation-description {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

/* Creative Display Styles */
.creative-display {
  margin-top: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: #f8fafc;
}

.creative-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: #f1f5f9;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  color: #475569;
  transition: background-color 0.2s;
}

.creative-header:hover {
  background-color: #e2e8f0;
}

.creative-details {
  padding: 1rem;
}

.creative-section {
  margin-bottom: 1rem;
}

.creative-section:last-child {
  margin-bottom: 0;
}

/* Media Assets Styles */
.media-preview {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.media-item {
  flex-shrink: 0;
}

.media-thumbnail {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.media-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #6b7280;
  font-size: 0.75rem;
}

.media-info {
  flex: 1;
}

.media-type {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  text-transform: capitalize;
}

.media-detail {
  font-size: 0.8rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  word-break: break-all;
}

/* Copy Elements Styles */
.copy-item {
  margin-bottom: 0.75rem;
}

.copy-item:last-child {
  margin-bottom: 0;
}

.copy-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.copy-content {
  padding: 0.5rem;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
  line-height: 1.4;
}

.copy-content.primary-text {
  border-left: 3px solid #3b82f6;
  background-color: #eff6ff;
}

.copy-content.headline {
  border-left: 3px solid #059669;
  background-color: #ecfdf5;
  font-weight: 600;
}

.copy-content.description {
  border-left: 3px solid #7c3aed;
  background-color: #f5f3ff;
}

.copy-content.link-description {
  border-left: 3px solid #f59e0b;
  background-color: #fefbf2;
}

.copy-content.title {
  border-left: 3px solid #dc2626;
  background-color: #fef2f2;
}

.copy-content.body {
  border-left: 3px solid #6b7280;
  background-color: #f9fafb;
}

/* Call to Action Styles */
.cta-button {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
  text-align: center;
  min-width: 120px;
}

/* Destination Styles */
.destination-item {
  margin-bottom: 0.5rem;
}

.destination-item:last-child {
  margin-bottom: 0;
}

.destination-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.destination-url {
  word-break: break-all;
}

.destination-link {
  color: #3b82f6;
  text-decoration: none;
  font-size: 0.875rem;
}

.destination-link:hover {
  text-decoration: underline;
}

.destination-display {
  font-size: 0.875rem;
  color: #374151;
  font-family: monospace;
  background-color: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

/* Creative Info Styles */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.info-item {
  display: flex;
  gap: 0.5rem;
}

.info-label {
  font-weight: 500;
  color: #6b7280;
  min-width: 40px;
}

.info-value {
  color: #374151;
  word-break: break-all;
}

/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.hero-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 120px);
}

.hero-content {
  max-width: 1200px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-text h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  opacity: 0.9;
  font-weight: 500;
}

.hero-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.8;
  line-height: 1.6;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  font-weight: 500;
}

.auth-container {
  display: flex;
  justify-content: center;
}

.auth-card {
  background: white;
  color: #1e293b;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 450px;
}

.auth-card h2 {
  font-size: 1.75rem;
  margin-bottom: 1.5rem;
  text-align: center;
  color: #1e293b;
  border-bottom: none;
  padding-bottom: 0;
}

.landing-footer {
  background: #1e293b;
  color: white;
  padding: 1.5rem;
  text-align: center;
}

.landing-footer p {
  margin: 0.25rem 0;
}

.landing-footer code {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Dashboard Styles */
.dashboard {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dashboard-header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.dashboard-title h1 {
  font-size: 2rem;
  margin-bottom: 0.25rem;
  font-weight: 700;
}

.dashboard-title p {
  font-size: 1rem;
  opacity: 0.9;
}

.dashboard-user-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  font-weight: 500;
}

.dashboard-actions {
  display: flex;
  gap: 0.75rem;
}

.nav-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.dashboard-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.dashboard-footer {
  background: #1e293b;
  color: white;
  padding: 1.5rem;
  text-align: center;
}

.dashboard-footer p {
  margin: 0.25rem 0;
}

.dashboard-footer code {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Tailwind-like Utility Classes */

/* Background utilities */
.bg-black {
  background-color: #000000;
}

.bg-gradient-to-r {
  background: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-cyan-400 {
  --tw-gradient-from: #22d3ee;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(34, 211, 238, 0));
}

.to-cyan-600 {
  --tw-gradient-to: #0891b2;
}

.from-cyan-500 {
  --tw-gradient-from: #06b6d4;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(6, 182, 212, 0));
}

/* Text utilities */
.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

.text-gray-300 {
  color: #d1d5db;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-cyan-400 {
  color: #22d3ee;
}

.text-green-400 {
  color: #4ade80;
}

.text-transparent {
  color: transparent;
}

.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
}

/* Layout utilities */
.min-h-screen {
  min-height: 100vh;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.relative {
  position: relative;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.z-10 {
  z-index: 10;
}

.-z-10 {
  z-index: -10;
}

/* Flexbox utilities */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-col {
  flex-direction: column;
}

/* Grid utilities */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

/* Spacing utilities */
.gap-4 {
  gap: 1rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-12 {
  gap: 3rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-3 > * + * {
  margin-left: 0.75rem;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

/* Padding utilities */
.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.pt-20 {
  padding-top: 5rem;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

/* Margin utilities */
.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* Width and height utilities */
.w-full {
  width: 100%;
}

.w-3 {
  width: 0.75rem;
}

.w-10 {
  width: 2.5rem;
}

.w-16 {
  width: 4rem;
}

.h-3 {
  height: 0.75rem;
}

.h-10 {
  height: 2.5rem;
}

.h-16 {
  height: 4rem;
}

.h-48 {
  height: 12rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-4xl {
  max-width: 56rem;
}

/* Border utilities */
.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-cyan-500 {
  border-color: #06b6d4;
}

.border-cyan-500\/20 {
  border-color: rgba(6, 182, 212, 0.2);
}

.border-cyan-500\/30 {
  border-color: rgba(6, 182, 212, 0.3);
}

.border-cyan-500\/50 {
  border-color: rgba(6, 182, 212, 0.5);
}

/* Border radius utilities */
.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

/* Typography utilities */
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.leading-tight {
  line-height: 1.25;
}

.leading-relaxed {
  line-height: 1.625;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.text-center {
  text-align: center;
}

/* Background color utilities */
.bg-cyan-500\/5 {
  background-color: rgba(6, 182, 212, 0.05);
}

.bg-cyan-500\/10 {
  background-color: rgba(6, 182, 212, 0.1);
}

.bg-black\/80 {
  background-color: rgba(0, 0, 0, 0.8);
}

/* Effects utilities */
.opacity-5 {
  opacity: 0.05;
}

.opacity-100 {
  opacity: 1;
}

.pointer-events-none {
  pointer-events: none;
}

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Transform utilities */
.transform {
  transform: var(--tw-transform);
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: scale(1.05);
}

.rotate-2 {
  --tw-rotate: 2deg;
  transform: rotate(2deg);
}

/* Transition utilities */
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

/* Hover utilities */
.hover\:text-cyan-400:hover {
  color: #22d3ee;
}

.hover\:text-black:hover {
  color: #000000;
}

.hover\:bg-cyan-500:hover {
  background-color: #06b6d4;
}

.hover\:border-cyan-500:hover {
  border-color: #06b6d4;
}

.hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover\:shadow-cyan-500\/25:hover {
  box-shadow: 0 20px 25px -5px rgba(6, 182, 212, 0.25), 0 10px 10px -5px rgba(6, 182, 212, 0.1);
}

.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Animation utilities */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Display utilities */
.inline-flex {
  display: inline-flex;
}

.hidden {
  display: none;
}

/* Responsive utilities */
@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 640px) {
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:flex-row {
    flex-direction: row;
  }
}

/* Additional utility classes for the complete landing page */

/* More background utilities */
.bg-gray-900 {
  background-color: #111827;
}

.from-gray-900 {
  --tw-gradient-from: #111827;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(17, 24, 39, 0));
}

.from-cyan-900\/20 {
  --tw-gradient-from: rgba(22, 78, 99, 0.2);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 78, 99, 0));
}

.to-black {
  --tw-gradient-to: #000000;
}

/* More text colors */
.text-gray-500 {
  color: #6b7280;
}

.text-green-500 {
  color: #10b981;
}

.fill-current {
  fill: currentColor;
}

/* More width utilities */
.w-2 {
  width: 0.5rem;
}

.h-2 {
  height: 0.5rem;
}

/* More spacing utilities */
.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-12 {
  margin-top: 3rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-8 {
  padding-top: 2rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

/* Position utilities */
.top-4 {
  top: 1rem;
}

.right-4 {
  right: 1rem;
}

.-top-4 {
  top: -1rem;
}

.left-1\/2 {
  left: 50%;
}

/* Transform utilities */
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translateX(-50%);
}

.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: scale(1.05);
}

/* More text sizes */
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

/* Object utilities */
.object-cover {
  object-fit: cover;
}

/* Filter utilities */
.filter {
  filter: var(--tw-filter);
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: grayscale(100%);
}

.hover\:grayscale-0:hover {
  --tw-grayscale: grayscale(0%);
  filter: grayscale(0%);
}

/* More duration utilities */
.duration-500 {
  transition-duration: 500ms;
}

/* Group utilities */
.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: scale(1.1);
}

/* More responsive utilities */
@media (min-width: 768px) {
  .md\:flex-row {
    flex-direction: row;
  }

  .md\:mt-0 {
    margin-top: 0;
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Block utility */
.block {
  display: block;
}

/* More spacing between elements */
.space-x-6 > * + * {
  margin-left: 1.5rem;
}

/* Items baseline */
.items-baseline {
  align-items: baseline;
}

/* More margin utilities */
.ml-2 {
  margin-left: 0.5rem;
}

/* Text right */
.text-right {
  text-align: right;
}

/* Button styling fixes */
button {
  border: none;
  cursor: pointer;
  font-family: inherit;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* Demo button specific styling */
.demo-button {
  background: transparent !important;
  border: 2px solid #06b6d4 !important;
  color: #22d3ee !important;
  padding: 1rem 2rem !important;
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  font-family: 'Orbitron', monospace !important;
  letter-spacing: 0.05em !important;
  text-transform: none !important;
  border-radius: 0 !important;
  transition: all 0.3s ease !important;
}

.demo-button:hover {
  background: #06b6d4 !important;
  color: #000000 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3) !important;
}

/* CTA button styling */
.cta-button {
  background: linear-gradient(to right, #06b6d4, #0891b2) !important;
  color: #000000 !important;
  border: none !important;
  padding: 1rem 2rem !important;
  font-size: 1.125rem !important;
  font-weight: 700 !important;
  font-family: 'Orbitron', monospace !important;
  letter-spacing: 0.05em !important;
  text-decoration: none !important;
  border-radius: 0 !important;
  transition: all 0.3s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.cta-button:hover {
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: 0 10px 25px rgba(6, 182, 212, 0.4) !important;
  color: #000000 !important;
}

/* Footer link styling */
.footer-link {
  color: #9ca3af !important;
  text-decoration: none !important;
  font-size: 0.875rem !important;
  transition: color 0.3s ease !important;
  border: none !important;
  background: none !important;
  padding: 0 !important;
  cursor: pointer !important;
}

.footer-link:hover {
  color: #22d3ee !important;
  text-decoration: none !important;
}

/* Navigation link styling */
.nav-link {
  color: #d1d5db !important;
  text-decoration: none !important;
  transition: color 0.3s ease !important;
  border: none !important;
  background: none !important;
  padding: 0 !important;
  cursor: pointer !important;
  font-size: inherit !important;
}

.nav-link:hover {
  color: #22d3ee !important;
  text-decoration: none !important;
}

/* Header login button */
.header-login-btn {
  border: 1px solid #06b6d4 !important;
  color: #22d3ee !important;
  background: transparent !important;
  padding: 0.5rem 1.5rem !important;
  font-weight: 700 !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  border-radius: 0 !important;
}

.header-login-btn:hover {
  background: #06b6d4 !important;
  color: #000000 !important;
  text-decoration: none !important;
}

/* Remove default link styling */
a {
  text-decoration: none;
  color: inherit;
}

a:hover {
  text-decoration: none;
}

/* Ensure buttons don't have default browser styling */
button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.5);
}

/* Fix any remaining button issues */
.btn-reset {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
}

/* Enhanced circuit pattern background */
.circuit-bg {
  background-image:
    radial-gradient(circle at 25% 25%, #06b6d4 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, #06b6d4 2px, transparent 2px),
    linear-gradient(45deg, transparent 40%, rgba(6, 182, 212, 0.1) 50%, transparent 60%),
    linear-gradient(135deg, transparent 40%, rgba(6, 182, 212, 0.05) 50%, transparent 60%);
  background-size: 50px 50px, 50px 50px, 100px 100px, 150px 150px;
  background-position: 0 0, 25px 25px, 0 0, 50px 50px;
  animation: circuitFlow 20s linear infinite;
}

@keyframes circuitFlow {
  0% { background-position: 0 0, 25px 25px, 0 0, 50px 50px; }
  100% { background-position: 50px 50px, 75px 75px, 100px 100px, 200px 200px; }
}

/* Advanced animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(6, 182, 212, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.8s ease-out forwards;
}

.animate-count-up {
  animation: countUp 1s ease-out forwards;
}

/* Staggered animation delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }

/* Glass morphism effects */
.glass {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(6, 182, 212, 0.2);
}

.glass-strong {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(6, 182, 212, 0.3);
}

/* Enhanced card effects */
.card-hover {
  transition: all 0.3s ease;
  transform-style: preserve-3d;
}

.card-hover:hover {
  transform: translateY(-8px) rotateX(5deg);
  box-shadow:
    0 20px 40px rgba(6, 182, 212, 0.2),
    0 0 0 1px rgba(6, 182, 212, 0.1);
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #06b6d4, #22d3ee, #0891b2);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Enhanced button effects */
.btn-3d {
  position: relative;
  transform-style: preserve-3d;
  transition: all 0.3s ease;
}

.btn-3d:hover {
  transform: translateY(-2px) rotateX(10deg);
}

.btn-3d::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(34, 211, 238, 0.3));
  transform: translateZ(-1px) translateY(4px);
  filter: blur(4px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-3d:hover::before {
  opacity: 1;
}

/* Particle effect background */
.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #06b6d4;
  border-radius: 50%;
  animation: particleFloat 6s linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}

/* Enhanced typography */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.text-glow {
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

/* Progress bars */
.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(6, 182, 212, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #06b6d4, #22d3ee);
  border-radius: 2px;
  animation: progressFill 2s ease-out forwards;
}

@keyframes progressFill {
  from { width: 0%; }
  to { width: var(--progress-width, 100%); }
}

/* Enhanced shadows */
.shadow-cyan {
  box-shadow: 0 4px 14px 0 rgba(6, 182, 212, 0.25);
}

.shadow-cyan-lg {
  box-shadow: 0 10px 25px -3px rgba(6, 182, 212, 0.3);
}

.shadow-cyan-xl {
  box-shadow: 0 20px 40px -12px rgba(6, 182, 212, 0.4);
}

/* Scroll reveal animations */
.reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced grid layouts */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}
