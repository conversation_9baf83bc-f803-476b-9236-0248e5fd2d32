{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { LogOut, User, Home } from 'lucide-react';\nimport AuthSection from '../components/AuthSection';\nimport FacebookConnectionSection from '../components/FacebookConnectionSection';\nimport CampaignSection from '../components/CampaignSection';\nimport LeadFormsSection from '../components/LeadFormsSection';\nimport ApiTestingSection from '../components/ApiTestingSection';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = async () => {\n    await logout();\n    navigate('/landing');\n  };\n  const handleGoToLanding = () => {\n    navigate('/landing');\n  };\n  if (!isAuthenticated) {\n    navigate('/landing');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dashboard-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\uD83D\\uDE80 Pressure Max API Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Comprehensive testing interface for the Pressure Max API backend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-user-section\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Welcome, \", user.firstName || user.email]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"dashboard-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleGoToLanding,\n              className: \"nav-btn\",\n              children: [/*#__PURE__*/_jsxDEV(Home, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this), \"Landing\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleLogout,\n              className: \"logout-btn\",\n              children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"dashboard-main\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sections-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(AuthSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(FacebookConnectionSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(CampaignSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(LeadFormsSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"section\",\n          children: /*#__PURE__*/_jsxDEV(ApiTestingSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"dashboard-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Pressure Max API Testing Interface - Built with React\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Backend API: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"http://localhost:3000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"B5aHiu91piX0w1CsOFDPXF/GbhU=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useAuth", "useNavigate", "LogOut", "User", "Home", "AuthSection", "FacebookConnectionSection", "CampaignSection", "LeadFormsSection", "ApiTestingSection", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "handleGoToLanding", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "firstName", "email", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { LogOut, User, Home } from 'lucide-react';\nimport AuthSection from '../components/AuthSection';\nimport FacebookConnectionSection from '../components/FacebookConnectionSection';\nimport CampaignSection from '../components/CampaignSection';\nimport LeadFormsSection from '../components/LeadFormsSection';\nimport ApiTestingSection from '../components/ApiTestingSection';\n\nconst Dashboard = () => {\n  const { user, isAuthenticated, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/landing');\n  };\n\n  const handleGoToLanding = () => {\n    navigate('/landing');\n  };\n\n  if (!isAuthenticated) {\n    navigate('/landing');\n    return null;\n  }\n\n  return (\n    <div className=\"dashboard\">\n      {/* Dashboard Header */}\n      <header className=\"dashboard-header\">\n        <div className=\"dashboard-header-content\">\n          <div className=\"dashboard-title\">\n            <h1>🚀 Pressure Max API Dashboard</h1>\n            <p>Comprehensive testing interface for the Pressure Max API backend</p>\n          </div>\n          \n          <div className=\"dashboard-user-section\">\n            {user && (\n              <div className=\"user-info\">\n                <User size={16} />\n                <span>Welcome, {user.firstName || user.email}</span>\n              </div>\n            )}\n            \n            <div className=\"dashboard-actions\">\n              <button onClick={handleGoToLanding} className=\"nav-btn\">\n                <Home size={16} />\n                Landing\n              </button>\n              <button onClick={handleLogout} className=\"logout-btn\">\n                <LogOut size={16} />\n                Logout\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Dashboard Main Content */}\n      <main className=\"dashboard-main\">\n        <div className=\"sections-container\">\n          <section className=\"section\">\n            <AuthSection />\n          </section>\n\n          <section className=\"section\">\n            <FacebookConnectionSection />\n          </section>\n\n          <section className=\"section\">\n            <CampaignSection />\n          </section>\n\n          <section className=\"section\">\n            <LeadFormsSection />\n          </section>\n\n          <section className=\"section\">\n            <ApiTestingSection />\n          </section>\n        </div>\n      </main>\n\n      {/* Dashboard Footer */}\n      <footer className=\"dashboard-footer\">\n        <p>Pressure Max API Testing Interface - Built with React</p>\n        <p>Backend API: <code>http://localhost:3000</code></p>\n      </footer>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,IAAI,EAAEC,IAAI,QAAQ,cAAc;AACjD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,yBAAyB,MAAM,yCAAyC;AAC/E,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,iBAAiB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGhB,OAAO,CAAC,CAAC;EACnD,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMF,MAAM,CAAC,CAAC;IACdC,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9BF,QAAQ,CAAC,UAAU,CAAC;EACtB,CAAC;EAED,IAAI,CAACF,eAAe,EAAE;IACpBE,QAAQ,CAAC,UAAU,CAAC;IACpB,OAAO,IAAI;EACb;EAEA,oBACEN,OAAA;IAAKS,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBV,OAAA;MAAQS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAClCV,OAAA;QAAKS,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCV,OAAA;UAAKS,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BV,OAAA;YAAAU,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCd,OAAA;YAAAU,QAAA,EAAG;UAAgE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,wBAAwB;UAAAC,QAAA,GACpCP,IAAI,iBACHH,OAAA;YAAKS,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBV,OAAA,CAACR,IAAI;cAACuB,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClBd,OAAA;cAAAU,QAAA,GAAM,WAAS,EAACP,IAAI,CAACa,SAAS,IAAIb,IAAI,CAACc,KAAK;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN,eAEDd,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAQkB,OAAO,EAAEV,iBAAkB;cAACC,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACrDV,OAAA,CAACP,IAAI;gBAACsB,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTd,OAAA;cAAQkB,OAAO,EAAEX,YAAa;cAACE,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACnDV,OAAA,CAACT,MAAM;gBAACwB,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTd,OAAA;MAAMS,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC9BV,OAAA;QAAKS,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjCV,OAAA;UAASS,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BV,OAAA,CAACN,WAAW;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEVd,OAAA;UAASS,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BV,OAAA,CAACL,yBAAyB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEVd,OAAA;UAASS,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BV,OAAA,CAACJ,eAAe;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEVd,OAAA;UAASS,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BV,OAAA,CAACH,gBAAgB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEVd,OAAA;UAASS,SAAS,EAAC,SAAS;UAAAC,QAAA,eAC1BV,OAAA,CAACF,iBAAiB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPd,OAAA;MAAQS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAClCV,OAAA;QAAAU,QAAA,EAAG;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5Dd,OAAA;QAAAU,QAAA,GAAG,eAAa,eAAAV,OAAA;UAAAU,QAAA,EAAM;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACZ,EAAA,CAlFID,SAAS;EAAA,QAC6BZ,OAAO,EAChCC,WAAW;AAAA;AAAA6B,EAAA,GAFxBlB,SAAS;AAoFf,eAAeA,SAAS;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}