{"title": "Getting Started with Facebook Marketing API", "summary": "This guide covers the essential prerequisites and setup steps for using the Facebook Marketing API, including ad account requirements, developer account setup, app creation, and authentication processes. It provides a comprehensive overview of what developers need to begin building with the Marketing API.", "content": "# Getting Started with Facebook Marketing API\n\nTo effectively utilize the Marketing API, users must follow some key steps to set up their environment and gain access to the API's features. This section outlines the prerequisites necessary for getting started.\n\n## Ad Account Requirements\n\nTo manage your ads through the Marketing API, you must have an active ad account. This account is crucial not only for running campaigns but also for managing billing settings and setting spending limits. An ad account allows you to track your advertising expenses, monitor performance, and optimize your campaigns effectively.\n\n### Finding Your Ad Account Number\n\nLocating your ad account number can be done through the Meta Ads Manager:\n\n1. **Log into Facebook:** Start by logging into your Facebook account that is associated with your business.\n2. **Access Ads Manager:** Ads Manager can be found in the drop-down menu in the upper right corner of your Facebook homepage or business page.\n3. **Locate your ad account:** In Ads Manager, click on the ad account Settings from the menu on the bottom left of the screen.\n4. **View ad account information:** In the Settings screen, you will find your ad account number listed along with other details such as your billing information and spending limits.\n\n## Meta Developer Account\n\nA Meta Developer account is required to access the Marketing API. This involves registering as a Meta Developer to gain access to development tools and resources.\n\n## Create an App\n\nYou need to create an app in the App Dashboard. This involves selecting appropriate app types and understanding different use cases for your Marketing API integration.\n\n## Authorization and Authentication\n\nProper authorization and authentication are essential for accessing the Marketing API:\n\n- **Authorization:** Verify users and apps that will access the Marketing API and grant them appropriate permissions\n- **Authentication:** Manage access tokens including getting, extending, and renewing them for continued API access\n\n## Next Steps\n\nOnce setup is complete, you can proceed with:\n\n1. Creating ad campaigns\n2. Managing ad campaigns\n3. Optimizing ad campaigns", "keyPoints": ["An active ad account is required to use the Marketing API for campaign management and billing", "Meta Developer account registration is necessary to access API development tools", "App creation in the App Dashboard is required before making API calls", "Proper authorization and authentication setup is essential for secure API access", "Ad account number can be found in Meta Ads Manager settings"], "apiEndpoints": [], "parameters": ["ad account number", "access tokens", "app permissions", "billing information", "spending limits"], "examples": [], "tags": ["Facebook Marketing API", "<PERSON><PERSON>", "Ad Account", "Authentication", "Authorization", "Setup", "Getting Started"], "relatedTopics": ["Meta Ads Manager", "App Dashboard", "Access Tokens", "Ad Campaign Creation", "Campaign Management", "Ad Optimization", "Developer Registration", "App Creation"], "difficulty": "beginner", "contentType": "guide", "originalUrl": "https://developers.facebook.com/docs/marketing-api/get-started", "processedAt": "2025-06-25T16:17:01.425Z", "processor": "openrouter-claude-sonnet-4"}