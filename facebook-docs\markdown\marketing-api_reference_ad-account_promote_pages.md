# Ad Account Promote Pages

Graph API Version

[v23.0](#)

# Ad Account Promote Pages

## Reading

Get all the promoted Pages for an Ad Account.

### Requirements

*   The `pages_show_list` permission - to get access to your app user's Pages
    
*   The `pages_manage_ads` permission - to get access to create and manage ads for your app user's Pages
    

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fpromote_pages&version=v23.0)

```
`GET /v23.0/{ad-account-id}/promote_pages HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of [Page](/docs/graph-api/reference/page/) nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

200

Permissions error

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

190

Invalid OAuth 2.0 Access Token

## Creating

You can't perform this operation on this endpoint.

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.