{"title": "Facebook Marketing API - Ad Creative Reference", "summary": "Complete reference for the Ad Creative object in Facebook Marketing API, which provides layout and contains content for ads. Covers creation, reading, updating, and deletion of ad creatives with detailed field specifications and examples.", "content": "# Facebook Marketing API - Ad Creative Reference\n\n## Overview\n\nThe Ad Creative object provides layout and contains content for Facebook ads. It defines the visual and textual elements that make up an advertisement across Facebook's family of apps and services.\n\n### Important Deprecation Notice\n\nThe `instagram_actor_id` field for `act_<AD_ACCOUNT_ID>/adcreatives` has been deprecated for v22.0 and will be deprecated for all versions January 20, 2026. Migrate to use the `instagram_user_id` field instead.\n\n## Special Requirements\n\n### Political Ads\n\nAdvertisers running ads about social issues, elections, and politics must:\n- Specify `special_ad_categories` while creating an ad campaign\n- Set `authorization_category` to flag at the ad creative level\n- Use `POLITICAL_WITH_DIGITALLY_CREATED_MEDIA` for digitally created/altered media (effective January 9, 2024)\n\n## API Operations\n\n### Reading Ad Creatives\n\n```bash\ncurl -G \\\n  -d 'fields=name,object_story_id' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>\n```\n\n#### Read Thumbnail\n\n```bash\ncurl -G \\\n  -d 'thumbnail_width=150' \\\n  -d 'thumbnail_height=120' \\\n  -d 'fields=thumbnail_url' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>\n```\n\n### Creating Ad Creatives\n\n#### Basic Link Ad\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"link_data\": { \n      \"image_hash\": \"<IMAGE_HASH>\", \n      \"link\": \"<URL>\", \n      \"message\": \"try it out\" \n    }, \n    \"page_id\": \"<PAGE_ID>\" \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Link Ad with Call to Action\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"link_data\": { \n      \"call_to_action\": {\"type\":\"SIGN_UP\",\"value\":{\"link\":\"<URL>\"}}, \n      \"link\": \"<URL>\", \n      \"message\": \"try it out\" \n    }, \n    \"page_id\": \"<PAGE_ID>\" \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Carousel Ad\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"link_data\": { \n      \"child_attachments\": [ \n        { \n          \"description\": \"$8.99\", \n          \"image_hash\": \"<IMAGE_HASH>\", \n          \"link\": \"https://www.link.com/product1\", \n          \"name\": \"Product 1\", \n          \"video_id\": \"<VIDEO_ID>\" \n        }, \n        { \n          \"description\": \"$9.99\", \n          \"image_hash\": \"<IMAGE_HASH>\", \n          \"link\": \"https://www.link.com/product2\", \n          \"name\": \"Product 2\", \n          \"video_id\": \"<VIDEO_ID>\" \n        } \n      ], \n      \"link\": \"<URL>\" \n    }, \n    \"page_id\": \"<PAGE_ID>\" \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Political Ad Creative\n\n```bash\ncurl \\\n  -F 'authorization_category=POLITICAL' \\\n  -F 'object_story_spec={...}' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Video Page Like Ad\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"page_id\": \"<PAGE_ID>\", \n    \"video_data\": { \n      \"call_to_action\": {\"type\":\"LIKE_PAGE\",\"value\":{\"page\":\"<PAGE_ID>\"}}, \n      \"image_url\": \"<THUMBNAIL_URL>\", \n      \"video_id\": \"<VIDEO_ID>\" \n    } \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Updating Ad Creatives\n\n```bash\ncurl \\\n  -F 'name=New creative name' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>\n```\n\n### Deleting Ad Creatives\n\n```bash\ncurl -X DELETE \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>/\n```\n\n## Content Limits and Guidelines\n\n### Field Limits\n\n- **Maximum ad title length**: 25 characters (recommended)\n- **Minimum ad title length**: 1 character\n- **Maximum ad body length**: 90 characters (recommended)\n- **Minimum ad body length**: 1 character\n- **Maximum URL length**: 1000 characters\n- **Maximum word length**: 30 characters (recommended)\n- **Creative name limit**: 100 characters\n\n### Content Rules\n\n- Cannot start with punctuation: `\\ / ! . ? - * ( ) , ; :`\n- Cannot have consecutive punctuation except three full-stops `...`\n- Words no longer than 30 characters\n- Only three 1-character words allowed\n- Special character restrictions vary by ad type\n\n### Prohibited Characters\n\n- IPA Symbols (except specific exceptions)\n- Standalone diacritical marks\n- Superscript and subscript characters (except ™ and ℠)\n- Special characters: `^~_={}[]|<>`\n\n## Key Fields\n\n### Core Fields\n\n- `id`: Unique numeric string identifier\n- `account_id`: Ad account ID\n- `name`: Creative name in library\n- `status`: ACTIVE, IN_PROCESS, WITH_ISSUES, DELETED\n- `object_story_spec`: Specification for creating unpublished page posts\n- `object_story_id`: ID of existing page post to use\n\n### Authorization Fields\n\n- `authorization_category`: POLITICAL, POLITICAL_WITH_DIGITALLY_CREATED_MEDIA\n- `effective_authorization_category`: System-determined political classification\n\n### Media Fields\n\n- `image_hash`: Image from ad account library\n- `image_url`: External image URL\n- `video_id`: Facebook video object ID\n- `thumbnail_url`: Thumbnail image URL\n\n### Targeting Fields\n\n- `call_to_action_type`: Button type and behavior\n- `object_type`: Type of Facebook object being advertised\n- `link_url`: Destination URL for link ads\n\n## Limits and Restrictions\n\n- Maximum 50,000 ad creatives returned (pagination unavailable beyond this)\n- Image size limit: 8 MB for page post images\n- Placement restrictions apply based on creative type\n\n## Related Resources\n\n- App Ads\n- Video & Carousel Ads\n- Advantage+ Catalog Ads\n- Instagram Ads\n- WhatsApp Click-to-Message Ads\n- Lead Ads", "keyPoints": ["Ad Creative objects define the visual and textual content for Facebook ads across all placements", "The instagram_actor_id field is deprecated - migrate to instagram_user_id before January 2026", "Political ads require special authorization_category flags and compliance with advertising policies", "Content has strict character limits and formatting rules that vary by ad type and placement", "Inline page post creation allows creating unpublished posts directly within the creative specification"], "apiEndpoints": ["GET /v23.0/<CREATIVE_ID>", "POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives", "POST /v23.0/<CREATIVE_ID>", "DELETE /v23.0/<CREATIVE_ID>", "GET /v23.0/<CREATIVE_ID>/previews"], "parameters": ["object_story_spec", "object_story_id", "authorization_category", "name", "image_hash", "image_url", "video_id", "call_to_action_type", "link_url", "thumbnail_width", "thumbnail_height", "instagram_user_id", "page_id", "asset_feed_spec", "platform_customizations"], "examples": ["Basic link ad creation with image and message", "Carousel ad with multiple products and descriptions", "Political ad creative with authorization category", "Video page like ad with call to action", "Reading creative with thumbnail specifications", "Partnership ads with branded content sponsor", "URL tags for tracking parameters"], "tags": ["facebook-marketing-api", "ad-creative", "advertising", "creative-management", "political-ads", "carousel-ads", "video-ads", "instagram-ads", "branded-content"], "relatedTopics": ["Ad Campaign", "Ad Set", "Ad Account", "Page Posts", "Image Library", "Dynamic Ads", "App Ads", "Lead Ads", "WhatsApp Ads", "Instagram Media", "Branded Content", "Political Advertising Policies"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-creative", "processedAt": "2025-06-25T15:41:48.868Z", "processor": "openrouter-claude-sonnet-4"}