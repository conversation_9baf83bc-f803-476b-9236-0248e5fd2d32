# Facebook Marketing API - Ad Account Targeting Search

## Summary
The Ad Account Targeting Search endpoint provides a unified search interface to retrieve targeting descriptors with query parameters. This read-only endpoint allows advertisers to search for various targeting options like interests, demographics, locations, and behaviors for their ad campaigns.

## Key Points
- Unified search endpoint for retrieving targeting descriptors with query parameters
- Supports extensive filtering options including demographics, interests, behaviors, and placements
- Read-only endpoint - no create, update, or delete operations allowed
- Subject to rate limiting and requires proper OAuth 2.0 authentication
- Returns paginated results with AdAccountTargetingUnified nodes

## API Endpoints
- `GET /v23.0/{ad-account-id}/targetingsearch`

## Parameters
- q (required)
- allow_only_fat_head_interests
- app_store
- limit_type
- objective
- regulated_categories

## Content
# Ad Account Targeting Search

## Overview

The Ad Account Targeting Search endpoint is a unified search interface that allows you to retrieve targeting descriptors using query parameters. This endpoint is essential for building targeting options in Facebook ad campaigns.

## Endpoint

```
GET /v23.0/{ad-account-id}/targetingsearch
```

## Parameters

### Required Parameters

- **`q`** (string): Search query term

### Optional Parameters

- **`allow_only_fat_head_interests`** (boolean): Restrict results to only pre-vetted interests
- **`app_store`** (enum): Specify the app store for app install campaigns. Options include:
  - amazon_app_store, google_play, itunes, itunes_ipad
  - fb_canvas, fb_gameroom, windows_store, fb_android_store
  - windows_10_store, roku_channel_store, instant_game
  - oculus_app_store, galaxy_store, and others
- **`limit_type`** (enum): Restrict the type of targeting audience to retrieve. Extensive options including:
  - Demographics: genders, age_min, age_max, countries, cities
  - Interests: interests, behaviors, life_events
  - Connections: custom_audiences, excluded_custom_audiences
  - Placements: facebook_positions, instagram_positions, messenger_positions
- **`objective`** (enum): Campaign objective such as:
  - APP_INSTALLS, BRAND_AWARENESS, CONVERSIONS
  - LEAD_GENERATION, LINK_CLICKS, PAGE_LIKES
  - OUTCOME_SALES, OUTCOME_TRAFFIC, VIDEO_VIEWS
- **`regulated_categories`** (array): Specify regulated campaign categories:
  - EMPLOYMENT, HOUSING, CREDIT
  - ISSUES_ELECTIONS_POLITICS
  - ONLINE_GAMBLING_AND_GAMING
  - FINANCIAL_PRODUCTS_SERVICES

## Response Format

The endpoint returns a JSON object with:

```json
{
  "data": [],
  "paging": {}
}
```

- **`data`**: Array of AdAccountTargetingUnified nodes
- **`paging`**: Pagination information for large result sets

## Example Request

```http
GET /v23.0/{ad-account-id}/targetingsearch?q=technology HTTP/1.1
Host: graph.facebook.com
```

## Error Codes

- **100**: Invalid parameter
- **80004**: Too many API calls - rate limiting applied
- **190**: Invalid OAuth 2.0 Access Token
- **200**: Permissions error
- **368**: Action deemed abusive or disallowed

## Limitations

- **Read-only endpoint**: Creating, updating, and deleting operations are not supported
- **Rate limiting**: Subject to Facebook's ads management rate limits
- **Permissions**: Requires appropriate access tokens and permissions

## Examples
GET /v23.0/{ad-account-id}/targetingsearch HTTP/1.1

---
**Tags:** Facebook Marketing API, targeting, search, ad account, audience, demographics, interests
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/targetingsearch/
**Processed:** 2025-06-25T15:40:31.669Z