# Facebook Marketing API - Ad Account Instagram Accounts Reference

## Summary
This reference page documents the Ad Account Instagram Accounts endpoint, which allows you to retrieve Instagram accounts associated with a specific Ad Account. The endpoint only supports read operations and returns a list of IGUser nodes with pagination and summary information.

## Key Points
- Retrieves Instagram accounts associated with a specific Ad Account
- Read-only endpoint - no create, update, or delete operations supported
- Returns IGUser nodes with pagination and summary information
- Subject to rate limiting for both Instagram accounts and ad accounts
- Requires proper OAuth 2.0 access token and permissions

## API Endpoints
- `GET /v23.0/{ad-account-id}/instagram_accounts`

## Parameters
- ad-account-id (path parameter)
- summary (optional query parameter for aggregated data)

## Content
# Ad Account Instagram Accounts

## Overview

The Ad Account Instagram Accounts endpoint allows you to retrieve Instagram accounts that are associated with a specific Ad Account in the Facebook Marketing API.

## Reading

Retrieve Instagram accounts associated with this Ad Account.

### Endpoint

```
GET /v23.0/{ad-account-id}/instagram_accounts
```

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/instagram_accounts HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/instagram_accounts',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/instagram_accounts",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

#### Android SDK
```java
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/instagram_accounts",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();
```

#### iOS SDK
```objc
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/instagram_accounts"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];
```

### Parameters

This endpoint doesn't have any parameters.

### Response Format

Reading from this edge will return a JSON formatted result:

```json
{
    "data": [],
    "paging": {},
    "summary": {}
}
```

#### Response Fields

- **data**: A list of IGUser nodes representing Instagram accounts
- **paging**: Pagination information for navigating through results
- **summary**: Aggregated information about the edge, such as counts

#### Summary Fields

| Field | Type | Description |
|-------|------|-------------|
| `total_count` | int32 | Total number of objects on this edge |

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 190 | Invalid OAuth 2.0 Access Token |
| 100 | Invalid parameter |
| 80002 | Too many calls to this Instagram account. Rate limiting applied. |
| 80004 | Too many calls to this ad-account. Rate limiting applied. |

## Supported Operations

- **Creating**: Not supported
- **Updating**: Not supported  
- **Deleting**: Not supported

This endpoint is read-only and only supports retrieving existing Instagram account associations.

## Examples
HTTP GET request example

PHP SDK implementation

JavaScript SDK usage

Android SDK integration

iOS SDK implementation

---
**Tags:** Facebook Marketing API, Instagram, Ad Account, Graph API, Social Media Marketing, API Reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/instagram_accounts/  
**Processed:** 2025-06-25T16:28:11.842Z