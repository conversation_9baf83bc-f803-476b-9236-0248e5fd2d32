<div class="_1dyy" id="u_0_5_bI"><div class="_33zv _3e1u"><div class="_33zw" tabindex="0" role="button">On This Page<div><i class="img sp_zD_MvjcHflF sx_a3c10c" alt="" data-visualcompletion="css-img"></i><i class="hidden_elem img sp_zD_MvjcHflF sx_6874ff" alt="" data-visualcompletion="css-img"></i></div></div><div class="_5-24 hidden_elem"><a href="#best-practices">Best Practices</a></div><div class="_5-24 hidden_elem"><a href="#ad-changes-triggering-ad-reviews">Ad Changes Triggering Ad Reviews</a></div><div class="_5-24 hidden_elem"><a href="#paging">Pagination</a></div><div class="_5-24 hidden_elem"><a href="#user-information">User Information</a></div><div class="_5-24 hidden_elem"><a href="#suggested-bids">Suggested Bids</a></div><div class="_5-24 hidden_elem"><a href="#batch-requests">Batch Requests</a></div><div class="_5-24 hidden_elem"><a href="#check-data-changes-using-etags">Check Data Changes using ETags</a></div><div class="_5-24 hidden_elem"><a href="#object-archive-and-delete-status">Object Archive and Delete Status</a></div><div class="_5-24 hidden_elem"><a href="#viewing-errors">Viewing Errors</a></div><div class="_5-24 hidden_elem"><a href="#facebook-marketing-developer-community-group">Facebook Marketing Developer Community Group</a></div><div class="_5-24 hidden_elem"><a href="#testing">Testing</a></div><div class="_5-24 hidden_elem"><a href="#criteria">Basic Criteria</a></div><div class="_5-24 hidden_elem"><a href="#policies">Policies</a></div></div></div><div id="documentation_body_pagelet" data-referrer="documentation_body_pagelet"><div class="_34yh" id="u_0_0_93"><div class="_4cel"><span data-click-area="main"><div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8"><div class="_4-u3 _588p"><h1 id="best-practices">Best Practices</h1>
</div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="ad-changes-triggering-ad-reviews">Ad Changes Triggering Ad Reviews</h2>

<p>If you make any changes to the following scenarios, your ad will be triggered for review:</p>

<ul>
<li>Any changes to your creative (image, text, link, video, and so on)</li>
<li>Any changes to targeting </li>
<li>Any changes of optimization goals and billing events may also trigger review</li>
</ul>

<p><strong>Note</strong>: Changes to bid amount, budget, and ad set schedule will not have any effect on the review status.</p>

<p>Additionally, if an ad enters Ad Review with the run status of "Paused", then it will remain Paused upon exiting Ad Review. Otherwise, the ad will be considered Active and ready to deliver.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="paging">Pagination</h2>

<p>For paging response data, see the <a href="/docs/graph-api/results">Graph API Pagination</a>.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="user-information">User Information</h2>

<p>You should store user IDs, session keys, and the ads account ID so it is easy to programmatically access them and keep them together. This is important because any calls made with an account ID belonging to one user and the session key for another user will fail with a permissions error. Any storages of user data must be done in compliance with <a href="/terms">Facebook Platform Terms</a> and <a href="/devpolicy">Developer Policies</a>.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="suggested-bids">Suggested Bids</h2>

<p>Run frequent reports on your campaigns, as suggested bids change dynamically in response to bidding by competitors using similar targeting. Bid suggestions get updated within a few hours, depending upon the bidding of competitors.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="batch-requests">Batch Requests</h2>

<p>Make multiple requests to the API with a single call, see:</p>

<ul>
<li><a href="/docs/graph-api/making-multiple-requests">Multiple Requests</a></li>
<li><a href="/docs/reference/ads-api/batch-requests">Batch Requests</a></li>
</ul>

<p>You can also query for multiple objects by ID as follows:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">https</span><span class="pun">:</span><span class="com">//graph.facebook.com/&lt;API_VERSION&gt;?ids=[id1,id2]</span></pre><p>To query for a specific field:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">https</span><span class="pun">:</span><span class="com">//graph.facebook.com/&lt;API_VERSION&gt;?ids=[id1,id2]&amp;amp;fields=field1,field2</span></pre><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="check-data-changes-using-etags">Check Data Changes using ETags</h2>

<p>Quickly check if the response to a request has changed since you last made it, see:</p>

<ul>
<li><a href="/blog/post/627/">ETags blog</a></li>
<li><a href="/docs/reference/ads-api/etags-reference/">ETags Reference</a></li>
</ul>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="object-archive-and-delete-status">Object Archive and Delete Status</h2>

<p>Ad objects have two types of delete states: archived and deleted. You can query both archived and deleted objects with the object id. However, we do not return deleted objects if you request it from another object's edge.</p>

<p>You can have up to 5000 archived objects at any time. You should move ad objects from archived states to deleted states if you no longer need to retrieve them via edges. To learn how states work and for sample calls see <a href="/docs/ads-api/best-practices/storing_adobjects">Storing Ad Objects</a>.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="viewing-errors">Viewing Errors</h2>

<p>People make mistakes and try to create ads that are not accepted, <a href="/docs/reference/ads-api/error-reference">Error Codes</a> provide reasons an API call failed. You should share some form of the error to users so they can fix their ads.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="facebook-marketing-developer-community-group">Facebook Marketing Developer Community Group</h2>

<p>Join <a href="https://www.facebook.com/groups/pmdcommunity/">Facebook Marketing Developer Community</a> group on Facebook for news and update on for Marketing API. We post items from the <a href="/ads/blog/">Marketing API blog</a> to the group.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="testing">Testing</h2>

<p>Sandbox mode is a testing environment to read and write Marketing API calls without delivering actual ads. See <a href="/ads/blog/post/2016/10/19/sandbox-ad-accounts/">Sandbox Mode for Developers</a></p>

<p>Try API calls with <a href="/tools/explorer">Graph API Explorer</a>. You can try any API call you would like to make to the Marketing API, see <a href="/blog/post/517/">blog post</a>. Select your app in <code>App</code>, and grant your app  <code>ads_management</code> or <code>ads_read</code> permission in <code>extended permissions</code> when you create an access token. Use <code>ads_read</code> if you only need Ads Insights API access for reporting. Use <code>ads_management</code> to read and update ads in an account.</p>

<p>For <a href="/docs/reference/ads-api/access">development and basic access</a>, configure a list of ad accounts your app is able to make API calls for, see <a href="/docs/reference/ads-api/access#standard_accounts">account list</a>.</p>

<p>You can use sandbox mode to demonstrate your app for app review. However in sandbox mode you cannot create ads or ad creative. Therefore you should use hard coded ad IDs and ad creative IDs to demonstrate your use of our API for app review.</p>

<h3 id="criteria">Basic Criteria</h3>

<ul>
<li><p>Demonstrate value beyond Facebook's core solutions, such as <a href="https://www.facebook.com/ads/manager/">Facebook Ads Manager</a>.</p></li>
<li><p>Focus on business objectives, such as increase in sales. Facebook business objectives can be found <a href="/docs/reference/ads-api/guides/chapter-2-objective-connections">here</a>.</p></li>
</ul>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="policies">Policies</h2>

<p>Understand the API policies; Facebook has the right to audit your activity anytime:</p>

<ul>
<li><strong><a href="https://developers.facebook.com/terms">Platform Terms</a></strong></li>
<li><strong><a href="https://developers.facebook.com/devpolicy">Developer Policies</a></strong></li>
<li><strong><a href="https://www.facebook.com/page_guidelines.php#promotionsguidelines">Promotion Policies</a></strong>  </li>
<li><strong><a href="https://www.facebook.com/full_data_use_policy">Data Use Policy</a></strong>  </li>
<li><strong><a href="https://www.facebook.com/legal/terms">Statement of Rights and Responsibilities</a></strong>  </li>
<li><strong><a href="https://www.facebook.com/ad_guidelines.php">Advertising Guidelines</a></strong></li>
</ul>

<p>Be ready to adapt quickly to changes. Most changes are <a href="/docs/reference/ads-api/versions">versioned</a> and change windows are 90 days, ongoing.</p>

<p>In <a href="https://www.facebook.com/legal/terms">Statement of Rights and Responsibilities</a>, you are financially and operationally responsible for your application, its contents, and your use of the Meta Platform and the Ads API. You should manage your app's stability and potential bugs.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div></div></span><div class="_4-u2 _57mb _1u44 _4-u8 _3la3"><div class="_4-u3 _588p _4_k"><fb:like href="https://developers.facebook.com/docs/marketing-api/best-practices/" layout="button_count" share="1"></fb:like><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div id="developer_documentation_toolbar" data-referrer="developer_documentation_toolbar" data-click-area="toolbar"></div><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '675141479195042');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '574561515946252');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '1754628768090156');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '217404712025032');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1" /></noscript></div></div></div>