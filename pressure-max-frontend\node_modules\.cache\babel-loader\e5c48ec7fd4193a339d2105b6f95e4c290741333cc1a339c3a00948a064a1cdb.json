{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { FacebookProvider } from './contexts/FacebookContext';\nimport Landing from './pages/Landing';\nimport Dashboard from './pages/Dashboard';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(FacebookProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: [/*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/landing\",\n              element: /*#__PURE__*/_jsxDEV(Landing, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 17,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 18,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/landing\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n            position: \"top-right\",\n            toastOptions: {\n              duration: 4000,\n              style: {\n                background: '#363636',\n                color: '#fff'\n              },\n              success: {\n                duration: 3000,\n                iconTheme: {\n                  primary: '#4ade80',\n                  secondary: '#fff'\n                }\n              },\n              error: {\n                duration: 5000,\n                iconTheme: {\n                  primary: '#ef4444',\n                  secondary: '#fff'\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "<PERSON>th<PERSON><PERSON><PERSON>", "FacebookProvider", "Landing", "Dashboard", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { FacebookProvider } from './contexts/FacebookContext';\nimport Landing from './pages/Landing';\nimport Dashboard from './pages/Dashboard';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <FacebookProvider>\n        <Router>\n          <div className=\"App\">\n            <Routes>\n              <Route path=\"/landing\" element={<Landing />} />\n              <Route path=\"/dashboard\" element={<Dashboard />} />\n              <Route path=\"/\" element={<Navigate to=\"/landing\" replace />} />\n            </Routes>\n\n            <Toaster\n              position=\"top-right\"\n              toastOptions={{\n                duration: 4000,\n                style: {\n                  background: '#363636',\n                  color: '#fff',\n                },\n                success: {\n                  duration: 3000,\n                  iconTheme: {\n                    primary: '#4ade80',\n                    secondary: '#fff',\n                  },\n                },\n                error: {\n                  duration: 5000,\n                  iconTheme: {\n                    primary: '#ef4444',\n                    secondary: '#fff',\n                  },\n                },\n              }}\n            />\n          </div>\n        </Router>\n      </FacebookProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACL,YAAY;IAAAO,QAAA,eACXF,OAAA,CAACJ,gBAAgB;MAAAM,QAAA,eACfF,OAAA,CAACV,MAAM;QAAAY,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBF,OAAA,CAACT,MAAM;YAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEL,OAAA,CAACH,OAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CT,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEL,OAAA,CAACF,SAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDT,OAAA,CAACR,KAAK;cAACY,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEL,OAAA,CAACP,QAAQ;gBAACiB,EAAE,EAAC,UAAU;gBAACC,OAAO;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eAETT,OAAA,CAACN,OAAO;YACNkB,QAAQ,EAAC,WAAW;YACpBC,YAAY,EAAE;cACZC,QAAQ,EAAE,IAAI;cACdC,KAAK,EAAE;gBACLC,UAAU,EAAE,SAAS;gBACrBC,KAAK,EAAE;cACT,CAAC;cACDC,OAAO,EAAE;gBACPJ,QAAQ,EAAE,IAAI;gBACdK,SAAS,EAAE;kBACTC,OAAO,EAAE,SAAS;kBAClBC,SAAS,EAAE;gBACb;cACF,CAAC;cACDC,KAAK,EAAE;gBACLR,QAAQ,EAAE,IAAI;gBACdK,SAAS,EAAE;kBACTC,OAAO,EAAE,SAAS;kBAClBC,SAAS,EAAE;gBACb;cACF;YACF;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEnB;AAACc,EAAA,GAzCQtB,GAAG;AA2CZ,eAAeA,GAAG;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}