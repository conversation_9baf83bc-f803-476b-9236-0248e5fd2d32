<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_Wf"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_+I"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_XT"></div></span></div></div>

<h1 id="overview">Ad Account Targetingsearch</h1>

<h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>Unified search endpoint to get targeting descriptors with query</p>
</div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_Pf"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_hR">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_8E">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_SM">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_2R">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_ym">iOS SDK</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=%7Bad-account-id%7D%2Ftargetingsearch&amp;version=v23.0" target="_blank">Graph API Explorer</a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_b_C+" style=""><code><span class="pln">GET </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/{</span><span class="pln">ad</span><span class="pun">-</span><span class="pln">account</span><span class="pun">-</span><span class="pln">id</span><span class="pun">}/</span><span class="pln">targetingsearch HTTP</span><span class="pun">/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_g_U7"><tr class="row_0"><td><div class="_yc"><span><code>allow_only_fat_head_interests</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>Allow only pre vetted interests</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>app_store</code></span></div><div class="_yb">enum {amazon_app_store, google_play, itunes, itunes_ipad, fb_canvas, fb_gameroom, windows_store, fb_android_store, windows_10_store, roku_channel_store, instant_game, oculus_app_store, galaxy_store, neon_android_store, digital_turbine_store, apk_pure, apk_monk, apk_mirror, xiaomi, oppo, vivo, bemobi_mobile_store, aptoide_a1_store, uptodown, does_not_exist, none}</div></td><td><p class="_yd"></p><div><div><p>The app store for which this ad is being promoted. This is typically only for app install campaign objectives.</p>
</div></div><p></p></td></tr><tr class="row_2"><td><div class="_yc"><span><code>limit_type</code></span></div><div class="_yb">enum {adgroup_id, genders, age_min, age_max, age_range, country_groups, countries, country, cities, city_keys, radius, regions, region_keys, zips, interests, location_cluster_ids, keywords, education_schools, education_majors, work_positions, work_employers, relationship_statuses, interested_in, locales, user_adclusters, excluded_user_adclusters, conjunctive_user_adclusters, custom_audiences, excluded_custom_audiences, cafe_ca_expansion_targeting_signal, cafe_ca_contraction_targeting_signal, expanded_implicit_custom_audiences, tafe_ca_mitigation_strategy, college_years, education_statuses, connections, excluded_connections, friends_of_connections, user_event, dynamic_audience_ids, excluded_dynamic_audience_ids, rtb_flag, site_category, geo_locations, excluded_geo_locations, timezones, place_page_set_ids, location_expansion, page_types, publisher_platforms, effective_publisher_platforms, facebook_positions, effective_facebook_positions, instagram_positions, effective_instagram_positions, messenger_positions, effective_messenger_positions, device_platforms, effective_device_platforms, audience_network_positions, effective_audience_network_positions, whatsapp_positions, effective_whatsapp_positions, oculus_positions, effective_oculus_positions, threads_positions, effective_threads_positions, excluded_publisher_categories, excluded_publisher_list_ids, user_device, mobile_device_model, excluded_user_device, excluded_mobile_device_model, user_os, wireless_carrier, marketing_message_channels, subscriber_universe, user_age_unknown, family_statuses, industries, life_events, political_views, politics, behaviors, income, net_worth, home_type, home_ownership, home_value, ethnic_affinity, generation, household_composition, moms, office_type, household_income, targeting_optimization, direct_install_devices, targeting_automation, targeting_relaxation_types, engagement_specs, excluded_engagement_specs, product_audience_specs, excluded_product_audience_specs, exclusions, flexible_spec, exclude_reached_since, exclude_previous_days, app_install_state, install_state_application, fb_deal_id, interest_defaults_source, alternate_auto_targeting_option, contextual_targeting_categories, topic, format, trending, gatekeepers, follow_profiles, follow_profiles_negative, location_categories, user_page_threads, user_page_threads_excluded, is_whatsapp_destination_ad, marketplace_product_categories, instream_video_sponsorship_placements, prospecting_audience, brand_safety_content_severity_levels, catalog_based_targeting, brand_safety_content_filter_levels, excluded_brand_safety_content_types, id, is_instagram_destination_ad, instagram_hashtags, instream_video_skippable_excluded}</div></td><td><p class="_yd"></p><div><div><p>Limit the type of audience to retrieve</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>objective</code></span></div><div class="_yb">enum{APP_INSTALLS, BRAND_AWARENESS, CONVERSIONS, EVENT_RESPONSES, LEAD_GENERATION, LINK_CLICKS, LOCAL_AWARENESS, MESSAGES, OFFER_CLAIMS, OUTCOME_APP_PROMOTION, OUTCOME_AWARENESS, OUTCOME_ENGAGEMENT, OUTCOME_LEADS, OUTCOME_SALES, OUTCOME_TRAFFIC, PAGE_LIKES, POST_ENGAGEMENT, PRODUCT_CATALOG_SALES, REACH, STORE_VISITS, VIDEO_VIEWS}</div></td><td><p class="_yd"></p><div><div><p>The objective of the ad campaign.</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>q</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Search query</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>regulated_categories</code></span></div><div class="_yb">array&lt;enum {NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES}&gt;</div></td><td><p class="_yd"></p><div><div><p>The regulated categories of the campaign.</p>
</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id="fields">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class="_3hux"><p>{
    "<code>data</code>": [],
    "<code>paging</code>": {}
}</p>
</pre><div class="_3-8o"><h4><code>data</code></h4>A list of AdAccountTargetingUnified nodes.</div><div class="_3-8o"><h4><code>paging</code></h4>For more details about pagination, see the <a href="/docs/graph-api/using-graph-api/#paging">Graph API guide</a>.</div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr></tbody></table></div></div></div>

<h2 id="Creating">Creating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Deleting">Deleting</h2><div class="_844_">You can't perform this operation on this endpoint.</div>