# Facebook Marketing API Image Crops

## Summary
Image Crops allow you to specify aspect ratios for images in different ad placements on Facebook. You can provide custom cropping specifications or use defaults, with support for various placement types through crop keys and pixel coordinates.

## Key Points
- Image crops allow custom aspect ratios for different ad placements using crop keys and pixel coordinates
- Must be used for all placements where an ad may appear to ensure consistency
- Only supported for ad creatives with image_file or image_hash, not Page posts
- Coordinates must be within image bounds and maintain the correct aspect ratio
- Facebook Stories do not support image crops functionality

## API Endpoints
- `https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adimages`

## Parameters
- filename
- access_token
- crop key
- x coordinate
- y coordinate
- image_file
- image_hash

## Content
# Image Crops

Provide aspect ratios for images in different ad placements. Facebook crops your image according to your specifications given or if you provide no cropping we display it using defaults. See [Ad Image](/docs/reference/ads-api/adimage/).

## Usage Example

First, upload an image to use in ad creative:

```bash
curl \
  -F 'filename=@<IMAGE_PATH>' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adimages
```

Then, provide ad creative by referencing the image hash returned in the previous call along with cropping.

## Crop Structure

Crops contains key-value pairs, where:
- **Key**: A `crop key` that describes an aspect ratio
- **Value**: The pixel dimensions of the crop as `(x, y)` coordinates

For all supported keys, see [Ads Image Crops Reference](/docs/marketing-api/reference/ads-image-crops).

## Coordinate System

Provide value as `(x, y)` coordinates for the upper-left and bottom-right corners of the cropping rectangle:
- Image origin `(0, 0)` is at the upper-left corner
- Point `(width - 1, height - 1)` is at the bottom-right corner
- The aspect ratio of the specified box must be as close as possible to the aspect ratio in the `crop key`

## Specification Requirements

When you use this feature, **you should use it for all placements where an ad may appear**. For example, if you provide it for the Right Hand Column, and you also want to use the ad in Newsfeed, you'll need to provide cropping for the Newsfeed placement.

## Limitations

Image crops are only supported for ad creatives with `image_file` or `image_hash`. `Page posts` are not supported. Values must adhere to these constraints:

- Points specified by `(x, y)` must lie within the image. A rectangle that extends beyond the bounds of the image is invalid
- The rectangle must be the same aspect ratio as specified by the crop key
- Coordinates cannot contain negative values
- Facebook Stories do not support image crops

## Example

```json
{"100x100": [[330, 67], [1080, 817]]}
```

## Examples
curl -F 'filename=@<IMAGE_PATH>' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adimages

{"100x100": [[330, 67], [1080, 817]]}

---
**Tags:** Facebook Marketing API, Image Crops, Ad Creative, Image Upload, Ad Placements, Aspect Ratios
**Difficulty:** intermediate
**Content Type:** guide
**Source:** https://developers.facebook.com/docs/marketing-api/image-crops
**Processed:** 2025-06-25T15:46:42.884Z