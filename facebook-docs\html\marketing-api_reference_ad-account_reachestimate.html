<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_iY"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_2D"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_hT"></div></span></div></div>

<h1 id="overview">Ad Account Reachestimate</h1>

<h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>Used to get the audience size estimation based on a targeting specification using this ad account. This endpoint returns a range-based size in the form of two fields: <code>users_lower_bound</code> and <code>users_upper_bound</code>.</p>
</div><div><span><h3 id="limitations">Limitations</h3>

<p>Reach estimates for custom audiences may not be available for certain businesses.</p>
</span></div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_Xp"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_43">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_e7">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_YV">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_3C">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_qN">iOS SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_b_jb">cURL</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Freachestimate%3Ftargeting_spec%3D%257B%2522geo_locations%2522%253A%257B%2522countries%2522%253A%255B%2522US%2522%255D%257D%252C%2522age_min%2522%253A20%252C%2522age_max%2522%253A40%257D&amp;version=v23.0" target="_blank">Graph API Explorer</a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_c_4n" style=""><code><span class="pln">GET </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/</span><span class="pln">act_</span><span class="pun">&lt;</span><span class="pln">AD_ACCOUNT_ID</span><span class="pun">&gt;</span><span class="str">/reachestimate?targeting_spec=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22age_min%22%3A20%2C%22age_max%22%3A40%7D HTTP/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_i_1u"><tr class="row_0"><td><div class="_yc"><span><code>object_store_url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Used in mobile app campaign. The url of the app in the app store.</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>targeting_spec</code></span></div><div class="_yb">Targeting object</div></td><td><p class="_yd"></p><div><div><p>The targeting structure for reach estimate. <code>countries</code> is required. See <a href="/docs/marketing-api/targeting-specs">targeting</a>.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><div><h3 id="fields">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class="_3hux"><p>{
    "<code>data</code>": [],
    "<code>paging</code>": {}
}</p>
</pre><div class="_3-8o"><h4><code>data</code></h4>A single <a target="_blank" href="/docs/graph-api/reference/ad-account-reach-estimate/">AdAccountReachEstimate</a> node.</div><div class="_3-8o"><h4><code>paging</code></h4>For more details about pagination, see the <a href="/docs/graph-api/using-graph-api/#paging">Graph API guide</a>.</div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>613</td><td>Calls to this api have exceeded the rate limit.</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2641</td><td>Your ad includes or excludes locations that are currently restricted</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr></tbody></table></div></div></div>

<h2 id="Creating">Creating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Deleting">Deleting</h2><div class="_844_">You can't perform this operation on this endpoint.</div>