{"title": "Ad Previews", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Marketing API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_Qo\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_JL\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_rd\"></div></span></div></div>\n\n<h1 id=\"ad-previews\">Ad Previews</h1>\n\n<p>Preview existing ads and generate previews of ads you want to create. Generated previews are based on your ad creative. For ad preview <strong>provide a user access token</strong>, not a Page access token. For example, preview existing ad creative:</p>\n<div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_5_5k\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_6_5l\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_Ez\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_AE\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_n0\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_Z8\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_b_mI\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_c_hm\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_d_aN\"><span label=\"Copy Code\" value=\"curl -X GET \\\n  -d 'ad_format=&quot;DESKTOP_FEED_STANDARD&quot;' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>/previews\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=\"DESKTOP_FEED_STANDARD\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">CREATIVE_ID</span><span class=\"pun\">&gt;/</span><span class=\"pln\">previews</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=%3CCREATIVE_ID%3E%2Fpreviews?ad_format=DESKTOP_FEED_STANDARD&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_t_ht\"><input type=\"hidden\" name=\"jazoest\" value=\"2986\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZyaU\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/generatepreview\"><input type=\"hidden\" autocomplete=\"off\" name=\"id\" value=\"***************\"><input type=\"hidden\" autocomplete=\"off\" name=\"name\" value=\"ADCREATIVE_GET_ADPREVIEWS\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><br><p>Or preview using creative spec for a domain ad for an external website:</p>\n<div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_u_zl\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_v_qw\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_w_hw\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_x_9G\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_y_l4\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_z_82\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_10_d1\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_11_OF\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_12_QF\"><span label=\"Copy Code\" value=\"curl -X GET \\\n  -d 'creative=&quot;<CREATIVE_SPEC>&quot;' \\\n  -d 'ad_format=&quot;<AD_FORMAT>&quot;' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'creative=\"&lt;CREATIVE_SPEC&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=\"&lt;AD_FORMAT&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;/</span><span class=\"pln\">generatepreviews</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%3CCREATIVE_SPEC%3E%26ad_format=%3CAD_FORMAT%3E&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_1i_w4\"><input type=\"hidden\" name=\"jazoest\" value=\"2986\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZyaU\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/generatepreview\"><input type=\"hidden\" autocomplete=\"off\" name=\"id\" value=\"***************\"><input type=\"hidden\" autocomplete=\"off\" name=\"name\" value=\"ADACCOUNT_GET_PREVIEWS\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div>\n\n<h2 id=\"html\">Generating Previews</h2>\n\n<p>There are a few ways to generate a preview, using:</p>\n\n<ul>\n<li><a href=\"/docs/reference/ads-api/adgroup/\">Ad</a> ID</li>\n<li><a href=\"/docs/reference/ads-api/adcreative/\">Ad Creative</a> ID</li>\n<li>Supplying a creative spec</li>\n</ul>\n\n<p>To use an ad ID for an existing ad, use ad's <a href=\"/docs/marketing-api/reference/adgroup/previews\"><code>previews</code></a>.</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;API_VERSION&gt;/&lt;AD_ID&gt;/previews</span></pre><p>To use an existing ad creative's ID, use the ad creative's <a href=\"/docs/marketing-api/reference/ad-creative/previews\"><code>previews</code></a>.</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;API_VERSION&gt;/&lt;AD_CREATIVE_ID&gt;/previews</span></pre><p>To use an <a href=\"/docs/reference/ads-api/adcreative/\">ad creative spec</a>, you have two endpoint options:</p>\n\n<ul>\n<li><a href=\"/docs/marketing-api/reference/ad-account/generatepreviews/\"><code>/act_&lt;AD_ACCOUNT_ID&gt;/generatepreviews</code></a>, or</li>\n<li><a href=\"/docs/graph-api/reference/generatepreviews/\"><code>/generatepreviews</code></a> — The creative passed in this call should not be associated with a specific ad account.</li>\n</ul>\n\n<p>For Advantage+ catalog ads pass the entire object_story_spec into the /generatepreviews endpoint, and also use <code>product_item_ids</code> described in <a href=\"/docs/marketing-api/dynamic-product-ads/ads-management#dynamicpreview\">Advantage+ Catalog Ads, Preview</a>.</p>\n\n<p>Previews from an ad account are only visible to people who have a role on the ad account. Previews generated using <code>generatepreviews</code> edge are visible to anyone.</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/API_VERSION&gt;/act_&lt;AD_ACCOUNT_ID&gt;/generatepreviews</span><span class=\"pln\">\nhttps</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/API_VERSION&gt;/generatepreviews</span></pre><p>Any of the four endpoints above will return an <a href=\"/docs/marketing-api/reference/ad-preview/\">ad preview object</a></p>\n\n\n<h2 id=\"examples\">Examples</h2>\n\n<p>Create a preview using <code>object_story_spec</code>:</p>\n<div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_1j_dP\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_1k_w6\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1l_hU\">Python Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1m_xW\">Java Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1n_zw\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_1o_6t\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdAccount</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdCreative</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdCreativeFields</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdPreviewFields</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdCreativeLinkDataFields</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdCreativeObjectStorySpecFields</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdCreativeLinkData</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdCreativeObjectStorySpec</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Values\\AdPreviewAdFormatValues</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Values\\AdCreativeCallToActionTypeValues</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$link_data </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdCreativeLinkData</span><span class=\"pun\">();</span><span class=\"pln\">\n$link_data</span><span class=\"pun\">-&gt;</span><span class=\"pln\">setData</span><span class=\"pun\">(</span><span class=\"pln\">array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeLinkDataFields</span><span class=\"pun\">::</span><span class=\"pln\">LINK </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'&lt;URL&gt;'</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeLinkDataFields</span><span class=\"pun\">::</span><span class=\"pln\">MESSAGE </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'Message'</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeLinkDataFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'Name'</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeLinkDataFields</span><span class=\"pun\">::</span><span class=\"pln\">DESCRIPTION </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'Description'</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeLinkDataFields</span><span class=\"pun\">::</span><span class=\"pln\">CALL_TO_ACTION </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> array</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'type'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"typ\">AdCreativeCallToActionTypeValues</span><span class=\"pun\">::</span><span class=\"pln\">SIGN_UP</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">'value'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> array</span><span class=\"pun\">(</span><span class=\"pln\">\n      </span><span class=\"str\">'link'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'&lt;URL&gt;'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">),</span><span class=\"pln\">\n  </span><span class=\"pun\">),</span><span class=\"pln\">\n</span><span class=\"pun\">));</span><span class=\"pln\">\n\n$story </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdCreativeObjectStorySpec</span><span class=\"pun\">();</span><span class=\"pln\">\n$story</span><span class=\"pun\">-&gt;</span><span class=\"pln\">setData</span><span class=\"pun\">(</span><span class=\"pln\">array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeObjectStorySpecFields</span><span class=\"pun\">::</span><span class=\"pln\">PAGE_ID </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"pun\">&lt;</span><span class=\"pln\">PAGE_ID</span><span class=\"pun\">&gt;,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeObjectStorySpecFields</span><span class=\"pun\">::</span><span class=\"pln\">LINK_DATA </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> $link_data</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">));</span><span class=\"pln\">\n\n$creative </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdCreative</span><span class=\"pun\">();</span><span class=\"pln\">\n$creative</span><span class=\"pun\">-&gt;</span><span class=\"pln\">setData</span><span class=\"pun\">(</span><span class=\"pln\">array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdCreativeFields</span><span class=\"pun\">::</span><span class=\"pln\">OBJECT_STORY_SPEC </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> $story</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">));</span><span class=\"pln\">\n\n$account </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$account</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGeneratePreviews</span><span class=\"pun\">(</span><span class=\"pln\">array</span><span class=\"pun\">(),</span><span class=\"pln\"> array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdPreviewFields</span><span class=\"pun\">::</span><span class=\"pln\">CREATIVE </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> $creative</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdPreviewFields</span><span class=\"pun\">::</span><span class=\"pln\">AD_FORMAT </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"typ\">AdPreviewAdFormatValues</span><span class=\"pun\">::</span><span class=\"pln\">DESKTOP_FEED_STANDARD</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">));</span></code></pre></div></div><p>Create an multi-product ad preview using <code>object_story_id</code>. To get <code>object_story_id</code>, first create a <a href=\"/docs/reference/ads-api/multi-product-ads\">Multi-Product Ad</a>.</p>\n<div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_1s_uz\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1t_iW\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1u_nN\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1v_o1\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1w_Fm\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1x_r4\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_1y_cd\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_1z_WT\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_20_IN\"><span label=\"Copy Code\" value=\"curl -X GET \\\n  -d 'creative={\n       &quot;object_story_id&quot;: &quot;<PAGE_ID>_<POST_ID>&quot;\n     }' \\\n  -d 'ad_format=&quot;DESKTOP_FEED_STANDARD&quot;' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'creative={\n       \"object_story_id\": \"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;\"\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=\"DESKTOP_FEED_STANDARD\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;/</span><span class=\"pln\">generatepreviews</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%7B%22object_story_id%22%3A%22%3CPAGE_ID%3E_%3CPOST_ID%3E%22%7D%26ad_format=DESKTOP_FEED_STANDARD&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_2g_D9\"><input type=\"hidden\" name=\"jazoest\" value=\"2986\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZyaU\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/generatepreview\"><input type=\"hidden\" autocomplete=\"off\" name=\"id\" value=\"***************\"><input type=\"hidden\" autocomplete=\"off\" name=\"name\" value=\"ADACCOUNT_GET_PREVIEWS_DESKTOP_WITH_STORY_ID\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><br><p>Create an app ad preview using <code>object_story_spec</code>. This is the only way to generate a preview for an app ad.</p>\n<div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_2h_kl\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2i_vg\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2j_9d\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2k_bL\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2l_0j\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2m_SD\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_2n_gq\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_2o_dA\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_2p_JA\"><span label=\"Copy Code\" value=\"curl -X GET \\\n  -d 'creative={\n       &quot;object_story_spec&quot;: {\n         &quot;link_data&quot;: {\n           &quot;call_to_action&quot;: {\n             &quot;type&quot;: &quot;USE_APP&quot;,\n             &quot;value&quot;: {\n               &quot;link&quot;: &quot;<URL>&quot;\n             }\n           },\n           &quot;description&quot;: &quot;Description&quot;,\n           &quot;link&quot;: &quot;<URL>&quot;,\n           &quot;message&quot;: &quot;Message&quot;,\n           &quot;name&quot;: &quot;Name&quot;,\n           &quot;picture&quot;: &quot;<IMAGE_URL>&quot;\n         },\n         &quot;page_id&quot;: &quot;<PAGE_ID>&quot;\n       }\n     }' \\\n  -d 'ad_format=&quot;MOBILE_FEED_STANDARD&quot;' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'creative={\n       \"object_story_spec\": {\n         \"link_data\": {\n           \"call_to_action\": {\n             \"type\": \"USE_APP\",\n             \"value\": {\n               \"link\": \"&lt;URL&gt;\"\n             }\n           },\n           \"description\": \"Description\",\n           \"link\": \"&lt;URL&gt;\",\n           \"message\": \"Message\",\n           \"name\": \"Name\",\n           \"picture\": \"&lt;IMAGE_URL&gt;\"\n         },\n         \"page_id\": \"&lt;PAGE_ID&gt;\"\n       }\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=\"MOBILE_FEED_STANDARD\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;/</span><span class=\"pln\">generatepreviews</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fgeneratepreviews?creative=%7B%22object_story_spec%22%3A%7B%22link_data%22%3A%7B%22call_to_action%22%3A%7B%22type%22%3A%22USE_APP%22%2C%22value%22%3A%7B%22link%22%3A%22%3CURL%3E%22%7D%7D%2C%22description%22%3A%22Description%22%2C%22link%22%3A%22%3CURL%3E%22%2C%22message%22%3A%22Message%22%2C%22name%22%3A%22Name%22%2C%22picture%22%3A%22%3CIMAGE_URL%3E%22%7D%2C%22page_id%22%3A%22%3CPAGE_ID%3E%22%7D%7D%26ad_format=MOBILE_FEED_STANDARD&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_35_Vo\"><input type=\"hidden\" name=\"jazoest\" value=\"2986\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZyaU\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/generatepreview\"><input type=\"hidden\" autocomplete=\"off\" name=\"id\" value=\"***************\"><input type=\"hidden\" autocomplete=\"off\" name=\"name\" value=\"ADACCOUNT_GET_PREVIEWS_MAIA_WITH_OBJECT_STORY_SPEC\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><br><br><p>Instagram Explore home Ad Previews using INSTAGRAM_EXPLORE_GRID_HOME ad format</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=\"INSTAGRAM_EXPLORE_GRID_HOME\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;AD_ID&gt;/previews</span></pre><p>This returns something like this:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"str\">\"data\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">[</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"str\">\"body\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;iframe src=\\\"https://www.facebook.com/ads/api/preview_iframe.php?d=AQKuwYcWpyFgVKLORPWQi52_uTud4v8PpMoDtyBfntL65i0iFtgkiXWN5S4JMBhq-UMKQmvxXFexVxu-5l5Xbf4WWRP48sCAtn3ArQAXwbdrD5qH0EL2z34K-gAgYyENd80cOGAdhVreKGJZvPkLbjDS3iDkdqdNNJQ6yaAFTmUpaz__cjgmhVVCUW68wU3UOZwqlv376mkijYR57Sm2OlyES4U6ivMPNGDx4xnZEd5d8kWyagDD-lPbCaGEk0nnQF5mnyeV9pFqdByhq-IqN6n0ZhSWjCPXZQa84wu5GNQ70YR2w7QxEYoiWCgI2WP0Z2OPeUMiNOf9bhYB-TBZJZ7G6HylsOnzzII9FQ8-0K-b_Q&amp;t=AQJws9t-TtIGrKoFtCM\\\" width=\\\"274\\\" height=\\\"213\\\" scrolling=\\\"yes\\\" style=\\\"border: none;\\\"&gt;&lt;/iframe&gt;\"</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n  </span><span class=\"pun\">]</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></pre><p>Instagram search results Ad Previews using INSTAGRAM_SEARCH_CHAIN ad format</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'ad_format=\"INSTAGRAM_SEARCH_CHAIN\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;AD_ID&gt;/previews</span></pre><p>This returns something like this:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"str\">\"data\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">[</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"str\">\"body\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;iframe src=\\\"https://www.facebook.com/ads/api/preview_iframe.php?d=AQKVMPdwuorP3mliXRaOi0TCSvsGRfucEzBTnB4jghArle84f8kBjvJmX3gmdjniUjohKA3GUppDZqljStZwxxRRxkQl9Y4R1o5wV4zRGE3xO3NHf1_qBbFM_uEIGAnAvptMWo_DLpbiIqIYFMjxbXNELzmZQsR0gnbBjaXM9i6gkI29dnHPqnm4xGvPxo2w8RWeXfWvmik2C96_2PrhrRhh4NKL3SOmFC9JDVsTp9Z6SYDlLVcLJWwpRKmciAZqEMOnMEFgepVTZ39yJ4ZiAMRo76RK9XNVGcornsUBtxI8cZHKtdW7nmj3ivq09_NGGUnFiJdJaPm-Mk-obM3K0QyOvgHKwnmLn7wvMiizJeXPEWAcSBa4DPUFLAO1mSuaKla0VQ6tzAM4BqFU9LJOG1-zZmPec7wKxQGDcrXoCOKfv2xkLyzECc-oDS0JJgvxxlo&amp;t=AQI8ECKvkemIoVDaDrs\\\" width=\\\"274\\\" height=\\\"213\\\" scrolling=\\\"yes\\\" style=\\\"border: none;\\\"&gt;&lt;/iframe&gt;\"</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n  </span><span class=\"pun\">],</span><span class=\"pln\">\n  </span><span class=\"str\">\"__www_request_id__\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"AzCC2RoeSL0rMbSPTYDyDHa\"</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></pre>\n\n", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference", "/docs/marketing-api/generatepreview", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/adgroup/previews", "/docs/marketing-api/reference/ad-creative/previews", "/docs/marketing-api/reference/ad-account/generatepreviews/", "/docs/marketing-api/dynamic-product-ads/ads-management#dynamicpreview", "/docs/marketing-api/reference/ad-preview/"], "url": "https://developers.facebook.com/docs/marketing-api/generatepreview/v23.0", "timestamp": "2025-06-25T15:42:42.552Z"}