# Facebook Marketing API - Ad Account Asyncadcreatives Reference

## Summary
Documentation for the Ad Account Asyncadcreatives endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous ad creative jobs but does not support reading, updating, or deleting operations.

## Key Points
- Only POST (creating) operations are supported on this endpoint
- No Graph object is created when posting to this edge
- Supports read-after-write functionality
- Requires creative_spec and name parameters
- Optional notification system with completion callbacks

## API Endpoints
- `/act_{ad_account_id}/asyncadcreatives`

## Parameters
- creative_spec
- name
- notification_mode
- notification_uri

## Content
# Ad Account Asyncadcreatives

## Overview

The Ad Account Asyncadcreatives endpoint is part of the Facebook Marketing API v23.0 that handles asynchronous ad creative operations.

## Supported Operations

### Reading
Reading operations are not supported on this endpoint.

### Creating
You can make a POST request to the `asyncadcreatives` edge from the following path:
- `/act_{ad_account_id}/asyncadcreatives`

When posting to this edge, no Graph object will be created.

#### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| `creative_spec` | AdCreative | Specs for ad creative | Yes (Supports Emoji) |
| `name` | UTF-8 encoded string | Name of async job | Yes |
| `notification_mode` | enum{OFF, ON_COMPLETE} | Specify `0` for no notifications and `1` for notification on completion | No |
| `notification_uri` | URL | If notifications are enabled, specify the URL to send them | No |

#### Return Type
This endpoint supports read-after-write and will read the node represented by `id` in the return type.

```json
{
  "id": "numeric string"
}
```

#### Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |

### Updating
Updating operations are not supported on this endpoint.

### Deleting
Deleting operations are not supported on this endpoint.

---
**Tags:** Facebook Marketing API, Ad Account, Async Ad Creatives, POST endpoint, v23.0  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/asyncadcreatives/  
**Processed:** 2025-06-25T16:24:55.784Z