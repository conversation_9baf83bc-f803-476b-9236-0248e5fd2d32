{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestBuilder_1 = __importDefault(require(\"./PostgrestBuilder\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n  /**\n   * Perform a SELECT on the query result.\n   *\n   * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n   * return modified rows. By calling this method, modified rows are returned in\n   * `data`.\n   *\n   * @param columns - The columns to retrieve, separated by commas\n   */\n  select(columns) {\n    // Remove whitespaces except when quoted\n    let quoted = false;\n    const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*').split('').map(c => {\n      if (/\\s/.test(c) && !quoted) {\n        return '';\n      }\n      if (c === '\"') {\n        quoted = !quoted;\n      }\n      return c;\n    }).join('');\n    this.url.searchParams.set('select', cleanedColumns);\n    if (this.headers['Prefer']) {\n      this.headers['Prefer'] += ',';\n    }\n    this.headers['Prefer'] += 'return=representation';\n    return this;\n  }\n  /**\n   * Order the query result by `column`.\n   *\n   * You can call this method multiple times to order by multiple columns.\n   *\n   * You can order referenced tables, but it only affects the ordering of the\n   * parent table if you use `!inner` in the query.\n   *\n   * @param column - The column to order by\n   * @param options - Named parameters\n   * @param options.ascending - If `true`, the result will be in ascending order\n   * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n   * `null`s appear last.\n   * @param options.referencedTable - Set this to order a referenced table by\n   * its columns\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  order(column, {\n    ascending = true,\n    nullsFirst,\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const key = referencedTable ? `${referencedTable}.order` : 'order';\n    const existingOrder = this.url.searchParams.get(key);\n    this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n    return this;\n  }\n  /**\n   * Limit the query result by `count`.\n   *\n   * @param count - The maximum number of rows to return\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  limit(count, {\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n    this.url.searchParams.set(key, `${count}`);\n    return this;\n  }\n  /**\n   * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n   * Only records within this range are returned.\n   * This respects the query order and if there is no order clause the range could behave unexpectedly.\n   * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n   * and fourth rows of the query.\n   *\n   * @param from - The starting index from which to limit the result\n   * @param to - The last index to which to limit the result\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  range(from, to, {\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n    const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n    this.url.searchParams.set(keyOffset, `${from}`);\n    // Range is inclusive, so add 1\n    this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n    return this;\n  }\n  /**\n   * Set the AbortSignal for the fetch request.\n   *\n   * @param signal - The AbortSignal to use for the fetch request\n   */\n  abortSignal(signal) {\n    this.signal = signal;\n    return this;\n  }\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n   * returns an error.\n   */\n  single() {\n    this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n    return this;\n  }\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n   * this returns an error.\n   */\n  maybeSingle() {\n    // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n    // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n    if (this.method === 'GET') {\n      this.headers['Accept'] = 'application/json';\n    } else {\n      this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n    }\n    this.isMaybeSingle = true;\n    return this;\n  }\n  /**\n   * Return `data` as a string in CSV format.\n   */\n  csv() {\n    this.headers['Accept'] = 'text/csv';\n    return this;\n  }\n  /**\n   * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n   */\n  geojson() {\n    this.headers['Accept'] = 'application/geo+json';\n    return this;\n  }\n  /**\n   * Return `data` as the EXPLAIN plan for the query.\n   *\n   * You need to enable the\n   * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n   * setting before using this method.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.analyze - If `true`, the query will be executed and the\n   * actual run time will be returned\n   *\n   * @param options.verbose - If `true`, the query identifier will be returned\n   * and `data` will include the output columns of the query\n   *\n   * @param options.settings - If `true`, include information on configuration\n   * parameters that affect query planning\n   *\n   * @param options.buffers - If `true`, include information on buffer usage\n   *\n   * @param options.wal - If `true`, include information on WAL record generation\n   *\n   * @param options.format - The format of the output, can be `\"text\"` (default)\n   * or `\"json\"`\n   */\n  explain({\n    analyze = false,\n    verbose = false,\n    settings = false,\n    buffers = false,\n    wal = false,\n    format = 'text'\n  } = {}) {\n    var _a;\n    const options = [analyze ? 'analyze' : null, verbose ? 'verbose' : null, settings ? 'settings' : null, buffers ? 'buffers' : null, wal ? 'wal' : null].filter(Boolean).join('|');\n    // An Accept header can carry multiple media types but postgrest-js always sends one\n    const forMediatype = (_a = this.headers['Accept']) !== null && _a !== void 0 ? _a : 'application/json';\n    this.headers['Accept'] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`;\n    if (format === 'json') return this;else return this;\n  }\n  /**\n   * Rollback the query.\n   *\n   * `data` will still be returned, but the query is not committed.\n   */\n  rollback() {\n    var _a;\n    if (((_a = this.headers['Prefer']) !== null && _a !== void 0 ? _a : '').trim().length > 0) {\n      this.headers['Prefer'] += ',tx=rollback';\n    } else {\n      this.headers['Prefer'] = 'tx=rollback';\n    }\n    return this;\n  }\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns() {\n    return this;\n  }\n}\nexports.default = PostgrestTransformBuilder;", "map": {"version": 3, "names": ["PostgrestBuilder_1", "__importDefault", "require", "PostgrestTransformBuilder", "default", "select", "columns", "quoted", "cleanedColumns", "split", "map", "c", "test", "join", "url", "searchParams", "set", "headers", "order", "column", "ascending", "nullsFirst", "foreignTable", "referencedTable", "key", "existingOrder", "get", "undefined", "limit", "count", "range", "from", "to", "keyOffset", "keyLimit", "abortSignal", "signal", "single", "<PERSON><PERSON><PERSON><PERSON>", "method", "isMaybeSingle", "csv", "g<PERSON><PERSON><PERSON>", "explain", "analyze", "verbose", "settings", "buffers", "wal", "format", "options", "filter", "Boolean", "forMediatype", "_a", "rollback", "trim", "length", "returns", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\postgrest-js\\src\\PostgrestTransformBuilder.ts"], "sourcesContent": ["import PostgrestBuilder from './PostgrestBuilder'\nimport { GetResult } from './select-query-parser/result'\nimport { GenericSchema, CheckMatchingArrayTypes } from './types'\n\nexport default class PostgrestTransformBuilder<\n  Schema extends GenericSchema,\n  Row extends Record<string, unknown>,\n  Result,\n  RelationName = unknown,\n  Relationships = unknown\n> extends PostgrestBuilder<Result> {\n  /**\n   * Perform a SELECT on the query result.\n   *\n   * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n   * return modified rows. By calling this method, modified rows are returned in\n   * `data`.\n   *\n   * @param columns - The columns to retrieve, separated by commas\n   */\n  select<\n    Query extends string = '*',\n    NewResultOne = GetResult<Schema, Row, RelationName, Relationships, Query>\n  >(\n    columns?: Query\n  ): PostgrestTransformBuilder<Schema, Row, NewResultOne[], RelationName, Relationships> {\n    // Remove whitespaces except when quoted\n    let quoted = false\n    const cleanedColumns = (columns ?? '*')\n      .split('')\n      .map((c) => {\n        if (/\\s/.test(c) && !quoted) {\n          return ''\n        }\n        if (c === '\"') {\n          quoted = !quoted\n        }\n        return c\n      })\n      .join('')\n    this.url.searchParams.set('select', cleanedColumns)\n    if (this.headers['Prefer']) {\n      this.headers['Prefer'] += ','\n    }\n    this.headers['Prefer'] += 'return=representation'\n    return this as unknown as PostgrestTransformBuilder<\n      Schema,\n      Row,\n      NewResultOne[],\n      RelationName,\n      Relationships\n    >\n  }\n\n  order<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    options?: { ascending?: boolean; nullsFirst?: boolean; referencedTable?: undefined }\n  ): this\n  order(\n    column: string,\n    options?: { ascending?: boolean; nullsFirst?: boolean; referencedTable?: string }\n  ): this\n  /**\n   * @deprecated Use `options.referencedTable` instead of `options.foreignTable`\n   */\n  order<ColumnName extends string & keyof Row>(\n    column: ColumnName,\n    options?: { ascending?: boolean; nullsFirst?: boolean; foreignTable?: undefined }\n  ): this\n  /**\n   * @deprecated Use `options.referencedTable` instead of `options.foreignTable`\n   */\n  order(\n    column: string,\n    options?: { ascending?: boolean; nullsFirst?: boolean; foreignTable?: string }\n  ): this\n  /**\n   * Order the query result by `column`.\n   *\n   * You can call this method multiple times to order by multiple columns.\n   *\n   * You can order referenced tables, but it only affects the ordering of the\n   * parent table if you use `!inner` in the query.\n   *\n   * @param column - The column to order by\n   * @param options - Named parameters\n   * @param options.ascending - If `true`, the result will be in ascending order\n   * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n   * `null`s appear last.\n   * @param options.referencedTable - Set this to order a referenced table by\n   * its columns\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  order(\n    column: string,\n    {\n      ascending = true,\n      nullsFirst,\n      foreignTable,\n      referencedTable = foreignTable,\n    }: {\n      ascending?: boolean\n      nullsFirst?: boolean\n      foreignTable?: string\n      referencedTable?: string\n    } = {}\n  ): this {\n    const key = referencedTable ? `${referencedTable}.order` : 'order'\n    const existingOrder = this.url.searchParams.get(key)\n\n    this.url.searchParams.set(\n      key,\n      `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${\n        nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'\n      }`\n    )\n    return this\n  }\n\n  /**\n   * Limit the query result by `count`.\n   *\n   * @param count - The maximum number of rows to return\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  limit(\n    count: number,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`\n    this.url.searchParams.set(key, `${count}`)\n    return this\n  }\n\n  /**\n   * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n   * Only records within this range are returned.\n   * This respects the query order and if there is no order clause the range could behave unexpectedly.\n   * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n   * and fourth rows of the query.\n   *\n   * @param from - The starting index from which to limit the result\n   * @param to - The last index to which to limit the result\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  range(\n    from: number,\n    to: number,\n    {\n      foreignTable,\n      referencedTable = foreignTable,\n    }: { foreignTable?: string; referencedTable?: string } = {}\n  ): this {\n    const keyOffset =\n      typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`\n    const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`\n    this.url.searchParams.set(keyOffset, `${from}`)\n    // Range is inclusive, so add 1\n    this.url.searchParams.set(keyLimit, `${to - from + 1}`)\n    return this\n  }\n\n  /**\n   * Set the AbortSignal for the fetch request.\n   *\n   * @param signal - The AbortSignal to use for the fetch request\n   */\n  abortSignal(signal: AbortSignal): this {\n    this.signal = signal\n    return this\n  }\n\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n   * returns an error.\n   */\n  single<\n    ResultOne = Result extends (infer ResultOne)[] ? ResultOne : never\n  >(): PostgrestBuilder<ResultOne> {\n    this.headers['Accept'] = 'application/vnd.pgrst.object+json'\n    return this as unknown as PostgrestBuilder<ResultOne>\n  }\n\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n   * this returns an error.\n   */\n  maybeSingle<\n    ResultOne = Result extends (infer ResultOne)[] ? ResultOne : never\n  >(): PostgrestBuilder<ResultOne | null> {\n    // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n    // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n    if (this.method === 'GET') {\n      this.headers['Accept'] = 'application/json'\n    } else {\n      this.headers['Accept'] = 'application/vnd.pgrst.object+json'\n    }\n    this.isMaybeSingle = true\n    return this as unknown as PostgrestBuilder<ResultOne | null>\n  }\n\n  /**\n   * Return `data` as a string in CSV format.\n   */\n  csv(): PostgrestBuilder<string> {\n    this.headers['Accept'] = 'text/csv'\n    return this as unknown as PostgrestBuilder<string>\n  }\n\n  /**\n   * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n   */\n  geojson(): PostgrestBuilder<Record<string, unknown>> {\n    this.headers['Accept'] = 'application/geo+json'\n    return this as unknown as PostgrestBuilder<Record<string, unknown>>\n  }\n\n  /**\n   * Return `data` as the EXPLAIN plan for the query.\n   *\n   * You need to enable the\n   * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n   * setting before using this method.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.analyze - If `true`, the query will be executed and the\n   * actual run time will be returned\n   *\n   * @param options.verbose - If `true`, the query identifier will be returned\n   * and `data` will include the output columns of the query\n   *\n   * @param options.settings - If `true`, include information on configuration\n   * parameters that affect query planning\n   *\n   * @param options.buffers - If `true`, include information on buffer usage\n   *\n   * @param options.wal - If `true`, include information on WAL record generation\n   *\n   * @param options.format - The format of the output, can be `\"text\"` (default)\n   * or `\"json\"`\n   */\n  explain({\n    analyze = false,\n    verbose = false,\n    settings = false,\n    buffers = false,\n    wal = false,\n    format = 'text',\n  }: {\n    analyze?: boolean\n    verbose?: boolean\n    settings?: boolean\n    buffers?: boolean\n    wal?: boolean\n    format?: 'json' | 'text'\n  } = {}): PostgrestBuilder<Record<string, unknown>[]> | PostgrestBuilder<string> {\n    const options = [\n      analyze ? 'analyze' : null,\n      verbose ? 'verbose' : null,\n      settings ? 'settings' : null,\n      buffers ? 'buffers' : null,\n      wal ? 'wal' : null,\n    ]\n      .filter(Boolean)\n      .join('|')\n    // An Accept header can carry multiple media types but postgrest-js always sends one\n    const forMediatype = this.headers['Accept'] ?? 'application/json'\n    this.headers[\n      'Accept'\n    ] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`\n    if (format === 'json') return this as unknown as PostgrestBuilder<Record<string, unknown>[]>\n    else return this as unknown as PostgrestBuilder<string>\n  }\n\n  /**\n   * Rollback the query.\n   *\n   * `data` will still be returned, but the query is not committed.\n   */\n  rollback(): this {\n    if ((this.headers['Prefer'] ?? '').trim().length > 0) {\n      this.headers['Prefer'] += ',tx=rollback'\n    } else {\n      this.headers['Prefer'] = 'tx=rollback'\n    }\n    return this\n  }\n\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns<NewResult>(): PostgrestTransformBuilder<\n    Schema,\n    Row,\n    CheckMatchingArrayTypes<Result, NewResult>,\n    RelationName,\n    Relationships\n  > {\n    return this as unknown as PostgrestTransformBuilder<\n      Schema,\n      Row,\n      CheckMatchingArrayTypes<Result, NewResult>,\n      RelationName,\n      Relationships\n    >\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA,MAAAA,kBAAA,GAAAC,eAAA,CAAAC,OAAA;AAIA,MAAqBC,yBAMnB,SAAQH,kBAAA,CAAAI,OAAwB;EAChC;;;;;;;;;EASAC,MAAMA,CAIJC,OAAe;IAEf;IACA,IAAIC,MAAM,GAAG,KAAK;IAClB,MAAMC,cAAc,GAAG,CAACF,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,GAAG,EACnCG,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAAEC,CAAC,IAAI;MACT,IAAI,IAAI,CAACC,IAAI,CAACD,CAAC,CAAC,IAAI,CAACJ,MAAM,EAAE;QAC3B,OAAO,EAAE;;MAEX,IAAII,CAAC,KAAK,GAAG,EAAE;QACbJ,MAAM,GAAG,CAACA,MAAM;;MAElB,OAAOI,CAAC;IACV,CAAC,CAAC,CACDE,IAAI,CAAC,EAAE,CAAC;IACX,IAAI,CAACC,GAAG,CAACC,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAER,cAAc,CAAC;IACnD,IAAI,IAAI,CAACS,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC1B,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG;;IAE/B,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,IAAI,uBAAuB;IACjD,OAAO,IAMN;EACH;EAwBA;;;;;;;;;;;;;;;;;;EAkBAC,KAAKA,CACHC,MAAc,EACd;IACEC,SAAS,GAAG,IAAI;IAChBC,UAAU;IACVC,YAAY;IACZC,eAAe,GAAGD;EAAY,IAM5B,EAAE;IAEN,MAAME,GAAG,GAAGD,eAAe,GAAG,GAAGA,eAAe,QAAQ,GAAG,OAAO;IAClE,MAAME,aAAa,GAAG,IAAI,CAACX,GAAG,CAACC,YAAY,CAACW,GAAG,CAACF,GAAG,CAAC;IAEpD,IAAI,CAACV,GAAG,CAACC,YAAY,CAACC,GAAG,CACvBQ,GAAG,EACH,GAAGC,aAAa,GAAG,GAAGA,aAAa,GAAG,GAAG,EAAE,GAAGN,MAAM,IAAIC,SAAS,GAAG,KAAK,GAAG,MAAM,GAChFC,UAAU,KAAKM,SAAS,GAAG,EAAE,GAAGN,UAAU,GAAG,aAAa,GAAG,YAC/D,EAAE,CACH;IACD,OAAO,IAAI;EACb;EAEA;;;;;;;;;;EAUAO,KAAKA,CACHC,KAAa,EACb;IACEP,YAAY;IACZC,eAAe,GAAGD;EAAY,IACyB,EAAE;IAE3D,MAAME,GAAG,GAAG,OAAOD,eAAe,KAAK,WAAW,GAAG,OAAO,GAAG,GAAGA,eAAe,QAAQ;IACzF,IAAI,CAACT,GAAG,CAACC,YAAY,CAACC,GAAG,CAACQ,GAAG,EAAE,GAAGK,KAAK,EAAE,CAAC;IAC1C,OAAO,IAAI;EACb;EAEA;;;;;;;;;;;;;;;EAeAC,KAAKA,CACHC,IAAY,EACZC,EAAU,EACV;IACEV,YAAY;IACZC,eAAe,GAAGD;EAAY,IACyB,EAAE;IAE3D,MAAMW,SAAS,GACb,OAAOV,eAAe,KAAK,WAAW,GAAG,QAAQ,GAAG,GAAGA,eAAe,SAAS;IACjF,MAAMW,QAAQ,GAAG,OAAOX,eAAe,KAAK,WAAW,GAAG,OAAO,GAAG,GAAGA,eAAe,QAAQ;IAC9F,IAAI,CAACT,GAAG,CAACC,YAAY,CAACC,GAAG,CAACiB,SAAS,EAAE,GAAGF,IAAI,EAAE,CAAC;IAC/C;IACA,IAAI,CAACjB,GAAG,CAACC,YAAY,CAACC,GAAG,CAACkB,QAAQ,EAAE,GAAGF,EAAE,GAAGD,IAAI,GAAG,CAAC,EAAE,CAAC;IACvD,OAAO,IAAI;EACb;EAEA;;;;;EAKAI,WAAWA,CAACC,MAAmB;IAC7B,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,OAAO,IAAI;EACb;EAEA;;;;;;EAMAC,MAAMA,CAAA;IAGJ,IAAI,CAACpB,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC;IAC5D,OAAO,IAA8C;EACvD;EAEA;;;;;;EAMAqB,WAAWA,CAAA;IAGT;IACA;IACA,IAAI,IAAI,CAACC,MAAM,KAAK,KAAK,EAAE;MACzB,IAAI,CAACtB,OAAO,CAAC,QAAQ,CAAC,GAAG,kBAAkB;KAC5C,MAAM;MACL,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC;;IAE9D,IAAI,CAACuB,aAAa,GAAG,IAAI;IACzB,OAAO,IAAqD;EAC9D;EAEA;;;EAGAC,GAAGA,CAAA;IACD,IAAI,CAACxB,OAAO,CAAC,QAAQ,CAAC,GAAG,UAAU;IACnC,OAAO,IAA2C;EACpD;EAEA;;;EAGAyB,OAAOA,CAAA;IACL,IAAI,CAACzB,OAAO,CAAC,QAAQ,CAAC,GAAG,sBAAsB;IAC/C,OAAO,IAA4D;EACrE;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;EAyBA0B,OAAOA,CAAC;IACNC,OAAO,GAAG,KAAK;IACfC,OAAO,GAAG,KAAK;IACfC,QAAQ,GAAG,KAAK;IAChBC,OAAO,GAAG,KAAK;IACfC,GAAG,GAAG,KAAK;IACXC,MAAM,GAAG;EAAM,IAQb,EAAE;;IACJ,MAAMC,OAAO,GAAG,CACdN,OAAO,GAAG,SAAS,GAAG,IAAI,EAC1BC,OAAO,GAAG,SAAS,GAAG,IAAI,EAC1BC,QAAQ,GAAG,UAAU,GAAG,IAAI,EAC5BC,OAAO,GAAG,SAAS,GAAG,IAAI,EAC1BC,GAAG,GAAG,KAAK,GAAG,IAAI,CACnB,CACEG,MAAM,CAACC,OAAO,CAAC,CACfvC,IAAI,CAAC,GAAG,CAAC;IACZ;IACA,MAAMwC,YAAY,GAAG,CAAAC,EAAA,OAAI,CAACrC,OAAO,CAAC,QAAQ,CAAC,cAAAqC,EAAA,cAAAA,EAAA,GAAI,kBAAkB;IACjE,IAAI,CAACrC,OAAO,CACV,QAAQ,CACT,GAAG,8BAA8BgC,MAAM,UAAUI,YAAY,cAAcH,OAAO,GAAG;IACtF,IAAID,MAAM,KAAK,MAAM,EAAE,OAAO,IAA8D,MACvF,OAAO,IAA2C;EACzD;EAEA;;;;;EAKAM,QAAQA,CAAA;;IACN,IAAI,CAAC,CAAAD,EAAA,OAAI,CAACrC,OAAO,CAAC,QAAQ,CAAC,cAAAqC,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAEE,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACxC,OAAO,CAAC,QAAQ,CAAC,IAAI,cAAc;KACzC,MAAM;MACL,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,aAAa;;IAExC,OAAO,IAAI;EACb;EAEA;;;;;;EAMAyC,OAAOA,CAAA;IAOL,OAAO,IAMN;EACH;;AAjUFC,OAAA,CAAAvD,OAAA,GAAAD,yBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}