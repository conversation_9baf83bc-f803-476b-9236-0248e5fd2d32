# Facebook Marketing API - Ad Account Delivery Estimate

## Summary
The Ad Account Delivery Estimate endpoint returns delivery estimates for a given ad set configuration within an ad account. This read-only endpoint helps advertisers predict the potential reach and delivery of their ad campaigns before launching them.

## Key Points
- Provides delivery estimates for ad set configurations before campaign launch
- Requires optimization_goal and targeting_spec as mandatory parameters
- Read-only endpoint - no create, update, or delete operations supported
- Cannot be used with inactive Lookalike Audiences
- Returns structured data with delivery predictions and paging information

## API Endpoints
- `GET /v23.0/{ad-account-id}/delivery_estimate`

## Parameters
- optimization_goal
- targeting_spec
- promoted_object
- application_id
- pixel_id
- page_id
- product_catalog_id
- custom_event_type
- omnichannel_object
- full_funnel_objective
- value_semantic_type

## Content
# Ad Account Delivery Estimate

Returns the delivery estimate for a given ad set configuration in this ad account. You are not able to retrieve this field for inactive Lookalike Audiences.

## Reading

Delivery estimate for a given ad set configuration in this ad account.

### Endpoint

```
GET /v23.0/{ad-account-id}/delivery_estimate
```

### Required Parameters

- **optimization_goal** (enum): The optimization goal for the estimate. Supported values include:
  - `NONE`, `APP_INSTALLS`, `AD_RECALL_LIFT`, `ENGAGED_USERS`, `EVENT_RESPONSES`
  - `IMPRESSIONS`, `LEAD_GENERATION`, `QUALITY_LEAD`, `LINK_CLICKS`
  - `OFFSITE_CONVERSIONS`, `PAGE_LIKES`, `POST_ENGAGEMENT`, `QUALITY_CALL`
  - `REACH`, `LANDING_PAGE_VIEWS`, `VISIT_INSTAGRAM_PROFILE`, `VALUE`
  - `THRUPLAY`, `DERIVED_EVENTS`, `APP_INSTALLS_AND_OFFSITE_CONVERSIONS`
  - `CONVERSATIONS`, `IN_APP_VALUE`, `MESSAGING_PURCHASE_CONVERSION`
  - And many more...

- **targeting_spec** (Targeting object): The targeting specification for delivery estimate

### Optional Parameters

#### Promoted Object
- **application_id** (int): Facebook Application ID
- **pixel_id** (numeric string): Facebook conversion pixel ID
- **page_id** (Page ID): Facebook Page ID
- **product_catalog_id** (numeric string): Product Catalog ID for Dynamic Product Ads
- **event_id** (numeric string): Facebook Event ID
- **custom_event_type** (enum): Custom event types for mobile apps
- **object_store_url** (URL): Mobile/digital store URL
- **offer_id** (numeric string): Facebook Page Offer ID
- **instagram_profile_id** (numeric string): Instagram profile ID
- **offline_conversion_data_set_id** (numeric string): Offline dataset ID
- **fundraiser_campaign_id** (numeric string): Fundraiser campaign ID
- **conversion_goal_id** (numeric string): Conversion Goal ID
- **whats_app_business_phone_number_id** (numeric string): WhatsApp Business phone number ID

#### Advanced Options
- **omnichannel_object** (Object): Omnichannel configuration
- **full_funnel_objective** (enum): Full funnel objective values (6, 8, 12, 14, 15, 19, 24, 26, 29, 31, 32, 35, 36, 37, 39, 41, 42, 40, 43, 44, 46)
- **product_set_optimization** (enum): `enabled` or `disabled`
- **value_semantic_type** (enum): `VALUE`, `MARGIN`, or `LIFETIME_VALUE`

### Response Format

```json
{
  "data": [],
  "paging": {}
}
```

The `data` field contains a list of AdAccountDeliveryEstimate nodes.

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/delivery_estimate HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
try {
  $response = $fb->get(
    '/{ad-account-id}/delivery_estimate',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/delivery_estimate",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | The action attempted has been deemed abusive or is otherwise disallowed |
| 613 | Calls to this api have exceeded the rate limit |
| 900 | No such application exists |
| 2635 | You are calling a deprecated version of the Ads API |
| 2641 | Your ad includes or excludes locations that are currently restricted |
| 80004 | Too many calls to this ad-account. Wait and try again |

### Limitations

- **Read-only**: This endpoint only supports GET requests
- **No Creating/Updating/Deleting**: These operations are not supported
- **Inactive Lookalike Audiences**: Cannot retrieve estimates for inactive Lookalike Audiences

## Examples
HTTP GET request to delivery_estimate endpoint

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest example

iOS SDK FBSDKGraphRequest example

---
**Tags:** Facebook Marketing API, Ad Account, Delivery Estimate, Campaign Planning, Targeting, Optimization Goals, API Reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/delivery_estimate/  
**Processed:** 2025-06-25T16:27:26.838Z