# Ad Account Saved Audiences

Graph API Version

[v23.0](#)

# Ad Account Saved Audiences

## Reading

saved audiences

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fsaved_audiences&version=v23.0)

```
`GET /v23.0/{ad-account-id}/saved_audiences HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`business_id`

numeric string or integer

optional param assist with filters such as recently used

`fields`

list<string>

Fields to be retrieved. Default behavior is to return only the ids.

`filtering`

list<Filter Object>

Filters on the report data. This parameter is an array of filter objects.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of [SavedAudience](/docs/marketing-api/reference/saved-audience/) nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

200

Permissions error

100

Invalid parameter

190

Invalid OAuth 2.0 Access Token

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

## Creating

You can't perform this operation on this endpoint.

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.