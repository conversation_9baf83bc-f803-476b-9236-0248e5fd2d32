# Overview

On This Page

[Overview](#overview)

[How it Works](#how-it-works)

[Ad campaigns](#ad-campaigns)

[Ad sets](#ad-sets)

[Ad creatives](#ad-creatives)

[Ads](#ads)

[Ad Components](#ad-components)

# Overview

The Marketing API is a Meta business tool designed to empower developers and marketers with the ability to automate advertising efforts across Meta technologies. It offers a comprehensive suite of functionalities that streamline the processes of ad creation, management, and performance analysis.

One of the primary features of the Marketing API is its ability to facilitate the automated creation of ads. You can programmatically generate ad campaigns, ad sets, and individual ads, allowing for rapid deployment and iteration based on real-time performance data. This automation also enables businesses to reach larger audiences with greater efficiency.

In addition to ad creation, you can:

*   Update, pause, or delete ads seamlessly
*   Ensure that campaigns remain aligned with business objectives
*   Access detailed insights and analytics to track ad performance and make data-driven decisions to improve outcomes

## How it Works

![](https://scontent-phx1-1.xx.fbcdn.net/v/t39.8562-6/465922728_1274126690433578_4616558889838468033_n.png?_nc_cat=103&ccb=1-7&_nc_sid=f537c7&_nc_ohc=N9X76CpW_hoQ7kNvwHRgo0d&_nc_oc=AdnmTPUAPRM07qkO-7kjW8uak9DuW9JkQsiQ732GX0cjSY4I5SM_H9f1LgiQn6oSHxM&_nc_zt=14&_nc_ht=scontent-phx1-1.xx&_nc_gid=cdOLrL3kR-2sXcvKziz4bQ&oh=00_AfNGsD0N3Hr38-ru2z61UbSs7vKINAJ7BYaZ0GKuk4Hyfg&oe=6861DBE5)

### Ad campaigns

A campaign is the highest level organizational structure within an ad account and should represent a single objective, for example, to drive Page post engagement. Setting the objective of the campaign enforces validation on any ads added to that campaign to ensure they also have the correct objective.

### Ad sets

Ad sets are groups of ads and are used to configure the budget and period the ads should run for. All ads contained within an ad set should have the same targeting, budget, billing, optimization goal, and duration.

Create an ad set for each target audience with your bid; ads in the set target the same audience with the same bid. This helps control the amount you spend on each audience, determine when the audience will see your ads, and provides metrics for each audience.

### Ad creatives

Ad creatives contain just the visual elements of the ad and you can't change them once they're created. Each ad account has a creative library to store creatives for reuse in ads.

### Ads

An ad object contains all of the information necessary to display an ad on Facebook, Instagram, Messenger, and WhatsApp, including the ad creative. Create multiple ads in each ad set to optimize ad delivery based on different images, links, video, text, or placements.

### Ad Components

This table shows how the various ad components align to the different levels of ad creation.

Ad Campaign

Ad Set

Ad

**Objective**

✓

**Schedule**

✓

**Budget**

✓

**Bidding**

✓

**Audience**

✓

**Ad Creative**

✓

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)