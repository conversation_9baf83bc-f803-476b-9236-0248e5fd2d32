# Facebook Marketing API - Ad Videos Reference

## Summary
Complete reference documentation for the Facebook Marketing API Ad Videos endpoint, covering video upload, management, and deletion operations for ad accounts. Includes detailed parameters, return types, and error codes for video operations.

## Key Points
- Ad Videos endpoint supports creating (uploading) and deleting video assets for ad accounts
- Supports both regular upload and chunked upload for large video files
- Includes specialized support for 360° videos and slideshow creation
- Reading and updating operations are not supported on this endpoint
- Comprehensive error handling with specific error codes for different failure scenarios

## API Endpoints
- `POST /act_{ad_account_id}/advideos`
- `DELETE /act_{ad_account_id}/advideos`

## Parameters
- name
- title
- description
- source
- file_url
- file_size
- upload_phase
- upload_session_id
- start_offset
- end_offset
- video_file_chunk
- slideshow_spec
- original_projection_type
- unpublished_content_type
- video_id

## Content
# Ad Videos

The Ad Videos endpoint allows you to manage video assets within Facebook ad accounts. This endpoint supports creating (uploading) and deleting video content for use in advertising campaigns.

## Operations Overview

### Reading
Reading operations are not supported on this endpoint.

### Creating
You can upload videos by making a POST request to the `advideos` edge:
- **Endpoint**: `/act_{ad_account_id}/advideos`
- **Creates**: A Video object

#### Parameters

**Basic Video Parameters:**
- `name` (string): The name of the video in the library
- `title` (UTF-8 string): The name of the video being uploaded (max 255 characters, supports emoji)
- `description` (UTF-8 string): Video description (supports emoji)
- `source` (string): The video file encoded as form data
- `file_url` (string): URL to video file
- `file_size` (int64): Size of video file in bytes (for chunked upload)

**Chunked Upload Parameters:**
- `upload_phase` (enum): Phase during chunked upload - `start`, `transfer`, `finish`, `cancel`
- `upload_session_id` (numeric string): Session ID for chunked upload
- `start_offset` (int64): Start position in bytes of chunk being sent
- `end_offset` (int64): End position in bytes of chunk
- `video_file_chunk` (string): The chunk of video data

**360° Video Parameters:**
- `fisheye_video_cropped` (boolean): Whether single fisheye video is cropped
- `front_z_rotation` (float): Front z rotation in degrees
- `original_fov` (int64): Original field of view of source camera
- `original_projection_type` (enum): `equirectangular`, `cubemap`, `half_equirectangular`

**Slideshow Parameters:**
- `slideshow_spec` (JSON object):
  - `images_urls` (list<URL>): 3-7 element array of image URLs (required)
  - `duration_ms` (integer): Duration of each image in milliseconds (default: 1000)
  - `transition_ms` (integer): Crossfade transition duration (default: 1000)
  - `reordering_opt_in` (boolean): Default false
  - `music_variations_opt_in` (boolean): Default false

**Other Parameters:**
- `unpublished_content_type` (enum): Content type - `SCHEDULED`, `SCHEDULED_RECURRING`, `DRAFT`, `ADS_POST`, `INLINE_CREATED`, `PUBLISHED`, `REVIEWABLE_BRANDED_CONTENT`
- `transcode_setting_properties` (string): Properties for computing transcode settings
- `composer_session_id` (string): Session identifier
- `time_since_original_post` (int64): Time since original post

#### Return Type
```json
{
  "id": "numeric string",
  "upload_session_id": "numeric string",
  "video_id": "numeric string",
  "start_offset": "numeric string",
  "end_offset": "numeric string",
  "success": "bool",
  "skip_upload": "bool",
  "upload_domain": "string",
  "region_hint": "string",
  "xpv_asset_id": "numeric string",
  "is_xpv_single_prod": "bool",
  "transcode_bit_rate_bps": "numeric string",
  "transcode_dimension": "numeric string",
  "should_expand_to_transcode_dimension": "bool",
  "action_id": "string",
  "gop_size_seconds": "numeric string",
  "target_video_codec": "string",
  "target_hdr": "string",
  "maximum_frame_rate": "numeric string"
}
```

### Updating
Update operations are not supported on this endpoint.

### Deleting
You can remove a video from an ad account by making a DELETE request:
- **Endpoint**: `/act_{ad_account_id}/advideos`

#### Parameters
- `video_id` (video ID): Ad account library video ID (required)

#### Return Type
```json
{
  "success": "bool"
}
```

## Error Codes

### Create Operation Errors
- **381**: Problem uploading video file
- **100**: Invalid parameter
- **222**: Video not visible
- **389**: Unable to fetch video file from URL
- **352**: Unsupported video format
- **200**: Permissions error
- **382**: Video file too small
- **190**: Invalid OAuth 2.0 Access Token
- **6001**: General video upload problem

### Delete Operation Errors
- **100**: Invalid parameter
- **368**: Action deemed abusive or disallowed
- **613**: Rate limit exceeded

## Examples
Chunked upload workflow using upload_phase, upload_session_id, and video_file_chunk

Slideshow creation with images_urls array and timing parameters

360° video upload with projection type and field of view settings

---
**Tags:** Facebook Marketing API, Video Upload, Ad Videos, Chunked Upload, 360 Video, Slideshow, Video Management  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/advideos/  
**Processed:** 2025-06-25T16:23:05.732Z