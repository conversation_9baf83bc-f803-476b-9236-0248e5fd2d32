# Insights API

On This Page

[Insights API](#insights-api)

[Before you begin](#before-you-begin)

[Campaign Statistics](#campaign-statistics)

[Making Calls](#makingacall)

[Request](#request)

[Response](#response)

[Levels](#levels)

[Request](#request-2)

[Response](#response-2)

[Attribution windows](#attribution-windows)

[Field Expansion](#field-expansion)

[Request](#request-3)

[Response](#response-3)

[Sorting](#sorting)

[Request](#request-4)

[Response](#response-4)

[Ads Labels](#ads-labels)

[Request](#request-5)

[Response](#response-5)

[Clicks definition](#clicks-definition)

[Deleted and Archived Objects](#deleted-and-archived-objects)

[Request](#request-6)

[Response](#response-6)

[Deleted Objects Insights](#deleted-objects-insights)

[Troubleshooting](#troubleshooting)

[Timeouts](#timeouts)

[Rate Limiting](#rate-limiting)

[Discrepancy with Ads Manager](#discrepancy-with-ads-manager)

[Learn More](#learn-more)

# Insights API

Provides a single, consistent interface to retrieve ad statistics.

*   [Breakdowns](/docs/marketing-api/insights/breakdowns) - Group results
*   [Action Breakdowns](/docs/marketing-api/insights/action-breakdowns) - Understanding the response from action breakdowns.
*   [Async Jobs](/docs/marketing-api/insights/async) - For requests with large results, use asynchronous jobs
*   [Limits and Best Practices](/docs/marketing-api/insights/best-practices/) - Call limits, filtering and best practices.

Before you can get data on your ad's performance, you should set up your ads to track the metrics you are interested in. For that, you can use [URL Tags](/docs/reference/ads-api/adcreative), [Meta Pixel](/docs/marketing-api/audiences-api/pixel), and the [Conversions API](/docs/marketing-api/conversions-api).

## Before you begin

You will need:

*   The `ads_read` permission.
*   An [app](https://developers.facebook.com/apps/). See [Meta App Development](/docs/development) for more information.

[](#)

## Campaign Statistics

To get the statistics of a campaign's last 7 day performance:

```
curl \-G \\
  \-d "date\_preset=last\_7d" \\
  \-d "access\_token=ACCESS\_TOKEN" \\
  "https://graph.facebook.com/API\_VERSION/AD\_CAMPAIGN\_ID/insights"
```

To learn more, see the [Ad Insights Reference](/docs/marketing-api/insights).

[](#)

## Making Calls

The Insights API is available as an edge on any ads object.

API Method

[`act_<AD_ACCOUNT_ID>/insights`](/docs/marketing-api/reference/ad-account/insights)

[`<CAMPAIGN_ID>/insights`](/docs/marketing-api/reference/ad-campaign-group/insights)

[`<ADSET_ID>/insights`](/docs/marketing-api/reference/ad-campaign/insights)

[`<AD_ID>/insights`](/docs/marketing-api/reference/adgroup/insights)

### Request

You can request specific fields with a comma-separated list in the `fields` parameters. For example:

```
curl \-G \\
\-d "fields=impressions" \\
\-d "access\_token=ACCESS\_TOKEN" \\
"https://graph.facebook.com/`v23.0`/<AD\_ID>/insights"
    
```

### Response

```
{
  "data": \[
    {
      "impressions": "2466376",
      "date\_start": "2009-03-28",
      "date\_stop": "2016-04-01"
    }
  \],
  "paging": {
    "cursors": {
      "before": "MAZDZD",
      "after": "MAZDZD"
    }
  }
}
```

[](#)

## Levels

Aggregate results at a defined object level. This automatically deduplicates data.

### Request

For example, get a campaign's insights on ad level.

```
curl \-G \\
\-d "level=ad" \\
\-d "fields=impressions,ad\_id" \\
\-d "access\_token=ACCESS\_TOKEN" \\
"https://graph.facebook.com/`v23.0`/CAMPAIGN\_ID/insights"
```

### Response

```
{
  "data": \[
    {
      "impressions": "9708",
      "ad\_id": "6142546123068",
      "date\_start": "2009-03-28",
      "date\_stop": "2016-04-01"
    },
    {
      "impressions": "18841",
      "ad\_id": "*************",
      "date\_start": "2009-03-28",
      "date\_stop": "2016-04-01"
    }
  \],
  "paging": {
    "cursors": {
      "before": "MAZDZD",
      "after": "MQZDZD"
    }
  }
}
```

If you don't have access to all ad objects at the requested level, the insights call returns no data. For example, while requesting insights with `level` set to `ad`, if you don't have access to one or more ad objects under the ad account, this API call will return a permission error.

[](#)

## Attribution windows

The **conversion attribution window** provides timeframes that define when we attribute an event to an ad on a Meta app. For background information, see [Meta Business Help Center, About attribution windows](https://www.facebook.com/business/help/****************). We measure the actions that occur when a conversion event occurs and look back in time 1-day and 7-days. To view actions attributed to different attribution windows, make a request to `/{ad-account-id}/insights`. If you do not provide `action_attribution_windows` we use `7d_click` and provide it under `value`.

For example specify `action_attribution_windows` and 'value' is fixed at `7d_click` attribution window. Make a request to `act_10151816772662695/insights?action_attribution_windows=['1d_click','1d_view']` and get this result:

```
"spend": 2352.45,
"actions": \[
{
"action\_type": "link\_click",
"value": 6608,
"1d\_view": 86,
"1d\_click": 6510
},
"cost\_per\_action\_type": \[
{
"action\_type": "link\_click",
"value": 0.**************,
"1d\_view": 27.************,
"1d\_click": 0.**************
},

// if attribution window is \_not\_ specified in query. And note that the number under 'value' key is the same even if attribution window is specified.
// act\_10151816772662695/insights
"spend": 2352.45,
"actions": \[
{
"action\_type": "link\_click",
"value": 6608
},
"cost\_per\_action\_type": \[
{
"action\_type": "link\_click",
"value": 0.**************
},
```

[](#)

## Field Expansion

Request fields at the node level and by fields specified in [field expansion](/docs/graph-api/using-graph-api/#field-expansion).

### Request

```
curl \-G \\
\-d "fields=insights{impressions}" \\
\-d "access\_token=ACCESS\_TOKEN" \\
"https://graph.facebook.com/`v23.0`/AD\_ID"
```

### Response

```
{
  "id": "6042542123268",
  "name": "My Website Clicks Ad",
  "insights": {
    "data": \[
      {
        "impressions": "9708",
        "date\_start": "2016-03-06",
        "date\_stop": "2016-04-01"
      }
    \],
    "paging": {
      "cursors": {
        "before": "MAZDZD",
        "after": "MAZDZD"
      }
    }
  }
}
```

[](#)

## Sorting

Sort results by providing the `sort` parameter with `{fieldname}_descending` or `{fieldname}_ascending`:

### Request

```
curl \-G \\
\-d "sort=reach\_descending" \\
\-d "level=ad" \\
\-d "fields=reach" \\
\-d "access\_token=ACCESS\_TOKEN" \\
"https://graph.facebook.com/`v23.0`/AD\_SET\_ID/insights"
```

### Response

```

{
  "data": \[
    {
      "reach": 10742,
      "date\_start": "2009-03-28",
      "date\_stop": "2016-04-01"
    },
    {
      "reach": 5630,
      "date\_start": "2009-03-28",
      "date\_stop": "2016-04-03"
    },
    {
      "reach": 3231,
      "date\_start": "2009-03-28",
      "date\_stop": "2016-04-02"
    },
    {
      "reach": 936,
      "date\_start": "2009-03-29",
      "date\_stop": "2016-04-02"
    }
  \],
  "paging": {
    "cursors": {
      "before": "MAZDZD",
      "after": "MQZDZD"
    }
  }
}
```

[](#)

## Ads Labels

Stats for all labels whose names are identical. Aggregated into a single value at an ad object level. See the [Ads Labels Reference](/docs/marketing-api/reference/ad-label) for more information.

### Request

```
curl \-G \\  
\-d "fields=id,name,insights{unique\_clicks,cpm,total\_actions}" \\
\-d "level=ad" \\
\-d 'filtering=\[{"field":"ad.adlabels","operator":"ANY", "value":\["Label Name"\]}\]'  \\
\-d 'time\_range={"since":"2015-03-01","until":"2015-03-31"}' \\
\-d "access\_token=ACCESS\_TOKEN" \\
"https://graph.facebook.com/`v23.0`/AD\_OBJECT\_ID/insights"
```

### Response

```
{
  "data": \[
    {
      "unique\_clicks": 74,
      "cpm": 0.81081081081081,
      "total\_actions": 49,
      "date\_start": "2015-03-01",
      "date\_stop": "2015-03-31",
    },
  \], 
  "paging": {
    "cursors": {
      "before": "MA==",
      "after": "MA==",
    }
  }
}
```

[](#)

## Clicks definition

To better understand the click metrics that Meta offers today, please read the definitions and usage of each below:

*   **Link Clicks, `actions:link_click`** - The number of clicks on ad links to select destinations or experiences, on or off Meta-owned properties. See [Ads Help Center, Link Clicks](https://www.facebook.com/business/help/659185130844708)
    
*   **Clicks (All), `clicks`** - The metric counts multiple types of clicks on your ad, including certain types of interactions with the ad container, links to other destinations, and links to expanded ad experiences. See [Ads Help Center, Clicks(All)](https://www.facebook.com/business/help/787506997938504)
    

[](#)

## Deleted and Archived Objects

Ad units may be `DELETED` or `ARCHIVED`. The stats of deleted or archived objects appear when you query their parents. This means if you query `impressions` at the ad set level, results include `impressions` from all ads in the set it, regardless of whether the the ads are in a deleted or archived state. See also, [Storing and Retrieving Ad Objects Best Practice](/docs/marketing-api/best-practices/storing_adobjects).

However, if you query using filtering, status filtering will be applied by default to return only Active objects. As a result, the total stats of the parent node may be greater than the stats of its children.

You can get the stats of `ARCHIVED` objects from their parent nodes though, by providing an extra `filtering` parameter.

### Request

To get the stats of all `ARCHIVED` ads in an ad account listed one by one:

```
curl \-G \\
  \-d "level=ad" \\
  \-d "filtering=\[{'field':'ad.effective\_status','operator':'IN','value':\['ARCHIVED'\]}\]" \\
  \-d "access\_token=<ACCESS\_TOKEN>" \\
  "https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/insights/"
```

### Response

Note that only archived objects are returned in this response.

```
{
  "data": \[
    {
      "impressions": "1741",
      "date\_start": "2016-03-11",
      "date\_stop": "2016-03-12"
    }
  \],
  "paging": {
    "cursors": {
      "before": "MAZDZD",
      "after": "MAZDZD"
    }
  }
}
```

### Deleted Objects Insights

You can query insights on deleted objects if you have their IDs or by using the `ad.effective_status` filter.

### Request

For example, if you have the ad set ID:

```
curl \-G \\
\-d "fields=id,name,status,insights{impressions}" \\
\-d "access\_token=ACCESS\_TOKEN" \\
"https://graph.facebook.com/`v23.0`/AD\_SET\_ID"
    
```

In this example, we query with `ad.effective_status`:

```
POST https://graph.facebook.com/<VERSION>/act\_ID/insights?access\_token=token&appsecret\_proof=proof&fields=ad\_id,impressions&date\_preset=lifetime&level=ad&filtering=\[{"field":"ad.effective\_status","operator":"IN","value":\["DELETED"\]}\]
```

### Response

```
{
  "id": "6042147342661",
  "name": "My Like Campaign",
  "status": "DELETED",
  "insights": {
    "data": \[
      {
        "impressions": "1741",
        "date\_start": "2016-03-11",
        "date\_stop": "2016-03-12"
      }
    \],
    "paging": {
      "cursors": {
        "before": "MAZDZD",
        "after": "MAZDZD"
      }
    }
  }
}
```

[](#)

## Troubleshooting

### Timeouts

The most common issues causing failure at this endpoint are too many requests and time outs:

*   On `/GET` or synchronous requests, you can get out-of-memory or timeout errors.
*   On `/POST` or asynchronous requests, you can possibly get timeout errors. For asynchronous requests, it can take up to an hour to complete a request including retry attempts. For example if you make a query that tries to fetch large volume of data for many ad level objects.

#### Recommendations

*   There is no explicit limit for when a query will fail. When it times out, try to break down the query into smaller queries by putting in filters like date range.
*   Unique metrics are time consuming to compute. Try to query unique metrics in a separate call to improve performance of non-unique metrics.

### Rate Limiting

The Meta Insights API utilizes rate limiting to ensure an optimal reporting experience for all of our partners. For more information and suggestions, see our Insights API [Limits & Best Practices](/docs/marketing-api/insights/best-practices/).

### Discrepancy with Ads Manager

Beginning June 10, 2025, to reduce discrepancies with Meta Ads Manager, `use_unified_attribution_setting` and `action_report_time parameters` will be disregarded and API responses will mimic Ads Manager settings:

*   Attributed `value`s will be based on Ad-Set-level attribution settings (similar to `use_unified_attribution_setting=true`), and inline/on-ad actions will be included in `1d_click` or `1d_view` attribution window data. After this change, standalone `inline` attribution window data will no longer be returned.
*   Actions will be reported using `action_report_time=mixed`: on-Meta actions (like Link Clicks) will use impression-based reporting time; whereas off-Meta actions (like Web Purchases) will leverage conversion-based reporting time.

The default behavior of the API is different from the default behavior in Ads Manager. If you would like to observe the same behavior as in Ads Manager, please set the field `use_unified_attribution_setting` to true.

[](#)

## Learn More

*   [Ad Account Insights](/docs/marketing-api/reference/ad-account/insights)
*   [Ad Campaign Insights](/docs/marketing-api/reference/ad-campaign-group/insights)
*   [Ad Set Insights](/docs/marketing-api/reference/ad-campaign/insights)
*   [Ad Insights](/docs/marketing-api/reference/adgroup/insights/)

Any endpoints not in the above list are not covered in this API. If you plan to include reports from Meta in your solution, see [Meta Platform Terms](/terms) and [Developer Policies for Marketing API](/devpolicy/#marketingapi).

[](#)

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '***************'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)