{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\pages\\\\Landing.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { Zap, MessageSquare, Target, Sparkles, Star, ArrowRight, Phone, Mail, MapPin, Shield, BarChart3, Facebook, User, Eye, EyeOff } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Landing = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  } = useAuth();\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Redirect to dashboard if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n  const onLogin = async data => {\n    setLoading(true);\n    const result = await login(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    const result = await register(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      if (response.data.oauthUrl) {\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      if (state !== storedState) {\n        throw new Error('Invalid state parameter');\n      }\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.user && response.data.tokens) {\n        const {\n          user: userData,\n          tokens\n        } = response.data;\n        localStorage.setItem('accessToken', tokens.accessToken);\n        localStorage.setItem('refreshToken', tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(userData));\n        localStorage.removeItem('facebook_oauth_state');\n        toast.success('Facebook login successful!');\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      window.history.replaceState({}, document.title, '/landing');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\uD83D\\uDE80 Pressure Max\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: \"Powerful Facebook Marketing API Integration & Campaign Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-description\",\n            children: \"Streamline your Facebook advertising with our comprehensive API testing interface, campaign management tools, and real-time analytics dashboard.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(Shield, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Secure Authentication\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(Zap, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Real-time API Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Campaign Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"auth-tabs\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: activeTab === 'facebook' ? 'active' : '',\n                onClick: () => setActiveTab('facebook'),\n                children: [/*#__PURE__*/_jsxDEV(Facebook, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), \"Facebook Login\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: activeTab === 'login' ? 'active' : '',\n                onClick: () => setActiveTab('login'),\n                children: \"Email Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: activeTab === 'register' ? 'active' : '',\n                onClick: () => setActiveTab('register'),\n                children: \"Register\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"facebook-login-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"facebook-login-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [/*#__PURE__*/_jsxDEV(Facebook, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this), \"Quick Facebook Login\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Connect with your Facebook account to access:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Automatic Marketing API permissions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Campaign management tools\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Real-time analytics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Lead form integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleFacebookLogin,\n                disabled: loading,\n                className: \"facebook-login-btn\",\n                children: [/*#__PURE__*/_jsxDEV(Facebook, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this), loading ? 'Connecting...' : 'Continue with Facebook', /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"facebook-note\",\n                children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"***************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 35\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 63\n                }, this), \"This will automatically grant marketing API permissions.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: loginForm.handleSubmit(onLogin),\n              className: \"auth-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  ...loginForm.register('email', {\n                    required: 'Email is required'\n                  }),\n                  placeholder: \"Enter your email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: loginForm.formState.errors.email.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  ...loginForm.register('password', {\n                    required: 'Password is required'\n                  }),\n                  placeholder: \"Enter your password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 21\n                }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: loginForm.formState.errors.password.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"submit-btn\",\n                children: [loading ? 'Logging in...' : 'Login', /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: registerForm.handleSubmit(onRegister),\n              className: \"auth-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"First Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  ...registerForm.register('firstName', {\n                    required: 'First name is required'\n                  }),\n                  placeholder: \"Enter your first name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.firstName.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  ...registerForm.register('lastName', {\n                    required: 'Last name is required'\n                  }),\n                  placeholder: \"Enter your last name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.lastName.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  ...registerForm.register('email', {\n                    required: 'Email is required'\n                  }),\n                  placeholder: \"Enter your email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.email.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  ...registerForm.register('password', {\n                    required: 'Password is required',\n                    minLength: {\n                      value: 6,\n                      message: 'Password must be at least 6 characters'\n                    }\n                  }),\n                  placeholder: \"Enter your password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.password.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"submit-btn\",\n                children: [loading ? 'Creating Account...' : 'Create Account', /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"landing-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Pressure Max API Testing Interface - Built with React & Supabase\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Backend API: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"http://localhost:3000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(Landing, \"m+c3JY+ZwG46FhJ9RD1ikQibAso=\", false, function () {\n  return [useAuth, useNavigate, useForm, useForm];\n});\n_c = Landing;\nexport default Landing;\nvar _c;\n$RefreshReg$(_c, \"Landing\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useNavigate", "useForm", "facebookAPI", "Zap", "MessageSquare", "Target", "<PERSON><PERSON><PERSON>", "Star", "ArrowRight", "Phone", "Mail", "MapPin", "Shield", "BarChart3", "Facebook", "User", "Eye", "Eye<PERSON>ff", "toast", "jsxDEV", "_jsxDEV", "Landing", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "navigate", "activeTab", "setActiveTab", "showToken", "setShowToken", "loading", "setLoading", "loginForm", "registerForm", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "handleFacebookCallback", "onLogin", "data", "result", "success", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "response", "getOAuthUrl", "oauthUrl", "localStorage", "setItem", "href", "Error", "error", "message", "storedState", "getItem", "handleOAuthCallback", "tokens", "userData", "accessToken", "refreshToken", "JSON", "stringify", "removeItem", "history", "replaceState", "document", "title", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "Users", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "email", "password", "firstName", "lastName", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/pages/Landing.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport {\n  Zap,\n  MessageSquare,\n  Target,\n  Sparkles,\n  Star,\n  ArrowRight,\n  Phone,\n  Mail,\n  MapPin,\n  Shield,\n  BarChart3,\n  Facebook,\n  User,\n  Eye,\n  EyeOff\n} from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst Landing = () => {\n  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  // Redirect to dashboard if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    \n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    const result = await login(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    const result = await register(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      \n      if (response.data.oauthUrl) {\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      \n      if (state !== storedState) {\n        throw new Error('Invalid state parameter');\n      }\n\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      \n      if (response.data.user && response.data.tokens) {\n        const { user: userData, tokens } = response.data;\n        \n        localStorage.setItem('accessToken', tokens.accessToken);\n        localStorage.setItem('refreshToken', tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(userData));\n        localStorage.removeItem('facebook_oauth_state');\n        \n        toast.success('Facebook login successful!');\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      window.history.replaceState({}, document.title, '/landing');\n    }\n  };\n\n  return (\n    <div className=\"landing-page\">\n      {/* Hero Section */}\n      <div className=\"hero-section\">\n        <div className=\"hero-content\">\n          <div className=\"hero-text\">\n            <h1>🚀 Pressure Max</h1>\n            <p className=\"hero-subtitle\">\n              Powerful Facebook Marketing API Integration & Campaign Management\n            </p>\n            <p className=\"hero-description\">\n              Streamline your Facebook advertising with our comprehensive API testing interface, \n              campaign management tools, and real-time analytics dashboard.\n            </p>\n            \n            <div className=\"features-grid\">\n              <div className=\"feature-item\">\n                <Shield size={24} />\n                <span>Secure Authentication</span>\n              </div>\n              <div className=\"feature-item\">\n                <Zap size={24} />\n                <span>Real-time API Testing</span>\n              </div>\n              <div className=\"feature-item\">\n                <Users size={24} />\n                <span>Campaign Management</span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"auth-container\">\n            <div className=\"auth-card\">\n              <h2>Get Started</h2>\n              \n              <div className=\"auth-tabs\">\n                <button \n                  className={activeTab === 'facebook' ? 'active' : ''}\n                  onClick={() => setActiveTab('facebook')}\n                >\n                  <Facebook size={16} />\n                  Facebook Login\n                </button>\n                <button \n                  className={activeTab === 'login' ? 'active' : ''}\n                  onClick={() => setActiveTab('login')}\n                >\n                  Email Login\n                </button>\n                <button \n                  className={activeTab === 'register' ? 'active' : ''}\n                  onClick={() => setActiveTab('register')}\n                >\n                  Register\n                </button>\n              </div>\n\n              {activeTab === 'facebook' ? (\n                <div className=\"facebook-login-section\">\n                  <div className=\"facebook-login-info\">\n                    <h3>\n                      <Facebook size={20} />\n                      Quick Facebook Login\n                    </h3>\n                    <p>Connect with your Facebook account to access:</p>\n                    <ul>\n                      <li>✅ Automatic Marketing API permissions</li>\n                      <li>✅ Campaign management tools</li>\n                      <li>✅ Real-time analytics</li>\n                      <li>✅ Lead form integration</li>\n                    </ul>\n                  </div>\n                  \n                  <button\n                    onClick={handleFacebookLogin}\n                    disabled={loading}\n                    className=\"facebook-login-btn\"\n                  >\n                    <Facebook size={20} />\n                    {loading ? 'Connecting...' : 'Continue with Facebook'}\n                    <ArrowRight size={16} />\n                  </button>\n                  \n                  <p className=\"facebook-note\">\n                    Using App ID: <code>***************</code><br/>\n                    This will automatically grant marketing API permissions.\n                  </p>\n                </div>\n              ) : activeTab === 'login' ? (\n                <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n                  <div className=\"form-group\">\n                    <label>Email:</label>\n                    <input\n                      type=\"email\"\n                      {...loginForm.register('email', { required: 'Email is required' })}\n                      placeholder=\"Enter your email\"\n                    />\n                    {loginForm.formState.errors.email && (\n                      <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Password:</label>\n                    <input\n                      type=\"password\"\n                      {...loginForm.register('password', { required: 'Password is required' })}\n                      placeholder=\"Enter your password\"\n                    />\n                    {loginForm.formState.errors.password && (\n                      <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n                    )}\n                  </div>\n\n                  <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n                    {loading ? 'Logging in...' : 'Login'}\n                    <ArrowRight size={16} />\n                  </button>\n                </form>\n              ) : (\n                <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n                  <div className=\"form-group\">\n                    <label>First Name:</label>\n                    <input\n                      type=\"text\"\n                      {...registerForm.register('firstName', { required: 'First name is required' })}\n                      placeholder=\"Enter your first name\"\n                    />\n                    {registerForm.formState.errors.firstName && (\n                      <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Last Name:</label>\n                    <input\n                      type=\"text\"\n                      {...registerForm.register('lastName', { required: 'Last name is required' })}\n                      placeholder=\"Enter your last name\"\n                    />\n                    {registerForm.formState.errors.lastName && (\n                      <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Email:</label>\n                    <input\n                      type=\"email\"\n                      {...registerForm.register('email', { required: 'Email is required' })}\n                      placeholder=\"Enter your email\"\n                    />\n                    {registerForm.formState.errors.email && (\n                      <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Password:</label>\n                    <input\n                      type=\"password\"\n                      {...registerForm.register('password', { \n                        required: 'Password is required',\n                        minLength: { value: 6, message: 'Password must be at least 6 characters' }\n                      })}\n                      placeholder=\"Enter your password\"\n                    />\n                    {registerForm.formState.errors.password && (\n                      <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n                    )}\n                  </div>\n\n                  <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n                    {loading ? 'Creating Account...' : 'Create Account'}\n                    <ArrowRight size={16} />\n                  </button>\n                </form>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"landing-footer\">\n        <p>Pressure Max API Testing Interface - Built with React & Supabase</p>\n        <p>Backend API: <code>http://localhost:3000</code></p>\n      </footer>\n    </div>\n  );\n};\n\nexport default Landing;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SACEC,GAAG,EACHC,aAAa,EACbC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,MAAM,QACD,cAAc;AACrB,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAC9E,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMuC,SAAS,GAAGnC,OAAO,CAAC,CAAC;EAC3B,MAAMoC,YAAY,GAAGpC,OAAO,CAAC,CAAC;;EAE9B;EACAH,SAAS,CAAC,MAAM;IACd,IAAI0B,eAAe,EAAE;MACnBK,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACL,eAAe,EAAEK,QAAQ,CAAC,CAAC;;EAE/B;EACA/B,SAAS,CAAC,MAAM;IACd,MAAMwC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpC,IAAID,IAAI,IAAIE,KAAK,IAAI,CAACrB,eAAe,EAAE;MACrCsB,sBAAsB,CAACH,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACrB,eAAe,CAAC,CAAC;EAErB,MAAMuB,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9Bb,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMc,MAAM,GAAG,MAAMxB,KAAK,CAACuB,IAAI,CAAC;IAChC,IAAIC,MAAM,CAACC,OAAO,EAAE;MAClBrB,QAAQ,CAAC,YAAY,CAAC;IACxB;IACAM,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMgB,UAAU,GAAG,MAAOH,IAAI,IAAK;IACjCb,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMc,MAAM,GAAG,MAAMvB,QAAQ,CAACsB,IAAI,CAAC;IACnC,IAAIC,MAAM,CAACC,OAAO,EAAE;MAClBrB,QAAQ,CAAC,YAAY,CAAC;IACxB;IACAM,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMiB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,WAAW,GAAG,GAAGb,MAAM,CAACC,QAAQ,CAACa,MAAM,UAAU;MACvD,MAAMC,QAAQ,GAAG,MAAMrD,WAAW,CAACsD,WAAW,CAACH,WAAW,CAAC;MAE3D,IAAIE,QAAQ,CAACP,IAAI,CAACS,QAAQ,EAAE;QAC1BC,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEJ,QAAQ,CAACP,IAAI,CAACH,KAAK,CAAC;QACjEL,MAAM,CAACC,QAAQ,CAACmB,IAAI,GAAGL,QAAQ,CAACP,IAAI,CAACS,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3B,UAAU,CAAC,KAAK,CAAC;MACjBjB,KAAK,CAAC4C,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAACC,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMjB,sBAAsB,GAAG,MAAAA,CAAOH,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACFV,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,WAAW,GAAGN,YAAY,CAACO,OAAO,CAAC,sBAAsB,CAAC;MAEhE,IAAIpB,KAAK,KAAKmB,WAAW,EAAE;QACzB,MAAM,IAAIH,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MAEA,MAAMR,WAAW,GAAG,GAAGb,MAAM,CAACC,QAAQ,CAACa,MAAM,UAAU;MACvD,MAAMC,QAAQ,GAAG,MAAMrD,WAAW,CAACgE,mBAAmB,CAACvB,IAAI,EAAEE,KAAK,EAAEQ,WAAW,CAAC;MAEhF,IAAIE,QAAQ,CAACP,IAAI,CAACzB,IAAI,IAAIgC,QAAQ,CAACP,IAAI,CAACmB,MAAM,EAAE;QAC9C,MAAM;UAAE5C,IAAI,EAAE6C,QAAQ;UAAED;QAAO,CAAC,GAAGZ,QAAQ,CAACP,IAAI;QAEhDU,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEQ,MAAM,CAACE,WAAW,CAAC;QACvDX,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEQ,MAAM,CAACG,YAAY,CAAC;QACzDZ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEY,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,CAAC;QACtDV,YAAY,CAACe,UAAU,CAAC,sBAAsB,CAAC;QAE/CvD,KAAK,CAACgC,OAAO,CAAC,4BAA4B,CAAC;QAC3CrB,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd3B,UAAU,CAAC,KAAK,CAAC;MACjBjB,KAAK,CAAC4C,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAACC,OAAO,CAAC;MACtDvB,MAAM,CAACkC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,EAAE,UAAU,CAAC;IAC7D;EACF,CAAC;EAED,oBACEzD,OAAA;IAAK0D,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3B3D,OAAA;MAAK0D,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3B3D,OAAA;QAAK0D,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B3D,OAAA;UAAK0D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB3D,OAAA;YAAA2D,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB/D,OAAA;YAAG0D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ/D,OAAA;YAAG0D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAGhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ/D,OAAA;YAAK0D,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3D,OAAA,CAACR,MAAM;gBAACwE,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB/D,OAAA;gBAAA2D,QAAA,EAAM;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACN/D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3D,OAAA,CAACjB,GAAG;gBAACiF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjB/D,OAAA;gBAAA2D,QAAA,EAAM;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACN/D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B3D,OAAA,CAACiE,KAAK;gBAACD,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnB/D,OAAA;gBAAA2D,QAAA,EAAM;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/D,OAAA;UAAK0D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B3D,OAAA;YAAK0D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB3D,OAAA;cAAA2D,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEpB/D,OAAA;cAAK0D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3D,OAAA;gBACE0D,SAAS,EAAEhD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;gBACpDwD,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC,UAAU,CAAE;gBAAAgD,QAAA,gBAExC3D,OAAA,CAACN,QAAQ;kBAACsE,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAExB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA;gBACE0D,SAAS,EAAEhD,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;gBACjDwD,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC,OAAO,CAAE;gBAAAgD,QAAA,EACtC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA;gBACE0D,SAAS,EAAEhD,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;gBACpDwD,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAAC,UAAU,CAAE;gBAAAgD,QAAA,EACzC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELrD,SAAS,KAAK,UAAU,gBACvBV,OAAA;cAAK0D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC3D,OAAA;gBAAK0D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC3D,OAAA;kBAAA2D,QAAA,gBACE3D,OAAA,CAACN,QAAQ;oBAACsE,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,wBAExB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/D,OAAA;kBAAA2D,QAAA,EAAG;gBAA6C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpD/D,OAAA;kBAAA2D,QAAA,gBACE3D,OAAA;oBAAA2D,QAAA,EAAI;kBAAqC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9C/D,OAAA;oBAAA2D,QAAA,EAAI;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpC/D,OAAA;oBAAA2D,QAAA,EAAI;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9B/D,OAAA;oBAAA2D,QAAA,EAAI;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEN/D,OAAA;gBACEkE,OAAO,EAAElC,mBAAoB;gBAC7BmC,QAAQ,EAAErD,OAAQ;gBAClB4C,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAE9B3D,OAAA,CAACN,QAAQ;kBAACsE,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrBjD,OAAO,GAAG,eAAe,GAAG,wBAAwB,eACrDd,OAAA,CAACZ,UAAU;kBAAC4E,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAET/D,OAAA;gBAAG0D,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,gBACb,eAAA3D,OAAA;kBAAA2D,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAAA/D,OAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,4DAEjD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,GACJrD,SAAS,KAAK,OAAO,gBACvBV,OAAA;cAAMoE,QAAQ,EAAEpD,SAAS,CAACqD,YAAY,CAAC1C,OAAO,CAAE;cAAC+B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACpE3D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAA2D,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB/D,OAAA;kBACEsE,IAAI,EAAC,OAAO;kBAAA,GACRtD,SAAS,CAACV,QAAQ,CAAC,OAAO,EAAE;oBAAEiE,QAAQ,EAAE;kBAAoB,CAAC,CAAC;kBAClEC,WAAW,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,EACD/C,SAAS,CAACyD,SAAS,CAACC,MAAM,CAACC,KAAK,iBAC/B3E,OAAA;kBAAM0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE3C,SAAS,CAACyD,SAAS,CAACC,MAAM,CAACC,KAAK,CAAChC;gBAAO;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACzE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAA2D,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxB/D,OAAA;kBACEsE,IAAI,EAAC,UAAU;kBAAA,GACXtD,SAAS,CAACV,QAAQ,CAAC,UAAU,EAAE;oBAAEiE,QAAQ,EAAE;kBAAuB,CAAC,CAAC;kBACxEC,WAAW,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EACD/C,SAAS,CAACyD,SAAS,CAACC,MAAM,CAACE,QAAQ,iBAClC5E,OAAA;kBAAM0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE3C,SAAS,CAACyD,SAAS,CAACC,MAAM,CAACE,QAAQ,CAACjC;gBAAO;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC5E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/D,OAAA;gBAAQsE,IAAI,EAAC,QAAQ;gBAACH,QAAQ,EAAErD,OAAQ;gBAAC4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAC5D7C,OAAO,GAAG,eAAe,GAAG,OAAO,eACpCd,OAAA,CAACZ,UAAU;kBAAC4E,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAEP/D,OAAA;cAAMoE,QAAQ,EAAEnD,YAAY,CAACoD,YAAY,CAACtC,UAAU,CAAE;cAAC2B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAC1E3D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAA2D,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1B/D,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBAAA,GACPrD,YAAY,CAACX,QAAQ,CAAC,WAAW,EAAE;oBAAEiE,QAAQ,EAAE;kBAAyB,CAAC,CAAC;kBAC9EC,WAAW,EAAC;gBAAuB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,EACD9C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACG,SAAS,iBACtC7E,OAAA;kBAAM0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE1C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACG,SAAS,CAAClC;gBAAO;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAChF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAA2D,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzB/D,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBAAA,GACPrD,YAAY,CAACX,QAAQ,CAAC,UAAU,EAAE;oBAAEiE,QAAQ,EAAE;kBAAwB,CAAC,CAAC;kBAC5EC,WAAW,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACD9C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACI,QAAQ,iBACrC9E,OAAA;kBAAM0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE1C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACI,QAAQ,CAACnC;gBAAO;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAA2D,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrB/D,OAAA;kBACEsE,IAAI,EAAC,OAAO;kBAAA,GACRrD,YAAY,CAACX,QAAQ,CAAC,OAAO,EAAE;oBAAEiE,QAAQ,EAAE;kBAAoB,CAAC,CAAC;kBACrEC,WAAW,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,EACD9C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACC,KAAK,iBAClC3E,OAAA;kBAAM0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE1C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACC,KAAK,CAAChC;gBAAO;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC5E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/D,OAAA;gBAAK0D,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3D,OAAA;kBAAA2D,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxB/D,OAAA;kBACEsE,IAAI,EAAC,UAAU;kBAAA,GACXrD,YAAY,CAACX,QAAQ,CAAC,UAAU,EAAE;oBACpCiE,QAAQ,EAAE,sBAAsB;oBAChCQ,SAAS,EAAE;sBAAEC,KAAK,EAAE,CAAC;sBAAErC,OAAO,EAAE;oBAAyC;kBAC3E,CAAC,CAAC;kBACF6B,WAAW,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EACD9C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACE,QAAQ,iBACrC5E,OAAA;kBAAM0D,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE1C,YAAY,CAACwD,SAAS,CAACC,MAAM,CAACE,QAAQ,CAACjC;gBAAO;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN/D,OAAA;gBAAQsE,IAAI,EAAC,QAAQ;gBAACH,QAAQ,EAAErD,OAAQ;gBAAC4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAC5D7C,OAAO,GAAG,qBAAqB,GAAG,gBAAgB,eACnDd,OAAA,CAACZ,UAAU;kBAAC4E,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/D,OAAA;MAAQ0D,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAChC3D,OAAA;QAAA2D,QAAA,EAAG;MAAgE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvE/D,OAAA;QAAA2D,QAAA,GAAG,eAAa,eAAA3D,OAAA;UAAA2D,QAAA,EAAM;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA5RID,OAAO;EAAA,QAC0DtB,OAAO,EAC3DC,WAAW,EAKVC,OAAO,EACJA,OAAO;AAAA;AAAAoG,EAAA,GARxBhF,OAAO;AA8Rb,eAAeA,OAAO;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}