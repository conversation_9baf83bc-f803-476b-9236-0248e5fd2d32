# Facebook Marketing API: Ad Campaign Management

## Summary
This guide covers essential ad campaign management operations through the Facebook Marketing API, including modifying campaign settings, pausing/resuming campaigns, archiving campaigns, and permanently deleting campaigns. It provides practical examples for each operation with proper API request formatting.

## Key Points
- Campaign modifications are done via POST requests to the /<CAMPAIGN_ID> endpoint
- Campaign status can be set to ACTIVE, PAUSED, or ARCHIVED for different operational states
- Deleting campaigns is permanent and cannot be undone - use DELETE method carefully
- Archived campaigns can be restored by changing status back to ACTIVE
- Multiple campaign attributes can be updated in a single API request

## API Endpoints
- `POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID>`
- `DELETE https://graph.facebook.com/v23.0/<CAMPAIGN_ID>`

## Parameters
- objective
- daily_budget
- status
- access_token
- CAMPAIGN_ID

## Content
# Ad Campaign Management

Managing ad campaigns through the Marketing API involves several key operations: modifying campaign settings, pausing and resuming campaigns, and deleting campaigns.

## Modify an Ad Campaign

To update an existing ad campaign, you can send a `POST` request to the `/<CAMPAIGN_ID>` endpoint. You can change various settings, including the campaign's objective, budget, and targeting attributes.

**Example API Request:**

```bash
curl -X POST \
  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \
  -F 'objective=CONVERSIONS' \
  -F 'daily_budget=2000' \
  -F 'status=ACTIVE' \
  -F 'access_token=<ACCESS_TOKEN>'
```

## Pause an Ad Campaign

Temporarily stopping a campaign can help you reassess your strategy without deleting the campaign entirely. To pause a campaign, update its status to `PAUSED`.

**Example API Request:**

```bash
curl -X POST \
  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \
  -F 'status=PAUSED' \
  -F 'access_token=<ACCESS_TOKEN>'
```

To resume the campaign, you can set the status back to `ACTIVE`.

## Archive an Ad Campaign

If you want to temporarily stop a campaign without deleting it, you can archive it instead. To do this, send a `POST` request to the `/<CAMPAIGN_ID>` endpoint with the status parameter set to `ARCHIVED`.

**Example API Request:**

```bash
curl -X POST \
  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \
  -F 'status=ARCHIVED' \
  -F 'access_token=<ACCESS_TOKEN>'
```

Note that archiving a campaign will stop it from running, but it can be easily restored by changing its status back to `ACTIVE`.

## Delete an Ad Campaign

When you need to permanently remove a campaign, send a `DELETE` request to the `/<CAMPAIGN_ID>` endpoint.

Be cautious when deleting campaigns, as this action cannot be undone. Always double-check the campaign ID before deletion to avoid accidental loss of data.

**Example API Request:**

```bash
curl -X DELETE \
  https://graph.facebook.com/v23.0/<CAMPAIGN_ID> \
  -F 'access_token=<ACCESS_TOKEN>'
```

## Learn More

- [Campaign Reference](/docs/marketing-api/reference/ad-campaign-group)
- [Manage Your Ad Object's Status](/docs/marketing-apis/guides/manage-your-ad-object-status)
- [Troubleshooting](/docs/liz-test/marketing-api/troubleshooting)

## Examples
curl -X POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'objective=CONVERSIONS' -F 'daily_budget=2000' -F 'status=ACTIVE' -F 'access_token=<ACCESS_TOKEN>'

curl -X POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'status=PAUSED' -F 'access_token=<ACCESS_TOKEN>'

curl -X POST https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'status=ARCHIVED' -F 'access_token=<ACCESS_TOKEN>'

curl -X DELETE https://graph.facebook.com/v23.0/<CAMPAIGN_ID> -F 'access_token=<ACCESS_TOKEN>'

---
**Tags:** facebook-marketing-api, campaign-management, api-operations, crud-operations, advertising
**Difficulty:** intermediate
**Content Type:** guide
**Source:** https://developers.facebook.com/docs/marketing-api/get-started/manage-campaigns
**Processed:** 2025-06-25T15:49:28.830Z