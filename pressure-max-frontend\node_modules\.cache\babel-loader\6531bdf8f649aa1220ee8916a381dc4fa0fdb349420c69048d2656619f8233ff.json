{"ast": null, "code": "\"use strict\";\n\n// ref: https://github.com/tc39/proposal-global\nvar getGlobal = function () {\n  // the only reliable means to get the global object is\n  // `Function('return this')()`\n  // However, this causes CSP violations in Chrome apps.\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('unable to locate global object');\n};\nvar globalObject = getGlobal();\nexport const fetch = globalObject.fetch;\nexport default globalObject.fetch.bind(globalObject);\nexport const Headers = globalObject.Headers;\nexport const Request = globalObject.Request;\nexport const Response = globalObject.Response;", "map": {"version": 3, "names": ["getGlobal", "self", "window", "global", "Error", "globalObject", "fetch", "bind", "Headers", "Request", "Response"], "sources": ["C:/Users/<USER>/node_modules/@supabase/node-fetch/browser.js"], "sourcesContent": ["\"use strict\";\n\n// ref: https://github.com/tc39/proposal-global\nvar getGlobal = function() {\n    // the only reliable means to get the global object is\n    // `Function('return this')()`\n    // However, this causes CSP violations in Chrome apps.\n    if (typeof self !== 'undefined') { return self; }\n    if (typeof window !== 'undefined') { return window; }\n    if (typeof global !== 'undefined') { return global; }\n    throw new Error('unable to locate global object');\n}\n\nvar globalObject = getGlobal();\n\nexport const fetch = globalObject.fetch;\n\nexport default globalObject.fetch.bind(globalObject);\n\nexport const Headers = globalObject.Headers;\nexport const Request = globalObject.Request;\nexport const Response = globalObject.Response;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,IAAIA,SAAS,GAAG,SAAAA,CAAA,EAAW;EACvB;EACA;EACA;EACA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAAE,OAAOA,IAAI;EAAE;EAChD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAAE,OAAOA,MAAM;EAAE;EACpD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAAE,OAAOA,MAAM;EAAE;EACpD,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;AACrD,CAAC;AAED,IAAIC,YAAY,GAAGL,SAAS,CAAC,CAAC;AAE9B,OAAO,MAAMM,KAAK,GAAGD,YAAY,CAACC,KAAK;AAEvC,eAAeD,YAAY,CAACC,KAAK,CAACC,IAAI,CAACF,YAAY,CAAC;AAEpD,OAAO,MAAMG,OAAO,GAAGH,YAAY,CAACG,OAAO;AAC3C,OAAO,MAAMC,OAAO,GAAGJ,YAAY,CAACI,OAAO;AAC3C,OAAO,MAAMC,QAAQ,GAAGL,YAAY,CAACK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}