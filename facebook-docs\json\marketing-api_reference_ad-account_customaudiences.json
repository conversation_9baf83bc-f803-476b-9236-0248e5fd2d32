{"title": "Ad Account Customaudiences", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_11_1U\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Ad Account Customaudiences</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Reading\">Reading</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#fields\">Fields</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Creating\">Creating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example-2\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters-2\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#return-type\">Return Type</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes-2\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Updating\">Updating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Deleting\">Deleting</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_7D\"><div data-click-area=\"main\"><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_7/\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_QB\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_8b\"></div></span></div></div></div></div><div class=\"_1xb4 _3-98\"><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\">Ad Account Customaudiences</h1><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>The custom audiences associated with the ad account.</p>\n</div><div><br><b>Note:</b> To retrieve the IDs of lookalike audiences based on your custom audiences, use the <code>lookalike_audience_ids</code> field. See <a href=\"/docs/marketing-api/audiences/guides/lookalike-audiences#read\">Lookalike Audiences - Managing Audiences</a> for more information.\n    \n  </div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_Ss\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_8r\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_4l\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_dS\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_E/\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_po\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_b_z3\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fcustomaudiences%3Ffields%3Did&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_c_9o\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/customaudiences?fields=id HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_d_SR\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"kwd\">get</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences?fields=id'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_e_yB\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"id\"</span><span class=\"pln\">\n    </span><span class=\"pun\">},</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_f_eh\" style=\"\"><code><span class=\"typ\">Bundle</span><span class=\"pln\"> </span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"fields\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"id\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">params</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">GET</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_g_Zx\" style=\"\"><code><span class=\"typ\">NSDictionary</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">@{</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"fields\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"id\"</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">};</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"GET\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_h_3z\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=\"id\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_i_CO\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>business_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Optional.<br>\nThis param assists with filters, such as recently used.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>fetch_primary_audience</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div><p>fetch_primary_audience</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>fields</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Fields to be retrieved. Default behavior is to return only the IDs.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>filtering</code></span></div><div class=\"_yb\">list&lt;Filter Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Filters on the report data. This parameter is an array of filter objects.</p>\n</div></div><p></p></td></tr><tr class=\"row_3-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>field</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_3-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>operator</code></span></div><div class=\"_yb\">enum {EQUAL, NOT_EQUAL, GREATER_THAN, GREATER_THAN_OR_EQUAL, LESS_THAN, LESS_THAN_OR_EQUAL, IN_RANGE, NOT_IN_RANGE, CONTAIN, NOT_CONTAIN, CONTAINS_ANY, NOT_CONTAINS_ANY, IN, NOT_IN, STARTS_WITH, ENDS_WITH, ANY, ALL, AFTER, BEFORE, ON_OR_AFTER, ON_OR_BEFORE, NONE, TOP}</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_3-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>value</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">numeric string</div></td><td><p class=\"_yd\"></p><div><div><p>Optional.<br>\nThis param fetches audiences associated to specific pixel.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"fields\">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class=\"_3hux\"><p>{\n    \"<code>data</code>\": [],\n    \"<code>paging</code>\": {}\n}</p>\n</pre><div class=\"_3-8o\"><h4><code>data</code></h4>A list of <a target=\"_blank\" href=\"/docs/marketing-api/reference/custom-audience/\">CustomAudience</a> nodes.</div><div class=\"_3-8o\"><h4><code>paging</code></h4>For more details about pagination, see the <a href=\"/docs/graph-api/using-graph-api/#paging\">Graph API guide</a>.</div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80003</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#custom-audience.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Creating\">Creating</h2><div><div class=\"_57yz _57z0 _3-8p\"><div class=\"_57y-\"><p>Your ability to create custom audiences may be limited.</p>\n</div></div><div class=\"_57yz _57z0 _3-8p\"><div class=\"_57y-\"><p>It is expected that you have the same audience capabilities independent of your app's status, which could be <em>in development</em> or <em>live</em>.</p>\n</div></div><span><p>To create a custom audience you'll first need to create a blank audience. Then, you'll want to add people to the blank audience you just created by updating the <a href=\"/docs/marketing-api/reference/custom-audience/users/\">users edge</a> of the audience. <strong>You can create a maximum of 500 custom audiences.</strong></p>\n</span><br></div><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>customaudiences</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-account/customaudiences/\"><code>/act_{ad_account_id}/customaudiences</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/marketing-api/reference/custom-audience/\">CustomAudience</a> will be created.</div><div><h3 id=\"example-2\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_j_+j\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_k_4r\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_l_K+\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_m_R0\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_n_Qh\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_o_JW\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_p_KW\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fcustomaudiences%3Fname%3DMy%2Bnew%2BCustom%2BAudience%26subtype%3DCUSTOM%26description%3DPeople%2Bwho%2Bpurchased%2Bon%2Bmy%2Bwebsite%26customer_file_source%3DUSER_PROVIDED_ONLY&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_q_/Z\" style=\"\"><code><span class=\"pln\">POST </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/customaudiences HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com\n\nname</span><span class=\"pun\">=</span><span class=\"typ\">My</span><span class=\"pun\">+</span><span class=\"kwd\">new</span><span class=\"pun\">+</span><span class=\"typ\">Custom</span><span class=\"pun\">+</span><span class=\"typ\">Audience</span><span class=\"pun\">&amp;</span><span class=\"pln\">subtype</span><span class=\"pun\">=</span><span class=\"pln\">CUSTOM</span><span class=\"pun\">&amp;</span><span class=\"pln\">description</span><span class=\"pun\">=</span><span class=\"typ\">People</span><span class=\"pun\">+</span><span class=\"pln\">who</span><span class=\"pun\">+</span><span class=\"pln\">purchased</span><span class=\"pun\">+</span><span class=\"pln\">on</span><span class=\"pun\">+</span><span class=\"kwd\">my</span><span class=\"pun\">+</span><span class=\"pln\">website</span><span class=\"pun\">&amp;</span><span class=\"pln\">customer_file_source</span><span class=\"pun\">=</span><span class=\"pln\">USER_PROVIDED_ONLY</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_r_c6\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"pln\">post</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences'</span><span class=\"pun\">,</span><span class=\"pln\">\n    array </span><span class=\"pun\">(</span><span class=\"pln\">\n      </span><span class=\"str\">'name'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'My new Custom Audience'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'subtype'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'CUSTOM'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'description'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'People who purchased on my website'</span><span class=\"pun\">,</span><span class=\"pln\">\n      </span><span class=\"str\">'customer_file_source'</span><span class=\"pln\"> </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> </span><span class=\"str\">'USER_PROVIDED_ONLY'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">),</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_s_1i\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"POST\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"My new Custom Audience\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"subtype\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"CUSTOM\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"description\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"People who purchased on my website\"</span><span class=\"pun\">,</span><span class=\"pln\">\n        </span><span class=\"str\">\"customer_file_source\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"USER_PROVIDED_ONLY\"</span><span class=\"pln\">\n    </span><span class=\"pun\">},</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_t_tQ\" style=\"\"><code><span class=\"typ\">Bundle</span><span class=\"pln\"> </span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">Bundle</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"name\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"My new Custom Audience\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"subtype\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"CUSTOM\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"description\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"People who purchased on my website\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"kwd\">params</span><span class=\"pun\">.</span><span class=\"pln\">putString</span><span class=\"pun\">(</span><span class=\"str\">\"customer_file_source\"</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"str\">\"USER_PROVIDED_ONLY\"</span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">params</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">POST</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_u_0P\" style=\"\"><code><span class=\"typ\">NSDictionary</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"kwd\">params</span><span class=\"pln\"> </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">@{</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"name\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"My new Custom Audience\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"subtype\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"CUSTOM\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"description\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"People who purchased on my website\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"pun\">@</span><span class=\"str\">\"customer_file_source\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">@</span><span class=\"str\">\"USER_PROVIDED_ONLY\"</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">};</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"POST\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre><pre class=\"_5gt1 prettyprint hidden_elem prettyprinted\" id=\"u_0_v_NU\" style=\"\"><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"My new Custom Audience\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'subtype=\"CUSTOM\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'description=\"People who purchased on my website\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'customer_file_source=\"USER_PROVIDED_ONLY\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/customaudiences</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_w_/G\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>allowed_domains</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>A list of domains that the audience is restricted to.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>claim_objective</code></span></div><div class=\"_yb\">enum {AUTOMOTIVE_MODEL, COLLABORATIVE_ADS, HOME_LISTING, MEDIA_TITLE, PRODUCT, TRAVEL, VEHICLE, VEHICLE_OFFER}</div></td><td><p class=\"_yd\"></p><div><div><p>Specifies the objective of audiences with <code>CLAIM</code> subtype.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>content_type</code></span></div><div class=\"_yb\">enum {AUTOMOTIVE_MODEL, DESTINATION, FLIGHT, GENERIC, HOME_LISTING, HOTEL, LOCAL_SERVICE_BUSINESS, MEDIA_TITLE, OFFLINE_PRODUCT, PRODUCT, VEHICLE, VEHICLE_OFFER}</div></td><td><p class=\"_yd\"></p><div><div><p>Specifies a mandatory content type for <code>TRAVEL</code> claim objective.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>customer_file_source</code></span></div><div class=\"_yb\">enum {USER_PROVIDED_ONLY, PARTNER_PROVIDED_ONLY, BOTH_USER_AND_PARTNER_PROVIDED}</div></td><td><p class=\"_yd\"></p><div><div><p>Source of customer information in the uploaded file.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>dataset_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The offline conversion dataset associated with this audience.</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>description</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The description for this custom audience</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>enable_fetch_or_create</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>If <code>true</code>, we fetch a custom audience instead of creating one when an identical custom audience already exists. Identical custom audiences must have same <code>name</code>, <code>claim_objective</code>, <code>content_type</code>, <code>event_source_group/event_sources/sliced_event_source_group</code>, inclusions, exclusions and rule.</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>event_source_group</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Specifies event source group for <code>TRAVEL</code> claim objective.</p>\n</div></div><p></p></td></tr><tr class=\"row_8 _5m27\"><td><div class=\"_yc\"><span><code>event_sources</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Specifies event sources for <code>TRAVEL</code> claim objective.</p>\n</div></div><p></p></td></tr><tr class=\"row_8-0 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>id</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_8-1 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>type</code></span></div><div class=\"_yb\">enum {APP, OFFLINE_EVENTS, PAGE, PIXEL}</div></td><td><p class=\"_yd\"></p><div><div><p>type</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>facebook_page_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>facebook_page_id</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>is_value_based</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>Whether the audience is used to seed a new value based lookalike audience.</p>\n</div></div><p></p></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>list_of_accounts</code></span></div><div class=\"_yb\">list&lt;int64&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>List of user and page accounts</p>\n</div></div><p></p></td></tr><tr class=\"row_12\"><td><div class=\"_yc\"><span><code>lookalike_spec</code></span></div><div class=\"_yb\">JSON-encoded string</div></td><td><p class=\"_yd\"></p><div><div><p>The specification for creating a <a href=\"/docs/marketing-api/lookalike-audience-targeting/\">lookalike audience</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_13 _5m29\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The name of this custom audience.</p>\n</div></div><p></p></td></tr><tr class=\"row_14\"><td><div class=\"_yc\"><span><code>opt_out_link</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Your opt-out URL so people can choose not to be targeted.</p>\n</div></div><p></p></td></tr><tr class=\"row_15 _5m29\"><td><div class=\"_yc\"><span><code>origin_audience_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of origin Custom Audience.The origin audience you create must have a minimum size of 100.</p>\n</div></div><p></p></td></tr><tr class=\"row_16\"><td><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The pixel associated with this audience</p>\n</div></div><p></p></td></tr><tr class=\"row_17 _5m29\"><td><div class=\"_yc\"><span><code>prefill</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>You can specify <code>true</code> or <code>false</code>. <code>true</code> includes website traffic recorded prior to the audience creation, and <code>false</code> only includes website traffic beginning at the time of the audience creation.</p>\n</div></div><p></p></td></tr><tr class=\"row_18\"><td><div class=\"_yc\"><span><code>product_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The Product Set to target with this audience</p>\n</div></div><p></p></td></tr><tr class=\"row_19 _5m29\"><td><div class=\"_yc\"><span><code>retention_days</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Number of days to keep the user in this cluster. You can use any value between <code>1</code> and <code>180</code> days.              Defaults to forever, if not specified.</p>\n</div></div><p></p></td></tr><tr class=\"row_20\"><td><div class=\"_yc\"><span><code>rule</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Audience rule to be applied on the referrer URL. Used for <a href=\"/docs/marketing-api/custom-audience-website/#audiencerules\">website custom audiences</a>, <a href=\"/docs/marketing-api/dynamic-product-ads/product-audiences/#productaudience\">product audiences</a>, and <a href=\"/docs/marketing-api/guides/videoads/#remarketing\">video remarketing audiences</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_21 _5m29\"><td><div class=\"_yc\"><span><code>rule_aggregation</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Aggregation rule</p>\n</div></div><p></p></td></tr><tr class=\"row_22\"><td><div class=\"_yc\"><span><code>subscription_info</code></span></div><div class=\"_yb\">list&lt;enum {WHATSAPP, MESSENGER}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>subscription_info</p>\n</div></div><p></p></td></tr><tr class=\"row_23 _5m29\"><td><div class=\"_yc\"><span><code>subtype</code></span></div><div class=\"_yb\">enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG_OF_ACCOUNTS, STUDY_RULE_AUDIENCE, FOX, MEASUREMENT, REGULATED_CATEGORIES_AUDIENCE, BIDDING, EXCLUSION, MESSENGER_SUBSCRIBER_LIST}</div></td><td><p class=\"_yd\"></p><div><div><p>Type of custom audience, derived from original data source. <br> Note: <code>COMBINATION</code> subtype is only used by Ads Manager, and is not available through the API. <br><br>              Number of audiences limit for selected subtype:<br>              <code>CUSTOM</code>: 500<br>                         <code>LOOKALIKE</code>: 10000<br></p>\n</div></div><p></p></td></tr><tr class=\"row_24\"><td><div class=\"_yc\"><span><code>use_for_products</code></span></div><div class=\"_yb\">list&lt;enum {ADS, MARKETING_MESSAGES}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>use_for_products</p>\n</div></div><p></p></td></tr><tr class=\"row_25 _5m29\"><td><div class=\"_yc\"><span><code>use_in_campaigns</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>true</code></div><p class=\"_yd\"></p><div><div><p>use_in_campaigns</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div><div class=\"_uoj\"><code>message</code>: string, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>2654</td><td>Failed to create custom audience</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80003</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#custom-audience.</td></tr><tr><td>2663</td><td>Terms of service has not been accepted. To accept, go to https://www.facebook.com/customaudiences/app/tos</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>2667</td><td>Your account permissions don't allow you to create a custom audience for this event source.</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account/", "/docs/marketing-api/reference/ad-account/customaudiences", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/reference/ad-account/account_controls/", "/docs/marketing-api/reference/ad-account/activities/", "/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "/docs/marketing-api/reference/ad-account/adcreatives/", "/docs/marketing-api/reference/ad-account/adimages/", "/docs/marketing-api/reference/ad-account/adlabels/", "/docs/marketing-api/reference/ad-account/adplayables/", "/docs/marketing-api/reference/ad-account/adrules_library/", "/docs/marketing-api/reference/ad-account/ads/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reference/ad-account/adspixels/", "/docs/marketing-api/reference/ad-account/advertisable_applications/", "/docs/marketing-api/reference/ad-account/advideos/", "/docs/marketing-api/reference/ad-account/agencies/", "/docs/marketing-api/reference/ad-account/applications/", "/docs/marketing-api/reference/ad-account/assigned_users/", "/docs/marketing-api/reference/ad-account/async_batch_requests/", "/docs/marketing-api/reference/ad-account/asyncadcreatives/", "/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "/docs/marketing-api/reference/ad-account/campaigns/", "/docs/marketing-api/reference/ad-account/customaudiences/", "/docs/marketing-api/reference/ad-account/customaudiencestos/", "/docs/marketing-api/reference/ad-account/customconversions/", "/docs/marketing-api/reference/ad-account/delivery_estimate/", "/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "/docs/marketing-api/reference/ad-account/dsa_recommendations/", "/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "/docs/marketing-api/reference/ad-account/insights/", "/docs/marketing-api/reference/ad-account/instagram_accounts/", "/docs/marketing-api/reference/ad-account/mcmeconversions/", "/docs/marketing-api/reference/ad-account/minimum_budgets/", "/docs/marketing-api/reference/ad-account/product_audiences/", "/docs/marketing-api/reference/ad-account/promote_pages/", "/docs/marketing-api/reference/ad-account/publisher_block_lists/", "/docs/marketing-api/reference/ad-account/reachestimate/", "/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "/docs/marketing-api/reference/ad-account/saved_audiences/", "/docs/marketing-api/reference/ad-account/subscribed_apps/", "/docs/marketing-api/reference/ad-account/targetingbrowse/", "/docs/marketing-api/reference/ad-account/targetingsearch/", "/docs/marketing-api/reference/ad-account/tracking/", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/audiences/guides/lookalike-audiences#read", "/docs/marketing-api/reference/custom-audience/", "/docs/marketing-api/reference/custom-audience/users/", "/docs/marketing-api/lookalike-audience-targeting/", "/docs/marketing-api/custom-audience-website/#audiencerules", "/docs/marketing-api/dynamic-product-ads/product-audiences/#productaudience", "/docs/marketing-api/guides/videoads/#remarketing"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiences/", "timestamp": "2025-06-25T15:25:25.839Z", "reprocessedAt": "2025-06-25T16:26:05.651Z", "reprocessedWith": "claude-sonnet-4"}