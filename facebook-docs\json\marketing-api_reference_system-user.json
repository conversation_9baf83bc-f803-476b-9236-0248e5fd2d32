{"title": "System User", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_Lp\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_3u\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_yr\"></div></span></div></div>\n\n<h1 id=\"overview\">System User</h1>\n\n<h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>Represents a system user</p>\n</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_6Z\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_Nl\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_sr\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_ny\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_qY\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_rW\">iOS SDK</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=%7Bsystem-user-id%7D&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_b_SQ\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/{</span><span class=\"pln\">system</span><span class=\"pun\">-</span><span class=\"pln\">user</span><span class=\"pun\">-</span><span class=\"pln\">id</span><span class=\"pun\">}</span><span class=\"pln\"> HTTP</span><span class=\"pun\">/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>System user ID.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>created_by</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/graph-api/reference/user/\">User</a></div></td><td><p class=\"_yd\"></p><div><div><p>The creator of this system user.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>created_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>The creation time of this system user.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>finance_permission</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Financial permission role of the user in business manager, such as Editor, Analyst, and so on.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>ip_permission</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Ads right permission role of the user in business manager, such as Reviewer, and so on.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Name used to identify this system user.</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr></tbody></table></div></div><div><h3 id=\"edges\">Edges</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/system-user/assigned_business_asset_groups/\"><code>assigned_business_asset_groups</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;BusinessAssetGroup&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Business asset groups that are assign to this business scoped user</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/system-user/assigned_pages/\"><code>assigned_pages</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Page&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Pages that are assigned to this business scoped user</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/system-user/assigned_product_catalogs/\"><code>assigned_product_catalogs</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalog&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Product catalogs that are assigned to this business scoped user</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>110</td><td>Invalid user id</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Creating\">Creating</h2><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>system_users</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/business/system_users/\"><code>/{business_id}/system_users</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/marketing-api/reference/system-user/\">SystemUser</a> will be created.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_g_wl\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Name of system user to be added to this business.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>role</code></span></div><div class=\"_yb\">enum {FINANCE_EDITOR, FINANCE_ANALYST, ADS_RIGHTS_REVIEWER, ADMIN, EMPLOYEE, DEVELOPER, PARTNER_CENTER_ADMIN, PARTNER_CENTER_ANALYST, PARTNER_CENTER_OPERATIONS, PARTNER_CENTER_MARKETING, PARTNER_CENTER_EDUCATION, MANAGE, DEFAULT, FINANCE_EDIT, FINANCE_VIEW}</div></td><td><p class=\"_yd\"></p><div><div><p>Role of system user to be added to this business.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>system_user_id</code></span></div><div class=\"_yb\">int</div></td><td><p class=\"_yd\"></p><div><div><p>ID of system user.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>104001</td><td>In order to create a system user, an app must be part of this business. Please add an app and then try again.</td></tr><tr><td>3965</td><td>This Business Manager has reached maximum number of admin system user limit.</td></tr><tr><td>3949</td><td>This Business Manager has reached maximum number of system user limit.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>102</td><td>Session key invalid or no longer valid</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div>\n\n<h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/system-user", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user/assigned_business_asset_groups/", "/docs/marketing-api/reference/system-user/assigned_pages/", "/docs/marketing-api/reference/system-user/assigned_product_catalogs/", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/business/system_users/", "/docs/marketing-api/reference/system-user/"], "url": "https://developers.facebook.com/docs/marketing-api/reference/system-user", "timestamp": "2025-06-25T15:47:33.457Z"}