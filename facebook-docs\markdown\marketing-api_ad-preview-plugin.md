# Ad Preview Plugin

Marketing API Version

[v23.0](#)

# Ad Preview Plugin

The Ad Preview plugin is the easiest way for advertisers to preview ads on their own websites.

The plugin enables you to generate Right Hand Column, Feed, or Mobile previews of an ad by specifying a Creative Spec, Adgroup ID or Creative ID. Previews can either be generated using a Social Plugin or through the Graph API.

## Parameters

*   Required: One of `creative`, `creative_id`, or `adgroup_id`
*   Required: `ad_format`, which replaces `page_type` parameter
*   Optional: `ad_account_id`, `targeting`, `post`

The preview plugin requires you to be logged in with Facebook Login. If `creative_id`, `adgroup_id`, or `ad_account_id` is used, you must also have the permissions to access the Creative, Ad Group, or Ad Account respectively.

Setting

HTML5 Attribute

Description

`ad_account_id`

`data-ad-account-id`

Required when specifying a creative that uses `image_hash`

`adgroup_id`

`data-adgroup-id`

Adgroup ID returned from a Graph API call.

`creative`

`data-creative`

JSON-encoded [creative spec](/docs/reference/ads-api/adcreative/).

`creative_id`

`data-creative-id`

Creative ID returned from a Graph API call.

`ad_format`

`data-ad-format`

One of: `RIGHT_COLUMN_STANDARD`, `DESKTOP_FEED_STANDARD`, `MOBILE_FEED_STANDARD`, or `FACEBOOK_STORY_MOBILE`.

`page_type`

`data-page-type`

One of: `rightcolumn`, `desktopfeed`, or `mobile`.

`targeting`

`data-targeting`

JSON-encoded [targeting spec](/docs/ads-api/targeting).

`post`

`data-post`

JSON-encoded post specification according to the [Pages API documentation](/docs/graph-api/reference/page).

## Graph API

Previews can also be generated using the [Graph API](/docs/reference/ads-api/generatepreview/). To generate a plugin-style preview, simply specify the additional parameter, `ad_format`, as described in the table above.