{"title": "Facebook Marketing API - Ad Account Instagram Accounts Reference", "summary": "This reference page documents the Ad Account Instagram Accounts endpoint, which allows you to retrieve Instagram accounts associated with a specific Ad Account. The endpoint only supports read operations and returns a list of IGUser nodes with pagination and summary information.", "content": "# Ad Account Instagram Accounts\n\n## Overview\n\nThe Ad Account Instagram Accounts endpoint allows you to retrieve Instagram accounts that are associated with a specific Ad Account in the Facebook Marketing API.\n\n## Reading\n\nRetrieve Instagram accounts associated with this Ad Account.\n\n### Endpoint\n\n```\nGET /v23.0/{ad-account-id}/instagram_accounts\n```\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/instagram_accounts HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/instagram_accounts',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/instagram_accounts\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n#### Android SDK\n```java\nnew GraphRequest(\n    AccessToken.getCurrentAccessToken(),\n    \"/{ad-account-id}/instagram_accounts\",\n    null,\n    HttpMethod.GET,\n    new GraphRequest.Callback() {\n        public void onCompleted(GraphResponse response) {\n            /* handle the result */\n        }\n    }\n).executeAsync();\n```\n\n#### iOS SDK\n```objc\nFBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]\n                               initWithGraphPath:@\"/{ad-account-id}/instagram_accounts\"\n                                      parameters:params\n                                      HTTPMethod:@\"GET\"];\n[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,\n                                      id result,\n                                      NSError *error) {\n    // Handle the result\n}];\n```\n\n### Parameters\n\nThis endpoint doesn't have any parameters.\n\n### Response Format\n\nReading from this edge will return a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {},\n    \"summary\": {}\n}\n```\n\n#### Response Fields\n\n- **data**: A list of IGUser nodes representing Instagram accounts\n- **paging**: Pagination information for navigating through results\n- **summary**: Aggregated information about the edge, such as counts\n\n#### Summary Fields\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `total_count` | int32 | Total number of objects on this edge |\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 100 | Invalid parameter |\n| 80002 | Too many calls to this Instagram account. Rate limiting applied. |\n| 80004 | Too many calls to this ad-account. Rate limiting applied. |\n\n## Supported Operations\n\n- **Creating**: Not supported\n- **Updating**: Not supported  \n- **Deleting**: Not supported\n\nThis endpoint is read-only and only supports retrieving existing Instagram account associations.", "keyPoints": ["Retrieves Instagram accounts associated with a specific Ad Account", "Read-only endpoint - no create, update, or delete operations supported", "Returns IGUser nodes with pagination and summary information", "Subject to rate limiting for both Instagram accounts and ad accounts", "Requires proper OAuth 2.0 access token and permissions"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/instagram_accounts"], "parameters": ["ad-account-id (path parameter)", "summary (optional query parameter for aggregated data)"], "examples": ["HTTP GET request example", "PHP SDK implementation", "JavaScript SDK usage", "Android SDK integration", "iOS SDK implementation"], "tags": ["Facebook Marketing API", "Instagram", "Ad Account", "Graph API", "Social Media Marketing", "API Reference"], "relatedTopics": ["IGUser nodes", "Graph API pagination", "OAuth 2.0 authentication", "Rate limiting", "Facebook SDK implementations", "Ad Account management"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/instagram_accounts/", "processedAt": "2025-06-25T16:28:11.842Z", "processor": "openrouter-claude-sonnet-4"}