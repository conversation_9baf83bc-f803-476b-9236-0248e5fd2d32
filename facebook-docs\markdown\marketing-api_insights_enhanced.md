# Facebook Marketing API Insights - Comprehensive Guide to Ad Statistics

## Summary
The Facebook Marketing API Insights provides a unified interface for retrieving ad performance statistics across campaigns, ad sets, and ads. This guide covers making API calls, handling responses, attribution windows, field expansion, sorting, and troubleshooting common issues.

## Key Points
- Insights API provides unified access to ad statistics across all Facebook advertising objects
- Supports multiple aggregation levels (account, campaign, ad set, ad) with automatic deduplication
- Attribution windows allow control over conversion timeframes (1-day, 7-day)
- Field expansion enables requesting insights as part of other object queries
- Proper filtering is required to access deleted or archived object statistics

## API Endpoints
- `act_<AD_ACCOUNT_ID>/insights`
- `<CAMPAIGN_ID>/insights`
- `<ADSET_ID>/insights`
- `<AD_ID>/insights`
- `https://graph.facebook.com/v23.0/{object_id}/insights`

## Parameters
- fields
- access_token
- level
- date_preset
- time_range
- action_attribution_windows
- sort
- filtering
- use_unified_attribution_setting
- action_report_time

## Content
# Facebook Marketing API Insights

The Insights API provides a single, consistent interface to retrieve ad statistics across all Facebook advertising objects.

## Prerequisites

Before you begin, you will need:
- The `ads_read` permission
- A Meta app (see [Meta App Development](/docs/development))
- Proper tracking setup using URL Tags, Meta Pixel, or Conversions API

## Core Concepts

### Available Endpoints

The Insights API is available as an edge on any ads object:

- `act_<AD_ACCOUNT_ID>/insights` - Account level insights
- `<CAMPAIGN_ID>/insights` - Campaign level insights  
- `<ADSET_ID>/insights` - Ad set level insights
- `<AD_ID>/insights` - Ad level insights

### Basic Request Structure

```bash
curl -G \
  -d "fields=impressions" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/<AD_ID>/insights"
```

### Response Format

```json
{
  "data": [
    {
      "impressions": "2466376",
      "date_start": "2009-03-28",
      "date_stop": "2016-04-01"
    }
  ],
  "paging": {
    "cursors": {
      "before": "MAZDZD",
      "after": "MAZDZD"
    }
  }
}
```

## Advanced Features

### Aggregation Levels

Aggregate results at different object levels to automatically deduplicate data:

```bash
curl -G \
  -d "level=ad" \
  -d "fields=impressions,ad_id" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/CAMPAIGN_ID/insights"
```

### Attribution Windows

Control conversion attribution timeframes (1-day, 7-day windows):

```bash
act_10151816772662695/insights?action_attribution_windows=['1d_click','1d_view']
```

Response includes attribution data:
```json
{
  "actions": [
    {
      "action_type": "link_click",
      "value": 6608,
      "1d_view": 86,
      "1d_click": 6510
    }
  ]
}
```

### Field Expansion

Request insights as part of other object queries:

```bash
curl -G \
  -d "fields=insights{impressions}" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/AD_ID"
```

### Sorting Results

Sort by any field in ascending or descending order:

```bash
curl -G \
  -d "sort=reach_descending" \
  -d "level=ad" \
  -d "fields=reach" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/AD_SET_ID/insights"
```

### Filtering by Ad Labels

Get insights for ads with specific labels:

```bash
curl -G \
  -d "fields=id,name,insights{unique_clicks,cpm,total_actions}" \
  -d "level=ad" \
  -d 'filtering=[{"field":"ad.adlabels","operator":"ANY", "value":["Label Name"]}]' \
  -d 'time_range={"since":"2015-03-01","until":"2015-03-31"}' \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/AD_OBJECT_ID/insights"
```

## Click Metrics Definitions

- **Link Clicks** (`actions:link_click`) - Clicks on ad links to destinations on or off Meta properties
- **Clicks (All)** (`clicks`) - All types of clicks including ad container interactions and expanded experiences

## Handling Deleted and Archived Objects

By default, only active objects are returned. To include archived objects:

```bash
curl -G \
  -d "level=ad" \
  -d "filtering=[{'field':'ad.effective_status','operator':'IN','value':['ARCHIVED']}]" \
  -d "access_token=ACCESS_TOKEN" \
  "https://graph.facebook.com/v23.0/act_AD_ACCOUNT_ID/insights/"
```

For deleted objects:

```bash
POST https://graph.facebook.com/VERSION/act_ID/insights?filtering=[{"field":"ad.effective_status","operator":"IN","value":["DELETED"]}]
```

## Troubleshooting

### Timeouts
- Break large queries into smaller date ranges
- Query unique metrics separately for better performance
- Use asynchronous requests for large datasets

### Rate Limiting
The API implements rate limiting for optimal performance. See [Limits & Best Practices](/docs/marketing-api/insights/best-practices/) for details.

### Ads Manager Discrepancy
Starting June 10, 2025:
- `use_unified_attribution_setting` and `action_report_time` parameters will be ignored
- API responses will match Ads Manager settings
- Attribution values based on ad-set-level settings
- Mixed reporting time for actions

## Related Resources

- [Breakdowns](/docs/marketing-api/insights/breakdowns) - Group results by dimensions
- [Action Breakdowns](/docs/marketing-api/insights/action-breakdowns) - Understanding action breakdown responses
- [Async Jobs](/docs/marketing-api/insights/async) - Handle large result sets
- [Best Practices](/docs/marketing-api/insights/best-practices/) - Limits and optimization tips

## Examples
Basic insights request with impressions field

Campaign insights at ad level aggregation

Attribution windows configuration

Field expansion for insights within object queries

Sorting results by reach descending

Filtering by ad labels

Querying archived and deleted objects

---
**Tags:** facebook-marketing-api, insights, ad-statistics, attribution, aggregation, filtering, api-reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/insights  
**Processed:** 2025-06-25T16:17:39.339Z