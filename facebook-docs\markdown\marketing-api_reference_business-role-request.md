# Business Role Request

Graph API Version

[v23.0](#)

# Business Role Request

## Reading

Represents a business user request. See the requests from an admin of the Business for people to join as member of this business.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bbusiness-role-request-id%7D&version=v23.0)

```
`GET /v23.0/{business-role-request-id} HTTP/1.1
Host: graph.facebook.com`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Fields

Field

Description

`id`

numeric string

Business role invitation request ID.

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

`created_by`

BusinessUser|SystemUser

User who sent the invitation to join this business.

`created_time`

datetime

Admin sent this request to someone to join a business at this time.

`email`

string

Email of user invited to join the business.

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

`expiration_time`

datetime

Invitation to join business expires at this time.

`finance_role`

enum

When you invite someone to join business, pre-assign the Finance role.

`invited_user_type`

list<enum>

Invited user type of this role request

`owner`

[Business](https://developers.facebook.com/docs/marketing-api/reference/business/)

Invite someone to join this business.

`role`

enum

Business role for user invited to the business.

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

`status`

enum

Status of the invitation, such as accepted, declined, expired and so on.

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

`updated_by`

BusinessUser|SystemUser

User who updated the invitation.

`updated_time`

datetime

Time invitation updated.

### Error Codes

Error

Description

200

Permissions error

## Creating

You can't perform this operation on this endpoint.

## Updating

You can update a [BusinessRoleRequest](/docs/marketing-api/reference/business-role-request/) by making a POST request to [`/{business_role_request_id}`](/docs/marketing-api/reference/business-role-request/).

### Parameters

Parameter

Description

`role`

enum {FINANCE\_EDITOR, FINANCE\_ANALYST, ADS\_RIGHTS\_REVIEWER, ADMIN, EMPLOYEE, DEVELOPER, PARTNER\_CENTER\_ADMIN, PARTNER\_CENTER\_ANALYST, PARTNER\_CENTER\_OPERATIONS, PARTNER\_CENTER\_MARKETING, PARTNER\_CENTER\_EDUCATION, MANAGE, DEFAULT, FINANCE\_EDIT, FINANCE\_VIEW}

Update invitation to include this role, such as `ADMIN`.

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node to which you POSTed.

Struct {

`id`: numeric string,

}

### Error Codes

Error

Description

100

Invalid parameter

415

Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.

## Deleting

You can delete a [BusinessRoleRequest](/docs/marketing-api/reference/business-role-request/) by making a DELETE request to [`/{business_role_request_id}`](/docs/marketing-api/reference/business-role-request/).

### Parameters

This endpoint doesn't have any parameters.

### Return Type

Struct {

`success`: bool,

}

### Error Codes

Error

Description

200

Permissions error

368

The action attempted has been deemed abusive or is otherwise disallowed

415

Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.

100

Invalid parameter