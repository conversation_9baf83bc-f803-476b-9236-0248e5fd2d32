# Facebook Marketing API - Ad Account Applications Reference

## Summary
Reference documentation for the Ad Account Applications endpoint in Facebook's Marketing API. This endpoint allows reading applications associated with an ad account but does not support creating, updating, or deleting operations.

## Key Points
- Only supports READ operations - creating, updating, and deleting are not allowed
- Returns a list of Application nodes associated with the specified ad account
- No parameters required for the basic request
- Subject to rate limiting (error code 80004)
- Requires proper OAuth 2.0 access token for authentication

## API Endpoints
- `GET /v23.0/{ad-account-id}/applications`

## Parameters
- ad-account-id (required in path)
- access-token (required for authentication)

## Content
# Ad Account Applications

The Ad Account Applications endpoint provides access to applications associated with a specific ad account in Facebook's Marketing API.

## Reading

### Endpoint
```
GET /v23.0/{ad-account-id}/applications
```

### Example Requests

#### HTTP
```http
GET /v23.0/{ad-account-id}/applications HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/applications',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/applications",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

#### Android SDK
```java
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/applications",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();
```

#### iOS SDK
```objc
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/applications"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];
```

### Parameters
This endpoint doesn't have any parameters.

### Response Format
Reading from this edge returns a JSON formatted result:
```json
{
    "data": [],
    "paging": {}
}
```

#### Fields
- **data**: A list of Application nodes
- **paging**: Pagination information (see Graph API guide for details)

### Error Codes
| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 190 | Invalid OAuth 2.0 Access Token |
| 80004 | Too many calls to this ad-account. Rate limiting applied |
| 100 | Invalid parameter |
| 2500 | Error parsing graph query |

## Operations Not Supported

### Creating
You can't perform this operation on this endpoint.

### Updating
You can't perform this operation on this endpoint.

### Deleting
You can't perform this operation on this endpoint.

## Examples
HTTP GET request example

PHP SDK implementation with error handling

JavaScript SDK callback pattern

Android SDK with GraphRequest

iOS SDK with completion handler

---
**Tags:** Facebook Marketing API, Ad Account, Applications, Graph API, REST API, Read-only endpoint  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/applications/  
**Processed:** 2025-06-25T16:23:50.477Z