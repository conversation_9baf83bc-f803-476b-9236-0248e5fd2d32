{"title": "Facebook Marketing API - Ad Account Campaigns Reference", "summary": "Complete reference documentation for managing ad campaigns within Facebook ad accounts, including reading, creating, updating, and deleting campaigns through the Marketing API v23.0.", "content": "# Ad Account, Ad Campaigns\n\nThe ad campaigns associated with a given ad account.\n\n## Important Updates\n\n- **May 1, 2018**: Marketing API 3.0 removed `kpi_custom_conversion_id`, `kpi_type`, and `kpi_results`\n- **September 15, 2022**: Marketing API v15.0 no longer allows creation of incremental conversion optimization campaigns\n- **Marketing API v15.0**: Advertisers can no longer create Special Ad Audiences\n\n## Reading Campaigns\n\nReturns the campaigns under this ad account. A request with no filters returns only campaigns that were not archived or deleted.\n\n### Endpoint\n```\nGET /v23.0/act_<AD_ACCOUNT_ID>/campaigns\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `date_preset` | enum | Predefined date range for aggregating insights metrics |\n| `effective_status` | list<enum> | Filter campaigns by status (ACTIVE, PAUSED, DELETED, etc.) |\n| `is_completed` | boolean | Return completed campaigns if true |\n| `time_range` | object | Custom date range with 'since' and 'until' fields |\n\n### Response Structure\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n- **data**: Array of Campaign objects\n- **paging**: Pagination information\n- **summary**: Aggregated information (insights, total_count)\n\n## Creating Campaigns\n\nCreate a new campaign by making a POST request to the campaigns edge.\n\n### Endpoint\n```\nPOST /v23.0/act_<AD_ACCOUNT_ID>/campaigns\n```\n\n### Required Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `name` | string | Campaign name (supports emoji) |\n| `objective` | enum | Campaign objective (OUTCOME_TRAFFIC, CONVERSIONS, etc.) |\n| `status` | enum | Campaign status (ACTIVE, PAUSED) |\n| `special_ad_categories` | array | Special ad category declarations |\n\n### Key Optional Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `bid_strategy` | enum | Bidding strategy (LOWEST_COST_WITHOUT_CAP, etc.) |\n| `daily_budget` | int64 | Daily budget in currency subunits |\n| `lifetime_budget` | int64 | Lifetime budget in currency subunits |\n| `buying_type` | string | AUCTION (default) or RESERVED |\n| `promoted_object` | object | Object being promoted across ads |\n| `spend_cap` | int64 | Maximum spend limit |\n| `start_time` | datetime | Campaign start time |\n| `stop_time` | datetime | Campaign end time |\n\n### Bid Strategies\n\n- **LOWEST_COST_WITHOUT_CAP**: Automatic bidding for maximum results\n- **LOWEST_COST_WITH_BID_CAP**: Manual maximum-cost bidding with bid limit\n- **COST_CAP**: Cost control bidding\n- **LOWEST_COST_WITH_MIN_ROAS**: Minimum ROAS bidding\n\n### Campaign Objectives\n\nSupported objectives include:\n- OUTCOME_TRAFFIC\n- OUTCOME_SALES\n- OUTCOME_LEADS\n- CONVERSIONS\n- BRAND_AWARENESS\n- And many more...\n\n## Updating Campaigns\n\nCampaign updates are not supported through this endpoint.\n\n## Deleting Campaigns\n\nDelete campaigns from an ad account using DELETE request.\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `delete_strategy` | enum | DELETE_ANY, DELETE_OLDEST, DELETE_ARCHIVED_BEFORE |\n| `object_count` | integer | Number of objects to delete |\n| `before_date` | datetime | Delete campaigns before this date |\n\n## Error Codes\n\nCommon error codes:\n- **100**: Invalid parameter\n- **190**: Invalid OAuth 2.0 Access Token\n- **200**: Permissions error\n- **2635**: Deprecated API version\n- **80004**: Rate limiting - too many calls\n\n## Code Examples\n\n### Reading Campaigns (cURL)\n```bash\ncurl -X GET -G \\\n  -d 'effective_status=[\"ACTIVE\",\"PAUSED\"]' \\\n  -d 'fields=\"name,objective\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/campaigns\n```\n\n### Creating a Campaign (cURL)\n```bash\ncurl -X POST \\\n  -F 'name=\"My campaign\"' \\\n  -F 'objective=\"OUTCOME_TRAFFIC\"' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'special_ad_categories=[]' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/campaigns\n```", "keyPoints": ["Campaigns are the top-level structure for organizing Facebook ads within an ad account", "Budget can be set at either campaign level (shared) or ad set level, but not both", "Special ad categories must be declared for certain types of campaigns (employment, housing, etc.)", "Bid strategy determines how Facebook optimizes for your campaign objectives", "Campaign objectives define the primary goal and affect available optimization options"], "apiEndpoints": ["GET /v23.0/act_<AD_ACCOUNT_ID>/campaigns", "POST /v23.0/act_<AD_ACCOUNT_ID>/campaigns", "DELETE /v23.0/act_<AD_ACCOUNT_ID>/campaigns"], "parameters": ["name", "objective", "status", "special_ad_categories", "bid_strategy", "daily_budget", "lifetime_budget", "effective_status", "promoted_object", "buying_type", "spend_cap"], "examples": ["Reading campaigns with status filter", "Creating a traffic campaign with OUTCOME_TRAFFIC objective", "Setting bid strategy and budget parameters", "Using special ad categories for compliance"], "tags": ["Facebook Marketing API", "Campaigns", "Ad Account", "Advertising", "API Reference", "CRUD Operations"], "relatedTopics": ["Ad Sets", "Ad Creative", "Campaign Budget Optimization", "Bid Strategies", "Special Ad Categories", "Campaign Objectives", "Marketing API Rate Limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/campaigns/", "processedAt": "2025-06-25T16:13:37.321Z", "processor": "openrouter-claude-sonnet-4"}