# Facebook Marketing API Bidding

## Summary
Comprehensive guide to Facebook's ad auction system, covering bidding strategies, optimization goals, budgets, and delivery mechanisms. Explains how bids and budgets work with Facebook's ad auction for API-created ads, with the same functionality as Facebook's native tools.

## Key Points
- Facebook's auction system works identically for API-created ads and native Facebook tool ads
- Five main concepts: Bid Strategies, Optimization Goals, Budgets, Pacing/Scheduling, and Billing Events
- Campaign Budget Optimization allows automatic budget distribution across ad sets
- Multiple bidding models available including CPM, CPA, and reach/frequency
- Bid Multipliers provide advanced bidding control but are available on limited basis

## Parameters
- bid_strategy
- optimization_goal
- budget
- billing_event
- bid_amount
- campaign_budget_optimization
- pacing_type
- bid_multiplier

## Content
# Facebook Marketing API Bidding

Learn how your bids and budget work with Facebook's ad auction and delivery. This covers bidding options, placing bids for desired action, setting budget limits and tracking ads delivery. Facebook's auction functions the same way for API-created ads as they do for ads from Facebook tools.

## Main Concepts

### Bid Strategies
Provide your bid preferences to control how much you're willing to pay for your advertising objectives.

### Optimization Goals
Define advertising goals you want to achieve when Facebook delivers your ads, allowing the system to optimize delivery accordingly.

### Budgets
Set spending limits and control how much you invest in your advertising campaigns.

### Pacing and Scheduling
Determine how your ads budget is spent over time, controlling the rate and timing of ad delivery.

### Billing Events
Defines events you want to pay for, including impressions, clicks, or various conversion actions.

## Common Use Cases

### Campaign Budget Optimization
Optimize the distribution of a campaign budget across your campaign's ad sets for maximum efficiency.

### Optimized Cost Per Mille Ads
Prioritize your marketing goals and automatically deliver ads towards these goals in the most effective way possible.

### Cost Per Action Ads
Specify conversion events and get charged by the amount of conversions rather than impressions or clicks.

### Reach and Frequency
Bid on a predicted unique audience reach for your ads on Facebook and Instagram and control display frequency.

### Bid Multipliers
Maintain a nuanced bidding strategy within a single ad set with one targeted audience. **Note: Available on a limited basis.**

## Documentation Structure

### Overview
Core concepts and usage requirements covering Budgets, Optimization Goals, and Bid Strategies.

### Guides
Use case based guides to help you perform specific bidding and optimization actions.

### Support
Access to FAQs, API updates, helpful links, reference pages, and Ads Help Center resources.

---
**Tags:** bidding, auction, budget, optimization, campaign-management, cost-control, delivery, facebook-ads
**Difficulty:** intermediate
**Content Type:** overview
**Source:** https://developers.facebook.com/docs/marketing-api/bidding
**Processed:** 2025-06-25T15:06:29.820Z