{"title": "Facebook Marketing API - Ad Set Reference", "summary": "Complete reference documentation for Facebook Marketing API Ad Sets, including creation, reading, updating, and deletion operations. Ad sets group ads with shared budget, schedule, bid type, and targeting data.", "content": "# Facebook Marketing API - Ad Set Reference\n\n## Overview\n\nAn ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data. Ad sets enable you to group ads according to your criteria, and you can retrieve the ad-related statistics that apply to a set.\n\n## Creating Ad Sets\n\n### Daily Budget Example\n\n```bash\ncurl -X POST \\\n  -F 'name=\"My Reach Ad Set\"' \\\n  -F 'optimization_goal=\"REACH\"' \\\n  -F 'billing_event=\"IMPRESSIONS\"' \\\n  -F 'bid_amount=2' \\\n  -F 'daily_budget=1000' \\\n  -F 'campaign_id=\"<AD_CAMPAIGN_ID>\"' \\\n  -F 'targeting={\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ]\n       },\n       \"facebook_positions\": [\n         \"feed\"\n       ]\n     }' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'promoted_object={\n       \"page_id\": \"<PAGE_ID>\"\n     }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n### Lifetime Budget Example\n\n```bash\ncurl -X POST \\\n  -F 'name=\"My First Adset\"' \\\n  -F 'lifetime_budget=20000' \\\n  -F 'start_time=\"2025-06-25T08:51:33-0700\"' \\\n  -F 'end_time=\"2025-07-05T08:51:33-0700\"' \\\n  -F 'campaign_id=\"<AD_CAMPAIGN_ID>\"' \\\n  -F 'bid_amount=100' \\\n  -F 'billing_event=\"LINK_CLICKS\"' \\\n  -F 'optimization_goal=\"LINK_CLICKS\"' \\\n  -F 'targeting={\n       \"facebook_positions\": [\n         \"feed\"\n       ],\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ]\n       },\n       \"publisher_platforms\": [\n         \"facebook\",\n         \"audience_network\"\n       ]\n     }' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\n```\n\n## Limits\n\n| Limit | Value |\n|-------|-------|\n| Maximum ad sets per regular ad account | 5000 non-deleted ad sets |\n| Maximum ad sets per bulk ad account | 10000 non-deleted ad sets |\n| Maximum ads per ad set | 50 non-archived ads |\n\n## Special Requirements\n\n### Housing, Employment and Credit Ads\n\nAdvertisers must specify a `special_ad_category` for ad campaigns that market housing, employment, and credit. This restricts available targeting options.\n\n### European Union Targeting Requirements\n\nBeginning May 16, 2023, advertisers targeting the EU must provide:\n- **Beneficiary information** (`dsa_beneficiary`): Who benefits from the ad\n- **Payor information** (`dsa_payor`): Who pays for the ad\n\nWithout this information, ads will not be published starting August 16, 2023.\n\n## Reading Ad Sets\n\n### Basic Read Example\n\n```bash\ncurl -X GET \\\n  -d 'fields=\"name,status\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\n```\n\n### Read with UNIX Timestamp Format\n\n```bash\ncurl -X GET \\\n  -d 'fields=\"id,name,start_time,end_time\"' \\\n  -d 'date_format=\"U\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\n```\n\n### Read Multiple Ad Sets (PHP)\n\n```php\nuse FacebookAds\\Object\\AdSet;\nuse FacebookAds\\Object\\Fields\\AdSetFields;\n\n$ad_set_ids = array(<AD_SET_1_ID>, <AD_SET_2_ID>, <AD_SET_3_ID>);\n$fields = array(\n  AdSetFields::NAME,\n  AdSetFields::CONFIGURED_STATUS,\n  AdSetFields::EFFECTIVE_STATUS,\n);\n$adsets = AdSet::readIds($ad_set_ids, $fields);\n\nforeach ($adsets as $adset) {\n  echo $adset->{AdSetFields::NAME}.PHP_EOL;\n}\n```\n\n## Key Fields\n\n### Required Fields\n- `name`: Ad set name (max 400 characters)\n- `campaign_id`: Parent campaign ID\n- `targeting`: Targeting specification (countries required)\n- `optimization_goal`: What to optimize for\n- `billing_event`: When to charge\n- Budget: Either `daily_budget` or `lifetime_budget`\n\n### Budget Fields\n- `daily_budget`: Daily budget in account currency\n- `lifetime_budget`: Total budget in account currency\n- `daily_min_spend_target`: Minimum daily spend target\n- `daily_spend_cap`: Maximum daily spend\n- `lifetime_min_spend_target`: Minimum lifetime spend target\n- `lifetime_spend_cap`: Maximum lifetime spend\n\n### Bid Strategy Options\n- `LOWEST_COST_WITHOUT_CAP`: Automatic bidding\n- `LOWEST_COST_WITH_BID_CAP`: Manual maximum-cost bidding\n- `COST_CAP`: Target cost bidding\n- `LOWEST_COST_WITH_MIN_ROAS`: Minimum ROAS bidding\n\n### Optimization Goals\n- `APP_INSTALLS`: Optimize for app installations\n- `LINK_CLICKS`: Optimize for link clicks\n- `CONVERSIONS`: Optimize for conversions\n- `REACH`: Optimize for unique reach\n- `IMPRESSIONS`: Maximize impressions\n- `PAGE_LIKES`: Optimize for page likes\n- `POST_ENGAGEMENT`: Optimize for post engagement\n- `THRUPLAY`: Optimize for video completion\n- `VALUE`: Optimize for purchase value\n\n### Billing Events\n- `IMPRESSIONS`: Pay per impression\n- `CLICKS`: Pay per click\n- `LINK_CLICKS`: Pay per link click\n- `APP_INSTALLS`: Pay per app install\n- `THRUPLAY`: Pay per video completion\n- `POST_ENGAGEMENT`: Pay per engagement\n\n## Updating Ad Sets\n\n```bash\ncurl -X POST \\\n  -F 'billing_event=\"IMPRESSIONS\"' \\\n  -F 'optimization_goal=\"LINK_CLICKS\"' \\\n  -F 'bid_amount=200' \\\n  -F 'targeting={\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ]\n       },\n       \"facebook_positions\": [\n         \"feed\"\n       ]\n     }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\n```\n\n### Budget Update Restrictions\n- New budget must be at least 10% greater than amount already spent\n- Must meet minimum budget requirements\n- Archived ad sets can only update `name` and `campaign_status`\n\n## Deleting Ad Sets\n\n```bash\ncurl -X DELETE \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\n```\n\n## Minimum Budget Requirements\n\n### LOWEST_COST_WITHOUT_CAP Strategy\n\n| Billing Event | Minimum Daily Budget |\n|---------------|---------------------|\n| Impressions | $0.50 |\n| Clicks/Likes/Video Views | $2.50 |\n| Low-frequency Actions | $40 |\n\n### LOWEST_COST_WITH_BID_CAP Strategy\n\n| Billing Event | Minimum Daily Budget |\n|---------------|---------------------|\n| Impressions | At least the bid_amount |\n| Clicks/Actions | 5x the bid_amount |\n\n**Note**: For certain countries (US, UK, Canada, etc.), minimum values are 2x the standard amounts.\n\n## Error Codes\n\n| Error | Description |\n|-------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 2500 | Error parsing graph query |\n| 2635 | Deprecated API version |\n| 80004 | Too many calls to ad account |\n\n## Outcome-Driven Ads Experiences (ODAX)\n\nNew campaign structure with specific objective mappings:\n\n### ODAX Objectives\n- **Awareness**: Reach, Brand Awareness\n- **Traffic**: Drive traffic to destinations\n- **Engagement**: Encourage interactions\n- **Leads**: Generate lead information\n- **App Promotion**: Drive app installs\n- **Sales**: Drive purchases and conversions\n\nEach objective has specific destination types, optimization goals, and promoted objects.", "keyPoints": ["Ad sets group ads with shared budget, schedule, bid type, and targeting data", "EU-targeted ad sets require dsa_payor and dsa_beneficiary fields starting August 2023", "Minimum budget requirements vary by billing event and bid strategy", "Budget updates must be at least 10% greater than amount already spent", "Special ad categories (housing, employment, credit) have restricted targeting options"], "apiEndpoints": ["GET /v23.0/<AD_SET_ID>/", "POST /v23.0/act_<AD_ACCOUNT_ID>/adsets", "POST /v23.0/<AD_SET_ID>/", "DELETE /v23.0/<AD_SET_ID>/", "POST /v23.0/<AD_SET_ID>/copies"], "parameters": ["name", "campaign_id", "targeting", "optimization_goal", "billing_event", "daily_budget", "lifetime_budget", "bid_amount", "bid_strategy", "start_time", "end_time", "status", "promoted_object", "dsa_payor", "dsa_beneficiary"], "examples": ["Daily budget ad set creation", "Lifetime budget ad set creation", "Reading ad set with specific fields", "Reading multiple ad sets", "Updating ad set targeting and bid", "Deleting an ad set"], "tags": ["Facebook Marketing API", "Ad Sets", "Advertising", "Budget Management", "Targeting", "Optimization", "EU Compliance", "ODAX"], "relatedTopics": ["Ad Campaigns", "Ad Creatives", "Targeting Specifications", "Budget Limits", "Bid Strategies", "Special Ad Categories", "European Union DSA Requirements", "Outcome-Driven Ads Experiences"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-campaign", "processedAt": "2025-06-25T15:52:27.531Z", "processor": "openrouter-claude-sonnet-4"}