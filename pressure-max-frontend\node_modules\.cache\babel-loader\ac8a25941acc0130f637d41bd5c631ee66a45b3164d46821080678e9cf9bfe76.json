{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\nconst supabaseUrl = 'https://pocxgdrtvwxjaurqhoua.supabase.co';\nconst supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvY3hnZHJ0dnd4amF1cnFob3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5ODE3MDgsImV4cCI6MjA2NjU1NzcwOH0.NoRnR1vody2Em2kc3GKXKjqAWZHAwnqCpGSIIX1H1Cg';\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  }\n});", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "supabaseAnonKey", "supabase", "auth", "autoRefreshToken", "persistSession", "detectSessionInUrl"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/lib/supabase.js"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = 'https://pocxgdrtvwxjaurqhoua.supabase.co';\nconst supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvY3hnZHJ0dnd4amF1cnFob3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5ODE3MDgsImV4cCI6MjA2NjU1NzcwOH0.NoRnR1vody2Em2kc3GKXKjqAWZHAwnqCpGSIIX1H1Cg';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true\n  }\n});\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AAEpD,MAAMC,WAAW,GAAG,0CAA0C;AAC9D,MAAMC,eAAe,GAAG,kNAAkN;AAE1O,OAAO,MAAMC,QAAQ,GAAGH,YAAY,CAACC,WAAW,EAAEC,eAAe,EAAE;EACjEE,IAAI,EAAE;IACJC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE;EACtB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}