[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js": "14", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js": "15", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js": "16"}, {"size": 232, "mtime": 1750870627179, "results": "17", "hashOfConfig": "18"}, {"size": 1544, "mtime": 1750988394638, "results": "19", "hashOfConfig": "18"}, {"size": 10186, "mtime": 1750904956981, "results": "20", "hashOfConfig": "18"}, {"size": 18514, "mtime": 1750938756494, "results": "21", "hashOfConfig": "18"}, {"size": 7324, "mtime": 1750904956983, "results": "22", "hashOfConfig": "18"}, {"size": 8243, "mtime": 1750873838649, "results": "23", "hashOfConfig": "18"}, {"size": 3438, "mtime": 1750870723227, "results": "24", "hashOfConfig": "18"}, {"size": 4416, "mtime": 1750938756489, "results": "25", "hashOfConfig": "18"}, {"size": 4531, "mtime": 1750906642416, "results": "26", "hashOfConfig": "18"}, {"size": 4881, "mtime": 1750906513515, "results": "27", "hashOfConfig": "18"}, {"size": 15521, "mtime": 1750913282515, "results": "28", "hashOfConfig": "18"}, {"size": 11191, "mtime": 1750915070114, "results": "29", "hashOfConfig": "18"}, {"size": 28034, "mtime": 1750938756493, "results": "30", "hashOfConfig": "18"}, {"size": 21576, "mtime": 1750938756324, "results": "31", "hashOfConfig": "18"}, {"size": 2817, "mtime": 1750988380203, "results": "32", "hashOfConfig": "18"}, {"size": 11213, "mtime": 1750988786279, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["82", "83"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["84", "85"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["86"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["87", "88"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["89"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["90"], ["91", "92"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js", [], ["93", "94"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js", ["95", "96", "97", "98", "99", "100", "101", "102", "103", "104"], [], {"ruleId": "105", "severity": 1, "message": "106", "line": 65, "column": 6, "nodeType": "107", "endLine": 65, "endColumn": 23, "suggestions": "108"}, {"ruleId": "105", "severity": 1, "message": "106", "line": 76, "column": 6, "nodeType": "107", "endLine": 76, "endColumn": 46, "suggestions": "109"}, {"ruleId": "110", "severity": 1, "message": "111", "line": 8, "column": 28, "nodeType": "112", "messageId": "113", "endLine": 8, "endColumn": 32}, {"ruleId": "105", "severity": 1, "message": "114", "line": 22, "column": 6, "nodeType": "107", "endLine": 22, "endColumn": 23, "suggestions": "115"}, {"ruleId": "105", "severity": 1, "message": "116", "line": 22, "column": 6, "nodeType": "107", "endLine": 22, "endColumn": 8, "suggestions": "117"}, {"ruleId": "110", "severity": 1, "message": "118", "line": 5, "column": 20, "nodeType": "112", "messageId": "113", "endLine": 5, "endColumn": 25}, {"ruleId": "105", "severity": 1, "message": "119", "line": 25, "column": 6, "nodeType": "107", "endLine": 25, "endColumn": 32, "suggestions": "120"}, {"ruleId": "105", "severity": 1, "message": "121", "line": 24, "column": 6, "nodeType": "107", "endLine": 24, "endColumn": 8, "suggestions": "122"}, {"ruleId": "110", "severity": 1, "message": "123", "line": 23, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 23, "endColumn": 27}, {"ruleId": "105", "severity": 1, "message": "124", "line": 87, "column": 6, "nodeType": "107", "endLine": 87, "endColumn": 8, "suggestions": "125", "suppressions": "126"}, {"ruleId": "105", "severity": 1, "message": "127", "line": 94, "column": 6, "nodeType": "107", "endLine": 94, "endColumn": 23, "suggestions": "128", "suppressions": "129"}, {"ruleId": "105", "severity": 1, "message": "119", "line": 108, "column": 6, "nodeType": "107", "endLine": 108, "endColumn": 8, "suggestions": "130", "suppressions": "131"}, {"ruleId": "105", "severity": 1, "message": "127", "line": 115, "column": 6, "nodeType": "107", "endLine": 115, "endColumn": 23, "suggestions": "132", "suppressions": "133"}, {"ruleId": "110", "severity": 1, "message": "134", "line": 6, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 6, "endColumn": 14}, {"ruleId": "110", "severity": 1, "message": "135", "line": 6, "column": 16, "nodeType": "112", "messageId": "113", "endLine": 6, "endColumn": 22}, {"ruleId": "110", "severity": 1, "message": "136", "line": 6, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 6, "endColumn": 27}, {"ruleId": "110", "severity": 1, "message": "137", "line": 6, "column": 29, "nodeType": "112", "messageId": "113", "endLine": 6, "endColumn": 35}, {"ruleId": "110", "severity": 1, "message": "111", "line": 10, "column": 11, "nodeType": "112", "messageId": "113", "endLine": 10, "endColumn": 15}, {"ruleId": "110", "severity": 1, "message": "138", "line": 10, "column": 51, "nodeType": "112", "messageId": "113", "endLine": 10, "endColumn": 57}, {"ruleId": "110", "severity": 1, "message": "139", "line": 10, "column": 59, "nodeType": "112", "messageId": "113", "endLine": 10, "endColumn": 67}, {"ruleId": "110", "severity": 1, "message": "140", "line": 13, "column": 10, "nodeType": "112", "messageId": "113", "endLine": 13, "endColumn": 19}, {"ruleId": "110", "severity": 1, "message": "141", "line": 13, "column": 21, "nodeType": "112", "messageId": "113", "endLine": 13, "endColumn": 33}, {"ruleId": "105", "severity": 1, "message": "142", "line": 35, "column": 6, "nodeType": "107", "endLine": 35, "endColumn": 23, "suggestions": "143"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["144"], ["145"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["146"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["147"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["148"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["149"], "'leadFormTemplates' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFormData'. Either include it or remove the dependency array.", ["150"], ["151"], "React Hook useEffect has a missing dependency: 'loadLeadForms'. Either include it or remove the dependency array.", ["152"], ["153"], ["154"], ["155"], ["156"], ["157"], "'User' is defined but never used.", "'LogOut' is defined but never used.", "'Eye' is defined but never used.", "'EyeOff' is defined but never used.", "'logout' is assigned a value but never used.", "'getToken' is assigned a value but never used.", "'showToken' is assigned a value but never used.", "'setShowToken' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleFacebookCallback'. Either include it or remove the dependency array.", ["158"], {"desc": "159", "fix": "160"}, {"desc": "161", "fix": "162"}, {"desc": "163", "fix": "164"}, {"desc": "165", "fix": "166"}, {"desc": "167", "fix": "168"}, {"desc": "169", "fix": "170"}, {"desc": "171", "fix": "172"}, {"kind": "173", "justification": "174"}, {"desc": "175", "fix": "176"}, {"kind": "173", "justification": "174"}, {"desc": "177", "fix": "178"}, {"kind": "173", "justification": "174"}, {"desc": "175", "fix": "179"}, {"kind": "173", "justification": "174"}, {"desc": "180", "fix": "181"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "182", "text": "183"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "184", "text": "185"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "186", "text": "187"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "188", "text": "189"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "190", "text": "191"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "192", "text": "193"}, "Update the dependencies array to be: [loadFormData]", {"range": "194", "text": "195"}, "directive", "", "Update the dependencies array to be: [loadLeadForms, selectedAccount]", {"range": "196", "text": "197"}, "Update the dependencies array to be: [loadAdAccounts]", {"range": "198", "text": "199"}, {"range": "200", "text": "197"}, "Update the dependencies array to be: [handleFacebookCallback, isAuthenticated]", {"range": "201", "text": "202"}, [2408, 2425], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2833, 2873], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]", [2300, 2302], "[loadFormData]", [2447, 2464], "[loadLeadForms, selectedAccount]", [2972, 2974], "[loadAdAccounts]", [3119, 3136], [1229, 1246], "[handleFacebookCallback, isAuthenticated]"]