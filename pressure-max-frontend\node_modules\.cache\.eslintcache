[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js": "14", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js": "15", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js": "16", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\lib\\supabase.js": "17", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Login.js": "18", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Signup.js": "19"}, {"size": 232, "mtime": 1750870627179, "results": "20", "hashOfConfig": "21"}, {"size": 1734, "mtime": 1750992208803, "results": "22", "hashOfConfig": "21"}, {"size": 10186, "mtime": 1750904956981, "results": "23", "hashOfConfig": "21"}, {"size": 18514, "mtime": 1750938756494, "results": "24", "hashOfConfig": "21"}, {"size": 7324, "mtime": 1750904956983, "results": "25", "hashOfConfig": "21"}, {"size": 8243, "mtime": 1750873838649, "results": "26", "hashOfConfig": "21"}, {"size": 3955, "mtime": 1750992163589, "results": "27", "hashOfConfig": "21"}, {"size": 4416, "mtime": 1750938756489, "results": "28", "hashOfConfig": "21"}, {"size": 4531, "mtime": 1750906642416, "results": "29", "hashOfConfig": "21"}, {"size": 4881, "mtime": 1750906513515, "results": "30", "hashOfConfig": "21"}, {"size": 15521, "mtime": 1750913282515, "results": "31", "hashOfConfig": "21"}, {"size": 11191, "mtime": 1750915070114, "results": "32", "hashOfConfig": "21"}, {"size": 28034, "mtime": 1750938756493, "results": "33", "hashOfConfig": "21"}, {"size": 21576, "mtime": 1750938756324, "results": "34", "hashOfConfig": "21"}, {"size": 2817, "mtime": 1750988380203, "results": "35", "hashOfConfig": "21"}, {"size": 32308, "mtime": 1750992242990, "results": "36", "hashOfConfig": "21"}, {"size": 526, "mtime": 1750992184196, "results": "37", "hashOfConfig": "21"}, {"size": 6445, "mtime": 1750992390395, "results": "38", "hashOfConfig": "21"}, {"size": 11522, "mtime": 1750992425150, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["97", "98"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["99", "100"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["101", "102"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["103"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["104"], ["105", "106"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js", [], ["107", "108"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js", ["109", "110", "111", "112", "113", "114", "115", "116", "117", "118"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\lib\\supabase.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Signup.js", [], [], {"ruleId": "119", "severity": 1, "message": "120", "line": 65, "column": 6, "nodeType": "121", "endLine": 65, "endColumn": 23, "suggestions": "122"}, {"ruleId": "119", "severity": 1, "message": "120", "line": 76, "column": 6, "nodeType": "121", "endLine": 76, "endColumn": 46, "suggestions": "123"}, {"ruleId": "124", "severity": 1, "message": "125", "line": 8, "column": 28, "nodeType": "126", "messageId": "127", "endLine": 8, "endColumn": 32}, {"ruleId": "119", "severity": 1, "message": "128", "line": 22, "column": 6, "nodeType": "121", "endLine": 22, "endColumn": 23, "suggestions": "129"}, {"ruleId": "124", "severity": 1, "message": "130", "line": 5, "column": 20, "nodeType": "126", "messageId": "127", "endLine": 5, "endColumn": 25}, {"ruleId": "119", "severity": 1, "message": "131", "line": 25, "column": 6, "nodeType": "121", "endLine": 25, "endColumn": 32, "suggestions": "132"}, {"ruleId": "119", "severity": 1, "message": "133", "line": 24, "column": 6, "nodeType": "121", "endLine": 24, "endColumn": 8, "suggestions": "134"}, {"ruleId": "124", "severity": 1, "message": "135", "line": 23, "column": 10, "nodeType": "126", "messageId": "127", "endLine": 23, "endColumn": 27}, {"ruleId": "119", "severity": 1, "message": "136", "line": 87, "column": 6, "nodeType": "121", "endLine": 87, "endColumn": 8, "suggestions": "137", "suppressions": "138"}, {"ruleId": "119", "severity": 1, "message": "139", "line": 94, "column": 6, "nodeType": "121", "endLine": 94, "endColumn": 23, "suggestions": "140", "suppressions": "141"}, {"ruleId": "119", "severity": 1, "message": "131", "line": 108, "column": 6, "nodeType": "121", "endLine": 108, "endColumn": 8, "suggestions": "142", "suppressions": "143"}, {"ruleId": "119", "severity": 1, "message": "139", "line": 115, "column": 6, "nodeType": "121", "endLine": 115, "endColumn": 23, "suggestions": "144", "suppressions": "145"}, {"ruleId": "124", "severity": 1, "message": "146", "line": 7, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 7, "endColumn": 11}, {"ruleId": "147", "severity": 1, "message": "148", "line": 525, "column": 19, "nodeType": "149", "endLine": 529, "endColumn": 25}, {"ruleId": "147", "severity": 1, "message": "148", "line": 632, "column": 23, "nodeType": "149", "endLine": 632, "endColumn": 59}, {"ruleId": "147", "severity": 1, "message": "148", "line": 633, "column": 23, "nodeType": "149", "endLine": 633, "endColumn": 59}, {"ruleId": "147", "severity": 1, "message": "148", "line": 640, "column": 23, "nodeType": "149", "endLine": 640, "endColumn": 59}, {"ruleId": "147", "severity": 1, "message": "148", "line": 641, "column": 23, "nodeType": "149", "endLine": 641, "endColumn": 59}, {"ruleId": "147", "severity": 1, "message": "148", "line": 642, "column": 23, "nodeType": "149", "endLine": 642, "endColumn": 59}, {"ruleId": "147", "severity": 1, "message": "148", "line": 643, "column": 23, "nodeType": "149", "endLine": 643, "endColumn": 59}, {"ruleId": "147", "severity": 1, "message": "148", "line": 671, "column": 17, "nodeType": "149", "endLine": 671, "endColumn": 53}, {"ruleId": "147", "severity": 1, "message": "148", "line": 672, "column": 17, "nodeType": "149", "endLine": 672, "endColumn": 53}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["150"], ["151"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["152"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["153"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["154"], "'leadFormTemplates' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFormData'. Either include it or remove the dependency array.", ["155"], ["156"], "React Hook useEffect has a missing dependency: 'loadLeadForms'. Either include it or remove the dependency array.", ["157"], ["158"], ["159"], ["160"], ["161"], ["162"], "'Sparkles' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"desc": "163", "fix": "164"}, {"desc": "165", "fix": "166"}, {"desc": "167", "fix": "168"}, {"desc": "169", "fix": "170"}, {"desc": "171", "fix": "172"}, {"desc": "173", "fix": "174"}, {"kind": "175", "justification": "176"}, {"desc": "177", "fix": "178"}, {"kind": "175", "justification": "176"}, {"desc": "179", "fix": "180"}, {"kind": "175", "justification": "176"}, {"desc": "177", "fix": "181"}, {"kind": "175", "justification": "176"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "182", "text": "183"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "184", "text": "185"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "186", "text": "187"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "188", "text": "189"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "190", "text": "191"}, "Update the dependencies array to be: [loadFormData]", {"range": "192", "text": "193"}, "directive", "", "Update the dependencies array to be: [loadLeadForms, selectedAccount]", {"range": "194", "text": "195"}, "Update the dependencies array to be: [loadAdAccounts]", {"range": "196", "text": "197"}, {"range": "198", "text": "195"}, [2408, 2425], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2833, 2873], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]", [2300, 2302], "[loadFormData]", [2447, 2464], "[loadLeadForms, selectedAccount]", [2972, 2974], "[loadAdAccounts]", [3119, 3136]]