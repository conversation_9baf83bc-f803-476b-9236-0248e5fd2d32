[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js": "14", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js": "15", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js": "16"}, {"size": 232, "mtime": 1750870627179, "results": "17", "hashOfConfig": "18"}, {"size": 1544, "mtime": 1750988394638, "results": "19", "hashOfConfig": "18"}, {"size": 10186, "mtime": 1750904956981, "results": "20", "hashOfConfig": "18"}, {"size": 18514, "mtime": 1750938756494, "results": "21", "hashOfConfig": "18"}, {"size": 7324, "mtime": 1750904956983, "results": "22", "hashOfConfig": "18"}, {"size": 8243, "mtime": 1750873838649, "results": "23", "hashOfConfig": "18"}, {"size": 3438, "mtime": 1750870723227, "results": "24", "hashOfConfig": "18"}, {"size": 4416, "mtime": 1750938756489, "results": "25", "hashOfConfig": "18"}, {"size": 4531, "mtime": 1750906642416, "results": "26", "hashOfConfig": "18"}, {"size": 4881, "mtime": 1750906513515, "results": "27", "hashOfConfig": "18"}, {"size": 15521, "mtime": 1750913282515, "results": "28", "hashOfConfig": "18"}, {"size": 11191, "mtime": 1750915070114, "results": "29", "hashOfConfig": "18"}, {"size": 28034, "mtime": 1750938756493, "results": "30", "hashOfConfig": "18"}, {"size": 21576, "mtime": 1750938756324, "results": "31", "hashOfConfig": "18"}, {"size": 2817, "mtime": 1750988380203, "results": "32", "hashOfConfig": "18"}, {"size": 24051, "mtime": 1750990409809, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["82", "83"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["84", "85"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["86"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["87", "88"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["89"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["90"], ["91", "92"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js", [], ["93", "94"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Dashboard.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\pages\\Landing.js", ["95", "96", "97", "98", "99", "100", "101", "102", "103"], [], {"ruleId": "104", "severity": 1, "message": "105", "line": 65, "column": 6, "nodeType": "106", "endLine": 65, "endColumn": 23, "suggestions": "107"}, {"ruleId": "104", "severity": 1, "message": "105", "line": 76, "column": 6, "nodeType": "106", "endLine": 76, "endColumn": 46, "suggestions": "108"}, {"ruleId": "109", "severity": 1, "message": "110", "line": 8, "column": 28, "nodeType": "111", "messageId": "112", "endLine": 8, "endColumn": 32}, {"ruleId": "104", "severity": 1, "message": "113", "line": 22, "column": 6, "nodeType": "106", "endLine": 22, "endColumn": 23, "suggestions": "114"}, {"ruleId": "104", "severity": 1, "message": "115", "line": 22, "column": 6, "nodeType": "106", "endLine": 22, "endColumn": 8, "suggestions": "116"}, {"ruleId": "109", "severity": 1, "message": "117", "line": 5, "column": 20, "nodeType": "111", "messageId": "112", "endLine": 5, "endColumn": 25}, {"ruleId": "104", "severity": 1, "message": "118", "line": 25, "column": 6, "nodeType": "106", "endLine": 25, "endColumn": 32, "suggestions": "119"}, {"ruleId": "104", "severity": 1, "message": "120", "line": 24, "column": 6, "nodeType": "106", "endLine": 24, "endColumn": 8, "suggestions": "121"}, {"ruleId": "109", "severity": 1, "message": "122", "line": 23, "column": 10, "nodeType": "111", "messageId": "112", "endLine": 23, "endColumn": 27}, {"ruleId": "104", "severity": 1, "message": "123", "line": 87, "column": 6, "nodeType": "106", "endLine": 87, "endColumn": 8, "suggestions": "124", "suppressions": "125"}, {"ruleId": "104", "severity": 1, "message": "126", "line": 94, "column": 6, "nodeType": "106", "endLine": 94, "endColumn": 23, "suggestions": "127", "suppressions": "128"}, {"ruleId": "104", "severity": 1, "message": "118", "line": 108, "column": 6, "nodeType": "106", "endLine": 108, "endColumn": 8, "suggestions": "129", "suppressions": "130"}, {"ruleId": "104", "severity": 1, "message": "126", "line": 115, "column": 6, "nodeType": "106", "endLine": 115, "endColumn": 23, "suggestions": "131", "suppressions": "132"}, {"ruleId": "133", "severity": 1, "message": "134", "line": 423, "column": 19, "nodeType": "135", "endLine": 427, "endColumn": 25}, {"ruleId": "133", "severity": 1, "message": "134", "line": 496, "column": 23, "nodeType": "135", "endLine": 496, "endColumn": 59}, {"ruleId": "133", "severity": 1, "message": "134", "line": 497, "column": 23, "nodeType": "135", "endLine": 497, "endColumn": 59}, {"ruleId": "133", "severity": 1, "message": "134", "line": 504, "column": 23, "nodeType": "135", "endLine": 504, "endColumn": 59}, {"ruleId": "133", "severity": 1, "message": "134", "line": 505, "column": 23, "nodeType": "135", "endLine": 505, "endColumn": 59}, {"ruleId": "133", "severity": 1, "message": "134", "line": 506, "column": 23, "nodeType": "135", "endLine": 506, "endColumn": 59}, {"ruleId": "133", "severity": 1, "message": "134", "line": 507, "column": 23, "nodeType": "135", "endLine": 507, "endColumn": 59}, {"ruleId": "133", "severity": 1, "message": "134", "line": 535, "column": 17, "nodeType": "135", "endLine": 535, "endColumn": 53}, {"ruleId": "133", "severity": 1, "message": "134", "line": 536, "column": 17, "nodeType": "135", "endLine": 536, "endColumn": 53}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["136"], ["137"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["138"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["139"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["140"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["141"], "'leadFormTemplates' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFormData'. Either include it or remove the dependency array.", ["142"], ["143"], "React Hook useEffect has a missing dependency: 'loadLeadForms'. Either include it or remove the dependency array.", ["144"], ["145"], ["146"], ["147"], ["148"], ["149"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"desc": "150", "fix": "151"}, {"desc": "152", "fix": "153"}, {"desc": "154", "fix": "155"}, {"desc": "156", "fix": "157"}, {"desc": "158", "fix": "159"}, {"desc": "160", "fix": "161"}, {"desc": "162", "fix": "163"}, {"kind": "164", "justification": "165"}, {"desc": "166", "fix": "167"}, {"kind": "164", "justification": "165"}, {"desc": "168", "fix": "169"}, {"kind": "164", "justification": "165"}, {"desc": "166", "fix": "170"}, {"kind": "164", "justification": "165"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "171", "text": "172"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "173", "text": "174"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "175", "text": "176"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "177", "text": "178"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "179", "text": "180"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [loadFormData]", {"range": "183", "text": "184"}, "directive", "", "Update the dependencies array to be: [loadLeadForms, selectedAccount]", {"range": "185", "text": "186"}, "Update the dependencies array to be: [loadAdAccounts]", {"range": "187", "text": "188"}, {"range": "189", "text": "186"}, [2408, 2425], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2833, 2873], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]", [2300, 2302], "[loadFormData]", [2447, 2464], "[loadLeadForms, selectedAccount]", [2972, 2974], "[loadAdAccounts]", [3119, 3136]]