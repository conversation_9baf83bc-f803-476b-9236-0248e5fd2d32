{"title": "Brand Safety and Suitability", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_5_XR\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#brand-safety-and-suitability\">Brand Safety and Suitability</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#documentation-links\">Documentation Links</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#integration-setup\">Integration Setup</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#block-lists-api\">Block Lists API</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#content-allow-lists-api\">Content Allow Lists API</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#content-delivery-reports-api\">Content Delivery Reports API</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#feed-verification-api\">Feed Verification API</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#partner-publisher-lists-api\">Partner-publisher Lists API</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#passback-api\">Passback API</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#publisher-delivery-reports-api\">Publisher Delivery Reports API</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_0_bX\"><div class=\"_4cel\"><span data-click-area=\"main\"><div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8\"><div class=\"_4-u3 _588p\"><h1 id=\"brand-safety-and-suitability\">Brand Safety and Suitability</h1>\n\n<p>Meta offers several brand suitability controls to help you place ads adjacent to organic content that is more suitable for your brand on Facebook, Instagram and Meta Audience Network. You can apply one of these controls or use them in combination. Meta keeps your brand safe by enforcing <a href=\"https://www.facebook.com/business/brand-safety/media-responsibility\">Facebook Community Standards</a> and <a href=\"https://www.facebook.com/help/instagram/477434105621119\">Instagram Community Guidelines</a> for all content and publishers. <a href=\"https://www.facebook.com/business/help/1559334364175848?id=1769156093197771\">Learn more about brand suitability</a>.</p>\n</div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"documentation-links\">Documentation Links</h2>\n<table class=\"uiGrid _51mz _57v1 _5f0n\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"integration-setup\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/brand-safety-partners\">Integration Setup</a></h3>\n\n<p>An overview of initial setup steps required for program participation. The main elements it addresses include: setting up a business in Business Manager, creating and obtaining access to ad accounts, and creating an app to access Meta’s API.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"block-lists-api\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/block-list\">Block Lists API</a></h3>\n\n<p>Block lists stop your ads from appearing with publishers you don't consider suitable for your brand or campaign.</p>\n</td></tr><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"content-allow-lists-api\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/content-allow-lists\">Content Allow Lists API</a></h3>\n\n<p>Content Allow Lists give you the ability to work with trusted Meta Business Partners to review and customize lists of brand suitable videos for running Facebook in-stream campaigns.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"content-delivery-reports-api\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/content-delivery-report\">Content Delivery Reports API</a></h3>\n\n<p>Content delivery reports provide transparency into where ads appeared and show impressions at the content level.</p>\n</td></tr><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"feed-verification-api\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/feed-verification\">Feed Verification API</a></h3>\n\n<p>Feed verification allows you to measure, verify and understand the suitability of content near your ads to help you make informed decisions in order to reach your marketing goals.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"partner-publisher-lists-api\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/publisher-list\">Partner-publisher Lists API</a></h3>\n\n<p>Partner-publisher lists show publishers that have signed up for monetization and follow our Partner Monetization Policies.</p>\n</td></tr><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"passback-api\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/passback-api\">Passback API</a></h3>\n\n<p>Passback allows Meta Business Partners to share content risk labels and campaign performance data with Meta. The goals are to provide advertisers and partners with a mechanism to give feedback on content, for Meta to be able to take action on that feedback, and for Meta and partners to be able to compare content labels.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"publisher-delivery-reports-api\"><a href=\"/docs/marketing-api/brand-safety-and-suitability/publisher-delivery-report\">Publisher Delivery Reports API</a></h3>\n\n<p>Publisher delivery reports provide transparency into where ads appeared and show impressions at the publisher level.</p>\n</td></tr></tbody></table><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div></span><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p _4_k\"><fb:like href=\"https://developers.facebook.com/docs/marketing-api/brand-safety-and-suitability/\" layout=\"button_count\" share=\"1\"></fb:like><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div id=\"developer_documentation_toolbar\" data-referrer=\"developer_documentation_toolbar\" data-click-area=\"toolbar\"></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '217404712025032');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability/brand-safety-partners", "/docs/marketing-api/brand-safety-and-suitability/block-list", "/docs/marketing-api/brand-safety-and-suitability/content-allow-lists", "/docs/marketing-api/brand-safety-and-suitability/content-delivery-report", "/docs/marketing-api/brand-safety-and-suitability/feed-verification", "/docs/marketing-api/brand-safety-and-suitability/publisher-delivery-report", "/docs/marketing-api/brand-safety-and-suitability/passback-api", "/docs/marketing-api/brand-safety-and-suitability/publisher-list", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/marketing-api-changelog"], "url": "https://developers.facebook.com/docs/marketing-api/brand-safety-and-suitability", "timestamp": "2025-06-25T15:07:51.870Z"}