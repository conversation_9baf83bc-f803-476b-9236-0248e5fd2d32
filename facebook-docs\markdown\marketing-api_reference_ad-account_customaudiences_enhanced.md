# Facebook Marketing API - Ad Account Custom Audiences Reference

## Summary
Complete reference documentation for managing custom audiences within Facebook ad accounts, including reading, creating, and managing audience data through the Marketing API. Covers endpoints, parameters, examples, and error handling for custom audience operations.

## Key Points
- Custom audiences can be read, created, but not updated or deleted through this endpoint
- Maximum limit of 500 custom audiences per ad account
- Creating custom audiences requires a two-step process: create blank audience, then add users
- Various audience subtypes available including CUSTOM, WEBSITE, APP, and LOOKALIKE
- Rate limiting applies with specific error code 80003 for too many calls

## API Endpoints
- `GET /v23.0/act_<AD_ACCOUNT_ID>/customaudiences`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/customaudiences`

## Parameters
- business_id
- fetch_primary_audience
- fields
- filtering
- pixel_id
- name
- subtype
- description
- customer_file_source
- retention_days
- rule
- lookalike_spec

## Content
# Ad Account Custom Audiences

The custom audiences associated with the ad account. This endpoint allows you to manage custom audiences for targeting in Facebook advertising campaigns.

**Note:** To retrieve the IDs of lookalike audiences based on your custom audiences, use the `lookalike_audience_ids` field. See [Lookalike Audiences - Managing Audiences](/docs/marketing-api/audiences/guides/lookalike-audiences#read) for more information.

## Reading Custom Audiences

Retrieve custom audiences associated with an ad account.

### Endpoint
```
GET /v23.0/act_<AD_ACCOUNT_ID>/customaudiences
```

### Example Request
```bash
curl -X GET -G \
  -d 'fields="id"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `business_id` | numeric string or integer | Optional. Assists with filters, such as recently used |
| `fetch_primary_audience` | boolean | Default: `false`. Fetch primary audience |
| `fields` | list<string> | Fields to be retrieved. Default behavior returns only IDs |
| `filtering` | list<Filter Object> | Filters on the report data |
| `pixel_id` | numeric string | Optional. Fetches audiences associated to specific pixel |

#### Filter Object Structure
| Field | Type | Description |
|-------|------|-------------|
| `field` | string | Required. Field to filter on |
| `operator` | enum | Required. Filter operator (EQUAL, NOT_EQUAL, etc.) |
| `value` | string | Required. Filter value |

### Response Format
```json
{
  "data": [],
  "paging": {}
}
```

## Creating Custom Audiences

Create a new custom audience. You can create a maximum of 500 custom audiences per ad account.

### Endpoint
```
POST /v23.0/act_<AD_ACCOUNT_ID>/customaudiences
```

### Example Request
```bash
curl -X POST \
  -F 'name="My new Custom Audience"' \
  -F 'subtype="CUSTOM"' \
  -F 'description="People who purchased on my website"' \
  -F 'customer_file_source="USER_PROVIDED_ONLY"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences
```

### Key Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `name` | string | The name of this custom audience |
| `subtype` | enum | Type of custom audience (CUSTOM, WEBSITE, APP, etc.) |
| `description` | string | The description for this custom audience |
| `customer_file_source` | enum | Source of customer information |
| `pixel_id` | numeric string | The pixel associated with this audience |
| `retention_days` | int64 | Days to keep user in cluster (1-180, default: forever) |
| `rule` | string | Audience rule for website custom audiences |
| `lookalike_spec` | JSON string | Specification for creating lookalike audience |

### Subtype Options
- `CUSTOM`: Standard custom audience (limit: 500)
- `WEBSITE`: Website traffic audience
- `APP`: Mobile app audience
- `LOOKALIKE`: Lookalike audience (limit: 10,000)
- `OFFLINE_CONVERSION`: Offline events audience
- And more...

### Return Type
```json
{
  "id": "numeric_string",
  "message": "string"
}
```

## Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | Action deemed abusive or disallowed |
| 2654 | Failed to create custom audience |
| 2663 | Terms of service not accepted |
| 2667 | Account permissions don't allow custom audience creation |
| 80003 | Too many calls to ad account (rate limiting) |

## Important Notes

1. **Audience Limits**: Maximum of 500 custom audiences per ad account
2. **Two-Step Process**: First create a blank audience, then add people via the users edge
3. **Rate Limiting**: Be aware of API rate limits for custom audience operations
4. **Permissions**: Ensure proper permissions for custom audience creation
5. **Terms of Service**: Must accept Facebook's custom audience terms of service

## Examples
GET request to retrieve custom audiences with fields parameter

POST request to create new custom audience with name, subtype, and description

cURL examples for both reading and creating operations

PHP SDK, JavaScript SDK, Android SDK, and iOS SDK code samples

---
**Tags:** Facebook Marketing API, Custom Audiences, Ad Account, Audience Management, Targeting, API Reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiences/  
**Processed:** 2025-06-25T16:26:05.649Z