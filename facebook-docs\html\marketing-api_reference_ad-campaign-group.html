<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_1U"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_VE"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_8h"></div></span></div></div>

<h1 id="overview">Campaign</h1>

<span><p>A campaign is the highest level organizational structure within an ad account and should represent a single objective for an advertiser, for example, to drive page post engagement. Setting objective of the campaign will enforce validation on any ads added to the campaign to ensure they also have the correct objective.</p>
<div><div class="_57yz _5s-k _3-8p"><div class="_57y-"><p>Facebook will no longer be able to aggregate non-inline conversion metric values across iOS 14.5 and non-iOS 14.5 campaigns due to differences in attribution logic. Querying across iOS 14.5 and non-iOS 14.5 campaigns will result in no data getting returned for non-inline conversion metrics such as app installs and purchases. Inline event metrics like impressions, link clicks, and video views, however, can still be aggregated. Please visit our <a href="https://developers.facebook.com/docs/graph-api/changelog/non-versioned-changes/jan-19-2021">changelog</a> for more information.</p>
</div></div></div><div class="_57yz _5s-k _3-8p"><div class="_57y-"><p>Ad campaigns that target iOS 14.5 must set the new <code>is_skadnetwork_attribution</code> field to <code>true</code>.</p>
</div></div><div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><div class="_57yz _57z1 _3-8p"><div class="_57y-"><p>The <code>date_preset = lifetime</code> parameter is disabled in Graph API v10.0 and replaced with <code>date_preset = maximum</code>, which returns a maximum of 37 months of data. For v9.0 and below, <code>date_preset = maximum</code> will be enabled on May 25, 2021, and any <code>lifetime</code> calls will default to <code>maximum</code> and return only 37 months of data.</p>
</div></div></div></div></div><h3>Limits</h3>

<ul>
<li>You can only create 200 ad sets per ad campaign. <a href="/docs/marketing-api/reference/ad-campaign-group">Learn more about the ad campaign structure</a>. </li>
<li>If your campaign has more than 70 ad sets and uses <a href="/docs/marketing-api/bidding/guides/campaign-budget-optimization">Campaign Budget Optimization</a>, you are not able to edit your current bid strategy or turn off CBO. <a href="https://www.facebook.com/business/help/519856662172206">Learn more in the Business Help Center</a>.</li>
</ul>

<h3>New Required Field for All Campaigns</h3>

<p>All businesses using the Marketing API must identify whether or not new and edited campaigns belong to a <a href="/docs/marketing-api/audiences/special-ad-category">Special Ad Category</a>. Current available categories are: <a href="/docs/marketing-api/audiences/special-ad-category/#context">housing, employment, credit</a>, or issues, elections, and politics. Businesses whose ads do not belong to a Special Ad Category must indicate NONE or send an empty array in the <code>special_ad_categories</code> field.</p>

<p>Businesses running <strong>housing</strong>, <strong>employment</strong>, or <strong>credit</strong> ads must comply with <a href="#hec-restrictions">targeting and audience restrictions</a>. Targeting for ads about social issues, elections or politics are not affected by the <code>special_ad_categories</code> label.</p>
<div class="_57yz _57z1 _3-8p"><div class="_57y-"><p>As of <strong>Marketing API 7.0</strong>, the <code>special_ad_category</code> parameter on the <a href="/docs/marketing-api/reference/ad-account/campaigns/#Creating"><code>POST /act_&lt;ad_account_id&gt;/campaigns</code></a> endpoint has been deprecated and replaced with a new <code>special_ad_categories</code> parameter. The new <code>special_ad_categories</code> parameter is required and accepts an array.</p>

<p>If you use the <code>special_ad_category</code> parameter, it will still return a string, but you should use <code>GET /{campaign-id}?fields=special_ad_categories</code> to get an array back. Refer  to <a href="https://developers.facebook.com/docs/marketing-api/audiences/special-ad-category/">Special Ad Category</a> for additional information.</p>
</div></div></span>

<div class="_57yz _57z1 _3-8p"><div class="_57y-"><p>The <code>date_preset = lifetime</code> parameter is disabled in Graph API v10.0 and replaced with <code>date_preset = maximum</code>, which returns a maximum of 37 months of data. For v9.0 and below, <code>date_preset = maximum</code> will be enabled on May 25, 2021, and any <code>lifetime</code> calls will default to <code>maximum</code> and return only 37 months of data.</p>
</div></div>

<h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>A campaign is a grouping of ad sets which are organized by the same business objective. Each campaign has an objective that must be valid across the ad sets within that campaign.</p>After your ads begin delivering, you can query stats for ad campaigns. The statistics returned will be unique stats, deduped across the ad sets. You can also get reports and statistics for all ad sets and ads in an campaign simultaneously. 
</div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_03"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_IQ">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_Mz">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_uc">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_lz">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_ur">iOS SDK</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=...%3Ffields%3D%257Bfieldname_of_type_Campaign%257D&amp;version=v23.0" target="_blank">Graph API Explorer</a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_b_18" style=""><code><span class="pln">GET v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/...?</span><span class="pln">fields</span><span class="pun">={</span><span class="pln">fieldname_of_type_Campaign</span><span class="pun">}</span><span class="pln"> HTTP</span><span class="pun">/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_g_Hk"><tr class="row_0"><td><div class="_yc"><span><code>date_preset</code></span></div><div class="_yb">enum{today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year}</div></td><td><p class="_yd"></p><div><div><p>Date Preset</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29 _5m27"><td><div class="_yc"><span><code>time_range</code></span></div><div class="_yb">{'since':YYYY-MM-DD,'until':YYYY-MM-DD}</div></td><td><p class="_yd"></p><div><div><p>Time Range. Note if time range is invalid, it will be ignored.</p>
</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id="fields">Fields</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><code>id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>Campaign's ID</p>
</div></div><p></p><div class="_2pic"><a href="https://developers.facebook.com/docs/graph-api/using-graph-api/#fields" target="blank"><span class="_1vet">Default</span></a></div></td></tr><tr><td><div class="_yc"><span><code>account_id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>ID of the ad account that owns this campaign</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb _yc"><a href="https://developers.facebook.com/docs/marketing-api/reference/ad-label/">list&lt;AdLabel&gt;</a></div></td><td><p class="_yd"></p><div><div><p>Ad Labels associated with this campaign</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>bid_strategy</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_h_m2"></a></div><div class="_yb _yc"><span>enum {LOWEST_COST_WITHOUT_CAP, LOWEST_COST_WITH_BID_CAP, COST_CAP, LOWEST_COST_WITH_MIN_ROAS}</span></div></td><td><p class="_yd"></p><div><div><p>Bid strategy for this campaign when you enable campaign budget optimization and
        when you use <code>AUCTION</code> as your buying type:<br>
        <code>LOWEST_COST_WITHOUT_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> without limiting your bid amount. This is the best strategy to select
        if you care most about cost efficiency. However, note that it may be harder to get
        stable average costs as you spend. Note: this strategy is also known as
        <em>automatic bidding</em>.
        Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br>
        <code>LOWEST_COST_WITH_BID_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> while limiting actual bid to a specified amount.
        Get specified bid cap in the <code>bid_amount</code> field for each ad set in this ad campaign.
        This strategy is known as <em>manual maximum-cost bidding</em>.
        Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br>
       <code>COST_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> while limiting actual average cost per optimization event to a specified amount.
        Get specified cost cap in the <code>bid_amount</code> field for each ad set in this ad campaign.
        Learn more in <a href="https://www.facebook.com/business/help/272336376749096?id=2196356200683573">Ads Help Center, About bid strategies: Cost Cap</a>.<br>Notes:</p>

<ul>
<li>If you do not enable campaign budget optimization, you should get <code>bid_strategy</code> at the ad set level.</li>
<li><code>TARGET_COST</code> bidding strategy has been deprecated with <a href="/docs/graph-api/changelog/version9.0">Marketing API v9</a>.</li>
</ul>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>boosted_object_id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>The Boosted Object this campaign has associated, if any</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>brand_lift_studies</code></span></div><div class="_yb _yc"><a href="https://developers.facebook.com/docs/marketing-api/reference/ad-study/">list&lt;AdStudy&gt;</a></div></td><td><p class="_yd"></p><div><div><p>Automated Brand Lift V2 studies for this ad set.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>budget_rebalance_flag</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>Whether to automatically rebalance budgets daily for all the adsets under this campaign. <a href="/docs/graph-api/changelog/version7.0#deprecations">This has been deprecated on Marketing API V7.0</a>.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>budget_remaining</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>Remaining budget</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>buying_type</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>Buying type, possible values are: <br><code>AUCTION</code>: default<br><code>RESERVED</code>: for <a href="/docs/marketing-api/reachandfrequency">reach and frequency ads</a>.<br><a href="/docs/marketing-api/reachandfrequency">Reach and Frequency</a> is disabled for <a href="/docs/marketing-api/special-ad-category">housing, employment and credit ads</a>.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>campaign_group_active_time</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>campaign_group_active_time this is only for Internal, This will have the active running length of Campaign Groups</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>can_create_brand_lift_study</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>If we can create a new automated brand lift study for the ad set.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>can_use_spend_cap</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>Whether the campaign can set the spend cap</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>configured_status</code></span></div><div class="_yb _yc"><span>enum {ACTIVE, PAUSED, DELETED, ARCHIVED}</span></div></td><td><p class="_yd"></p><div><div><p>If this status is <code>PAUSED</code>, all its active ad sets and ads will
        be paused and have an effective status <code>CAMPAIGN_PAUSED</code>. Prefer
        using 'status' instead of this.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>created_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>Created Time</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>daily_budget</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>The daily budget of the campaign</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>effective_status</code></span></div><div class="_yb _yc"><span>enum {ACTIVE, PAUSED, DELETED, ARCHIVED, IN_PROCESS, WITH_ISSUES}</span></div></td><td><p class="_yd"></p><div><div><p>IN_PROCESS is available for version 4.0 or higher</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>has_secondary_skadnetwork_reporting</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>has_secondary_skadnetwork_reporting</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>is_budget_schedule_enabled</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>Whether budget scheduling is enabled for the campaign group</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>is_skadnetwork_attribution</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>When set to <code>true</code> Indicates that the campaign will include SKAdNetwork, iOS 14+.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>issues_info</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_i_ex"></a></div><div class="_yb _yc"><a href="https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-issues-info/">list&lt;AdCampaignIssuesInfo&gt;</a></div></td><td><p class="_yd"></p><div><div><p>Issues for this campaign that prevented it from deliverying</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>last_budget_toggling_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>Last budget toggling time</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>lifetime_budget</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>The lifetime budget of the campaign</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>name</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>Campaign's name</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>objective</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>Campaign's objective</p>

<p>See the <a href="#odax">Outcome Ad-Driven Experience Objective Validation</a> section below for more information.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>pacing_type</code></span></div><div class="_yb _yc"><span>list&lt;string&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Defines pacing type of the campaign. The value is an array of options:  "standard".</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>primary_attribution</code></span></div><div class="_yb _yc"><span>enum</span></div></td><td><p class="_yd"></p><div><div><p>primary_attribution</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>promoted_object</code></span></div><div class="_yb _yc"><a href="https://developers.facebook.com/docs/marketing-api/reference/ad-promoted-object/">AdPromotedObject</a></div></td><td><p class="_yd"></p><div><div><p>The object this campaign is promoting across all its ads</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>smart_promotion_type</code></span></div><div class="_yb _yc"><span>enum</span></div></td><td><p class="_yd"></p><div><div><p>Smart Promotion Type. guided_creation or smart_app_promotion(the choice under APP_INSTALLS objective).</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>source_campaign</code></span></div><div class="_yb _yc"><a href="https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/">Campaign</a></div></td><td><p class="_yd"></p><div><div><p>The source campaign that this campaign is copied from</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>source_campaign_id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>The source campaign id that this campaign is copied from</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>special_ad_categories</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_j_nC"></a></div><div class="_yb _yc"><span>list&lt;enum&gt;</span></div></td><td><p class="_yd"></p><div><div><p>special ad categories</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>special_ad_category</code></span></div><div class="_yb _yc"><span>enum</span></div></td><td><p class="_yd"></p><div><div><p>The campaign's Special Ad Category. One of <code>HOUSING</code>, <code>EMPLOYMENT</code>, <code>CREDIT</code>, or <code>NONE</code>.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>special_ad_category_country</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_k_9Y"></a></div><div class="_yb _yc"><span>list&lt;enum&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Country field for  Special Ad Category.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>spend_cap</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>A spend cap for the campaign, such that it will not spend more than this cap. Expressed as integer value of the subunit in your currency.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>start_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>Merging of <code>start_time</code>s for the ad sets belonging to this campaign. At the campaign level, <code>start_time</code> is a read only field. You can setup <code>start_time</code> at the ad set level.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>status</code></span></div><div class="_yb _yc"><span>enum {ACTIVE, PAUSED, DELETED, ARCHIVED}</span></div></td><td><p class="_yd"></p><div><div><p>If this status is <code>PAUSED</code>, all its active ad sets and ads will
        be paused and have an effective status <code>CAMPAIGN_PAUSED</code>. The field
        returns the same value as 'configured_status', and is the suggested
        one to use.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>stop_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>Merging of <code>stop_time</code>s for the ad sets belonging to this campaign, if available. At the campaign level, <code>stop_time</code> is a read only field. You can setup <code>stop_time</code> at the ad set level.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>topline_id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>Topline ID</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>updated_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>Updated Time. If you update <code>spend_cap</code> or daily budget or lifetime budget, this will not automatically update this field.</p>
</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id="edges">Edges</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/ad-campaign-group/ad_studies/"><code>ad_studies</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdStudy&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The ad studies containing this campaign</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/ad-campaign-group/adrules_governed/"><code>adrules_governed</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdRule&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Ad rules that govern this campaign - by default, this only returns rules that either directly mention the campaign by id or indirectly through the set entity_type</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/ad-campaign-group/ads/"><code>ads</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Adgroup&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Ads under this campaign</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/ad-campaign-group/adsets/"><code>adsets</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdCampaign&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The ad sets under this campaign</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/ad-campaign-group/copies/"><code>copies</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdCampaignGroup&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The copies of this campaign</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>104</td><td>Incorrect signature</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>3018</td><td>The start date of the time range cannot be beyond 37 months from the current date</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr></tbody></table></div></div></div>

<h2 id="Creating">Creating</h2><div class="_844_"><div class="_3-98">You can make a POST request to <code>async_batch_requests</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-account/async_batch_requests/"><code>/act_{ad_account_id}/async_batch_requests</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> will be created.</div><div><h3 id="parameters-2">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_l_OS"><tr class="row_0 _5m27"><td><div class="_yc"><span><code>adbatch</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p>JSON encoded batch reqeust</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">UTF-8 encoded string</div></td><td><p class="_yd"></p><div><div><p>Name of the batch request for tracking purposes.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3 id="return-type">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div>}</div><h3 id="error-codes-2">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>194</td><td>Missing at least one required parameter</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can make a POST request to <code>copies</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-campaign-group/copies/"><code>/{campaign_id}/copies</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> will be created.</div><div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_m_ka"><tr class="row_0"><td><div class="_yc"><span><code>deep_copy</code></span></div><div class="_yb">boolean</div></td><td><div>Default value: <code>false</code></div><p class="_yd"></p><div><div><p>Whether to copy all the child ads. Limits: the total number of children ads to copy should not exceed 3 for a synchronous call and 51 for an asynchronous call.</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>end_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>For deep copy, the end time of the sets under the copied campaign, e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. UTC UNIX timestamp. When creating a set with a daily budget, specify <code>end_time=0</code> to set the set to be ongoing without end date. If not set, the copied sets will inherit the end time from the original set</p>
</div></div><p></p></td></tr><tr class="row_2 _5m27"><td><div class="_yc"><span><code>rename_options</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div><p>Rename options</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>start_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>For deep copy, the start time of the sets under the copied campaign, e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. UTC UNIX timestamp. If not set, the copied sets will inherit the start time from the original set</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>status_option</code></span></div><div class="_yb">enum {ACTIVE, PAUSED, INHERITED_FROM_SOURCE}</div></td><td><div>Default value: <code>PAUSED</code></div><p class="_yd"></p><div><div><p><code>ACTIVE</code>: the copied campaign will have active status. <code>PAUSED</code>: the copied campaign will have paused status. <code>INHERITED_FROM_SOURCE</code>: the copied campaign will have the parent status.</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>copied_campaign_id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>copied_campaign_id</code>: numeric string, </div><div class="_uoj"><code>ad_object_ids</code>:  List  [<div class="_uoj"> Struct  {<div class="_uoj"><code>ad_object_type</code>: enum {unique_adcreative, ad, ad_set, campaign, opportunities, privacy_info_center, topline, ad_account, product}, </div><div class="_uoj"><code>source_id</code>: numeric string, </div><div class="_uoj"><code>copied_id</code>: numeric string, </div>}</div>], </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can make a POST request to <code>campaigns</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-account/campaigns/"><code>/act_{ad_account_id}/campaigns</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> will be created.</div><div><h3 id="example-2">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_n_vV"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_o_9/">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_p_71">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_q_ZZ">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_r_fV">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_s_Ff">iOS SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_t_us">cURL</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fcampaigns%3Fname%3DMy%2Bcampaign%26objective%3DOUTCOME_TRAFFIC%26status%3DPAUSED%26special_ad_categories%3D%255B%255D&amp;version=v23.0" target="_blank">Graph API Explorer</a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_u_+1" style=""><code><span class="pln">POST </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/</span><span class="pln">act_</span><span class="pun">&lt;</span><span class="pln">AD_ACCOUNT_ID</span><span class="pun">&gt;</span><span class="str">/campaigns HTTP/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com

name</span><span class="pun">=</span><span class="typ">My</span><span class="pun">+</span><span class="pln">campaign</span><span class="pun">&amp;</span><span class="pln">objective</span><span class="pun">=</span><span class="pln">OUTCOME_TRAFFIC</span><span class="pun">&amp;</span><span class="pln">status</span><span class="pun">=</span><span class="pln">PAUSED</span><span class="pun">&amp;</span><span class="pln">special_ad_categories</span><span class="pun">=%</span><span class="lit">5B</span><span class="pun">%</span><span class="lit">5D</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_10_Xx"><tr class="row_0"><td><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p><a href="/docs/marketing-api/reference/ad-label">Ad Labels</a> associated with this campaign</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>bid_strategy</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_11_zq"></a></div><div class="_yb">enum{LOWEST_COST_WITHOUT_CAP, LOWEST_COST_WITH_BID_CAP, COST_CAP, LOWEST_COST_WITH_MIN_ROAS}</div></td><td><p class="_yd"></p><div><div><p>Choose bid strategy for this campaign to suit your specific business goals.
        Each strategy has tradeoffs and may be available for certain <code>optimization_goal</code>s:<br>
        <code>LOWEST_COST_WITHOUT_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> without limiting your bid amount. This is the best strategy
        if you care most about cost efficiency. However with this strategy it may be harder to get
        stable average costs as you spend. This strategy is also known as <em>automatic bidding</em>.
        Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br>
        <code>LOWEST_COST_WITH_BID_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> while limiting actual bid to your specified
        amount. With a bid cap you have more control over your
        cost per actual optimization event. However if you set a limit which is too low you may
        get less ads delivery. If you select this, you must provide
        a bid cap in the <code>bid_amount</code> field for each ad set in this ad campaign.
        Note: during creation this is the default bid strategy if you don't specify.
        This strategy is also known as <em>manual maximum-cost bidding</em>.
        Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br></p>

<p><strong>Notes:</strong></p>

<ul>
<li>If you do not enable campaign budget optimization, you should set <code>bid_strategy</code> at ad set level.</li>
<li><code>TARGET_COST</code> bidding strategy has been deprecated with <a href="/docs/graph-api/changelog/version9.0">Marketing API v9</a>.</li>
</ul>
</div></div><p></p></td></tr><tr class="row_2 _5m27"><td><div class="_yc"><span><code>budget_schedule_specs</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div><p>Initial high demand periods to be created with the campaign.<br>
Provide list of <code>time_start</code>, <code>time_end</code>,<code>budget_value</code>, and <code>budget_value_type</code>.<br>For example,<br>-F 'budget_schedule_specs=[{<br>
"time_start":1699081200,<br>
"time_end":1699167600,<br>
"budget_value":100,<br>
"budget_value_type":"ABSOLUTE"<br>
}]'
<br>
See <a href="https://developers.facebook.com/docs/graph-api/reference/high-demand-period/">High Demand Period</a> for more details on each field.</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>buying_type</code></span></div><div class="_yb">string</div></td><td><div>Default value: <code>AUCTION</code></div><p class="_yd"></p><div><div><p>This field will help Facebook make optimizations to delivery, pricing, and limits. All ad sets in this campaign must match the buying type. Possible values are: <br><code>AUCTION</code> (default)<br><code>RESERVED</code> (for <a href="/docs/marketing-api/reachandfrequency">reach and frequency ads</a>).</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>campaign_optimization_type</code></span></div><div class="_yb">enum{NONE, ICO_ONLY}</div></td><td><p class="_yd"></p><div><div><p>campaign_optimization_type</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>daily_budget</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Daily budget of this campaign. All adsets under this
        campaign will share this budget. You can either set budget at the
        campaign level or at the adset level, not both.</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>execution_options</code></span></div><div class="_yb">list&lt;enum{validate_only, include_recommendations}&gt;</div></td><td><div>Default value: <code>Set</code></div><p class="_yd"></p><div><div><p>An execution setting<br> <code>validate_only</code>: when this option is specified, the API call will not perform the mutation but will run through the validation rules against values of each field. <br><code>include_recommendations</code>: this option cannot be used by itself. When this option is used, recommendations  for ad object's configuration will be included. A separate section <a href="/docs/marketing-api/reference/ad-recommendation">recommendations</a> will be included in the response, but only if recommendations for this specification exist.<br>If the call passes validation or review, response will be <code>{"success": true}</code>. If the call does not pass, an error will be returned with more details. These options can be used to improve any UI to display errors to the user much sooner, e.g. as soon as a new value is typed into any field corresponding to this ad object, rather than at the upload/save stage, or after review.</p>
</div></div><p></p></td></tr><tr class="row_7 _5m29"><td><div class="_yc"><span><code>is_skadnetwork_attribution</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>To create an iOS 14 campaign, enable SKAdNetwork attribution for this campaign.</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>is_using_l3_schedule</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>is_using_l3_schedule</p>
</div></div><p></p></td></tr><tr class="row_9 _5m29"><td><div class="_yc"><span><code>iterative_split_test_configs</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p>Array of Iterative Split Test Configs created under this campaign .</p>
</div></div><p></p></td></tr><tr class="row_10"><td><div class="_yc"><span><code>lifetime_budget</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Lifetime budget of this campaign. All adsets under
        this campaign will share this budget. You can either set budget at the
        campaign level or at the adset level, not both.</p>
</div></div><p></p></td></tr><tr class="row_11 _5m29"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Name for this campaign</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_12"><td><div class="_yc"><span><code>objective</code></span></div><div class="_yb">enum{APP_INSTALLS, BRAND_AWARENESS, CONVERSIONS, EVENT_RESPONSES, LEAD_GENERATION, LINK_CLICKS, LOCAL_AWARENESS, MESSAGES, OFFER_CLAIMS, OUTCOME_APP_PROMOTION, OUTCOME_AWARENESS, OUTCOME_ENGAGEMENT, OUTCOME_LEADS, OUTCOME_SALES, OUTCOME_TRAFFIC, PAGE_LIKES, POST_ENGAGEMENT, PRODUCT_CATALOG_SALES, REACH, STORE_VISITS, VIDEO_VIEWS}</div></td><td><p class="_yd"></p><div><div><p>Campaign's objective. If it is specified the API will validate that any ads created under the campaign match that objective. <br>Currently, with <code>BRAND_AWARENESS</code> objective, all creatives should be either only images or only videos, not mixed.
<br>
See <a href="/docs/marketing-api/reference/ad-campaign-group/#odax">Outcome Ad-Driven Experience Objective Validation</a> for more information.</p>
</div></div><p></p></td></tr><tr class="row_13 _5m29 _5m27"><td><div class="_yc"><span><code>promoted_object</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div><p>The object this campaign is promoting across all its ads. It’s required for Meta iOS 14+ app promotion (SKAdNetwork or Aggregated Event Measurement) campaign creation. Only <code>product_catalog_id</code> is used at the ad set level.</p>
</div></div><p></p></td></tr><tr class="row_14"><td><div class="_yc"><span><code>source_campaign_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Used if a campaign has been copied. The ID from the original campaign that was copied.</p>
</div></div><p></p></td></tr><tr class="row_15 _5m29"><td><div class="_yc"><span><code>special_ad_categories</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_12_Wt"></a></div><div class="_yb">array&lt;enum {NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES}&gt;</div></td><td><p class="_yd"></p><div><div><p>special_ad_categories</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_16"><td><div class="_yc"><span><code>special_ad_category_country</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_13_/q"></a></div><div class="_yb">array&lt;enum {AD, AE, AF, AG, AI, AL, AM, AN, AO, AQ, AR, AS, AT, AU, AW, AX, AZ, BA, BB, BD, BE, BF, BG, BH, BI, BJ, BL, BM, BN, BO, BQ, BR, BS, BT, BV, BW, BY, BZ, CA, CC, CD, CF, CG, CH, CI, CK, CL, CM, CN, CO, CR, CU, CV, CW, CX, CY, CZ, DE, DJ, DK, DM, DO, DZ, EC, EE, EG, EH, ER, ES, ET, FI, FJ, FK, FM, FO, FR, GA, GB, GD, GE, GF, GG, GH, GI, GL, GM, GN, GP, GQ, GR, GS, GT, GU, GW, GY, HK, HM, HN, HR, HT, HU, ID, IE, IL, IM, IN, IO, IQ, IR, IS, IT, JE, JM, JO, JP, KE, KG, KH, KI, KM, KN, KP, KR, KW, KY, KZ, LA, LB, LC, LI, LK, LR, LS, LT, LU, LV, LY, MA, MC, MD, ME, MF, MG, MH, MK, ML, MM, MN, MO, MP, MQ, MR, MS, MT, MU, MV, MW, MX, MY, MZ, NA, NC, NE, NF, NG, NI, NL, NO, NP, NR, NU, NZ, OM, PA, PE, PF, PG, PH, PK, PL, PM, PN, PR, PS, PT, PW, PY, QA, RE, RO, RS, RU, RW, SA, SB, SC, SD, SE, SG, SH, SI, SJ, SK, SL, SM, SN, SO, SR, SS, ST, SV, SX, SY, SZ, TC, TD, TF, TG, TH, TJ, TK, TL, TM, TN, TO, TR, TT, TV, TW, TZ, UA, UG, UM, US, UY, UZ, VA, VC, VE, VG, VI, VN, VU, WF, WS, XK, YE, YT, ZA, ZM, ZW}&gt;</div></td><td><p class="_yd"></p><div><div><p>special_ad_category_country</p>
</div></div><p></p></td></tr><tr class="row_17 _5m29"><td><div class="_yc"><span><code>spend_cap</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>A spend cap for the campaign, such that it will not spend more than this cap. Defined as integer value of subunit in your currency with a minimum value of $100 USD (or approximate local equivalent). Set the value to 922337203685478 to remove the spend cap. Not available for Reach and Frequency or Premium Self Serve campaigns</p>
</div></div><p></p></td></tr><tr class="row_18"><td><div class="_yc"><span><code>start_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>start_time</p>
</div></div><p></p></td></tr><tr class="row_19 _5m29"><td><div class="_yc"><span><code>status</code></span></div><div class="_yb">enum{ACTIVE, PAUSED, DELETED, ARCHIVED}</div></td><td><p class="_yd"></p><div><div><p>Only <code>ACTIVE</code> and <code>PAUSED</code> are valid during
        creation. Other statuses can be used for update. If it is set to
        <code>PAUSED</code>, its active child objects will be paused and have an effective
        status <code>CAMPAIGN_PAUSED</code>.</p>
</div></div><p></p></td></tr><tr class="row_20"><td><div class="_yc"><span><code>stop_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>stop_time</p>
</div></div><p></p></td></tr><tr class="row_21 _5m29"><td><div class="_yc"><span><code>topline_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Topline ID</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div><div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr></tbody></table></div></div></div>

<h2 id="Updating">Updating</h2><div class="_844_"><div class="_3-98">You can update a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> by making a POST request to <a href="/docs/marketing-api/reference/ad-campaign-group/"><code>/{campaign_id}</code></a>.<div><h3 id="parameters-3">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_14_yT"><tr class="row_0"><td><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p><a href="/docs/marketing-api/reference/ad-label">Ad Labels</a> associated with this campaign</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>adset_bid_amounts</code></span></div><div class="_yb">JSON object {numeric string : int64}</div></td><td><p class="_yd"></p><div><div><p>A map of child adset IDs to their respective bid amounts required in the process of toggling campaign from autobid to manual bid</p>
</div></div><p></p></td></tr><tr class="row_2 _5m27"><td><div class="_yc"><span><code>adset_budgets</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div><p>An array of maps containing all the non-deleted child adset IDs and either daily_budget or lifetime_budget, required in the process of toggling between campaign budget and adset budget</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>bid_strategy</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_15_lx"></a></div><div class="_yb">enum{LOWEST_COST_WITHOUT_CAP, LOWEST_COST_WITH_BID_CAP, COST_CAP, LOWEST_COST_WITH_MIN_ROAS}</div></td><td><p class="_yd"></p><div><div><p>Choose bid strategy for this campaign to suit your specific business goals.
                          Each strategy has tradeoffs and may be available for certain <code>optimization_goal</code>s:<br>
                          <code>LOWEST_COST_WITHOUT_CAP</code>: Designed to get the most results for your budget based on
                          your ad set <code>optimization_goal</code> without limiting your bid amount. This is the best strategy
                          if you care most about cost efficiency. However with this strategy it may be harder to get
                          stable average costs as you spend. This strategy is also known as <em>automatic bidding</em>.
                          Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br>
                          <code>LOWEST_COST_WITH_BID_CAP</code>: Designed to get the most results for your budget based on
                          your ad set <code>optimization_goal</code> while limiting actual bid to your specified
                          amount. With a bid cap you have more control over your
                          cost per actual optimization event. However if you set a limit which is too low you may
                          get less ads delivery. If you select this, you must provide
                          a bid cap in the <code>bid_amount</code> field for each ad set in this ad campaign.
                          Note: during creation this is the default bid strategy if you don't specify.
                          This strategy is also known as <em>manual maximum-cost bidding</em>.
                          Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br>
       <code>COST_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> while limiting actual average cost per optimization event to a specified amount.
        Get specified cost cap in the <code>bid_amount</code> field for each ad set in this ad campaign.
        Learn more in <a href="https://www.facebook.com/business/help/272336376749096?id=2196356200683573">Ads Help Center, About bid strategies: Cost Cap</a>.<br></p>

<p>Notes:</p>

<ul>
<li>If you do not enable campaign budget optimization, you should set <code>bid_strategy</code> at ad set level.</li>
<li><code>TARGET_COST</code> bidding strategy has been deprecated with <a href="/docs/graph-api/changelog/version9.0">Marketing API v9</a>.</li>
</ul>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>budget_rebalance_flag</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>Whether to automatically rebalance budgets daily for all the adsets under this campaign.</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>campaign_optimization_type</code></span></div><div class="_yb">enum{NONE, ICO_ONLY}</div></td><td><p class="_yd"></p><div><div><p>campaign_optimization_type</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>daily_budget</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Daily budget of this campaign. All adsets under this
                          campaign will share this budget. You can either set budget at the
                          campaign level or at the adset level, not both.</p>
</div></div><p></p></td></tr><tr class="row_7 _5m29"><td><div class="_yc"><span><code>execution_options</code></span></div><div class="_yb">list&lt;enum{validate_only, include_recommendations}&gt;</div></td><td><div>Default value: <code>Set</code></div><p class="_yd"></p><div><div><p>An execution setting<br> <code>validate_only</code>: when this option is specified, the API call will not perform the mutation but will run through the validation rules against values of each field. <br><code>include_recommendations</code>: this option cannot be used by itself. When this option is used, recommendations  for ad object's configuration will be included. A separate section <a href="/docs/marketing-api/reference/ad-recommendation">recommendations</a> will be included in the response, but only if recommendations for this specification exist.<br>If the call passes validation or review, response will be <code>{"success": true}</code>. If the call does not pass, an error will be returned with more details. These options can be used to improve any UI to display errors to the user much sooner, e.g. as soon as a new value is typed into any field corresponding to this ad object, rather than at the upload/save stage, or after review.</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>is_skadnetwork_attribution</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>Flag to indicate that the campaign will be using SKAdNetwork, which also means that it will only be targeting iOS 14.x and above</p>
</div></div><p></p></td></tr><tr class="row_9 _5m29"><td><div class="_yc"><span><code>is_using_l3_schedule</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>is_using_l3_schedule</p>
</div></div><p></p></td></tr><tr class="row_10"><td><div class="_yc"><span><code>iterative_split_test_configs</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p>Array of Iterative Split Test Configs created under this campaign .</p>
</div></div><p></p></td></tr><tr class="row_11 _5m29"><td><div class="_yc"><span><code>lifetime_budget</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Lifetime budget of this campaign. All adsets under
                          this campaign will share this budget. You can either set budget at the
                          campaign level or at the adset level, not both.</p>
</div></div><p></p></td></tr><tr class="row_12"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Name for this campaign</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_13 _5m29"><td><div class="_yc"><span><code>objective</code></span></div><div class="_yb">enum{APP_INSTALLS, BRAND_AWARENESS, CONVERSIONS, EVENT_RESPONSES, LEAD_GENERATION, LINK_CLICKS, LOCAL_AWARENESS, MESSAGES, OFFER_CLAIMS, OUTCOME_APP_PROMOTION, OUTCOME_AWARENESS, OUTCOME_ENGAGEMENT, OUTCOME_LEADS, OUTCOME_SALES, OUTCOME_TRAFFIC, PAGE_LIKES, POST_ENGAGEMENT, PRODUCT_CATALOG_SALES, REACH, STORE_VISITS, VIDEO_VIEWS}</div></td><td><p class="_yd"></p><div><div><p>Campaign's objective. If it is specified the API will validate that any ads created under the campaign match that objective. <br>Currently, with <code>BRAND_AWARENESS</code> objective, all creatives should be either only images or only videos, not mixed.</p>

<p>See the <a href="#odax">Outcome Ad-Driven Experience Objective Validation</a> section below for more information.</p>
</div></div><p></p></td></tr><tr class="row_14 _5m27"><td><div class="_yc"><span><code>promoted_object</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div><p>The object this campaign is promoting across all its ads. Only <code>product_catalog_id</code> is used at the ad set level.</p>
</div></div><p></p></td></tr><tr class="row_15 _5m29"><td><div class="_yc"><span><code>smart_promotion_type</code></span></div><div class="_yb">enum{GUIDED_CREATION, SMART_APP_PROMOTION}</div></td><td><p class="_yd"></p><div><div><p>smart_promotion_type</p>
</div></div><p></p></td></tr><tr class="row_16"><td><div class="_yc"><span><code>special_ad_categories</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_16_bG"></a></div><div class="_yb">array&lt;enum {NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES}&gt;</div></td><td><p class="_yd"></p><div><div><p>special_ad_categories</p>
</div></div><p></p></td></tr><tr class="row_17 _5m29"><td><div class="_yc"><span><code>special_ad_category</code></span></div><div class="_yb">enum{NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES}</div></td><td><p class="_yd"></p><div><div><p>special_ad_category</p>
</div></div><p></p></td></tr><tr class="row_18"><td><div class="_yc"><span><code>special_ad_category_country</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_17_MD"></a></div><div class="_yb">array&lt;enum {AD, AE, AF, AG, AI, AL, AM, AN, AO, AQ, AR, AS, AT, AU, AW, AX, AZ, BA, BB, BD, BE, BF, BG, BH, BI, BJ, BL, BM, BN, BO, BQ, BR, BS, BT, BV, BW, BY, BZ, CA, CC, CD, CF, CG, CH, CI, CK, CL, CM, CN, CO, CR, CU, CV, CW, CX, CY, CZ, DE, DJ, DK, DM, DO, DZ, EC, EE, EG, EH, ER, ES, ET, FI, FJ, FK, FM, FO, FR, GA, GB, GD, GE, GF, GG, GH, GI, GL, GM, GN, GP, GQ, GR, GS, GT, GU, GW, GY, HK, HM, HN, HR, HT, HU, ID, IE, IL, IM, IN, IO, IQ, IR, IS, IT, JE, JM, JO, JP, KE, KG, KH, KI, KM, KN, KP, KR, KW, KY, KZ, LA, LB, LC, LI, LK, LR, LS, LT, LU, LV, LY, MA, MC, MD, ME, MF, MG, MH, MK, ML, MM, MN, MO, MP, MQ, MR, MS, MT, MU, MV, MW, MX, MY, MZ, NA, NC, NE, NF, NG, NI, NL, NO, NP, NR, NU, NZ, OM, PA, PE, PF, PG, PH, PK, PL, PM, PN, PR, PS, PT, PW, PY, QA, RE, RO, RS, RU, RW, SA, SB, SC, SD, SE, SG, SH, SI, SJ, SK, SL, SM, SN, SO, SR, SS, ST, SV, SX, SY, SZ, TC, TD, TF, TG, TH, TJ, TK, TL, TM, TN, TO, TR, TT, TV, TW, TZ, UA, UG, UM, US, UY, UZ, VA, VC, VE, VG, VI, VN, VU, WF, WS, XK, YE, YT, ZA, ZM, ZW}&gt;</div></td><td><p class="_yd"></p><div><div><p>special_ad_category_country</p>
</div></div><p></p></td></tr><tr class="row_19 _5m29"><td><div class="_yc"><span><code>spend_cap</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>A spend cap for the campaign, such that it will not spend more than this cap. Defined as integer value of subunit in your currency with a minimum value of $100 USD (or approximate local equivalent). Set the value to 922337203685478 to remove the spend cap. Not available for Reach and Frequency or Premium Self Serve campaigns</p>
</div></div><p></p></td></tr><tr class="row_20"><td><div class="_yc"><span><code>start_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>start_time</p>
</div></div><p></p></td></tr><tr class="row_21 _5m29"><td><div class="_yc"><span><code>status</code></span></div><div class="_yb">enum{ACTIVE, PAUSED, DELETED, ARCHIVED}</div></td><td><p class="_yd"></p><div><div><p>Only <code>ACTIVE</code> and <code>PAUSED</code> are valid during
                          creation. Other statuses can be used for update. If it is set to
                          <code>PAUSED</code>, its active child objects will be paused and have an effective
                          status <code>CAMPAIGN_PAUSED</code>.</p>
</div></div><p></p></td></tr><tr class="row_22"><td><div class="_yc"><span><code>stop_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>stop_time</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type-2">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node to which you POSTed.</div><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3 id="error-codes-3">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr></tbody></table></div></div></div>

<h2 id="Deleting">Deleting</h2><div class="_844_"><div class="_3-98">You can delete a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> by making a DELETE request to <a href="/docs/marketing-api/reference/ad-campaign-group/"><code>/{campaign_id}</code></a>.<div><h3 id="parameters-4">Parameters</h3>This endpoint doesn't have any parameters.</div><h3 id="return-type-3">Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3 id="error-codes-4">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> from an&nbsp;<a href="/docs/marketing-api/reference/ad-account/">AdAccount</a> by making a DELETE request to <a href="/docs/marketing-api/reference/ad-account/campaigns/"><code>/act_{ad_account_id}/campaigns</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_18_rC"><tr class="row_0"><td><div class="_yc"><span><code>before_date</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>Set a before date to delete campaigns before this date</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>delete_strategy</code></span></div><div class="_yb">enum{DELETE_ANY, DELETE_OLDEST, DELETE_ARCHIVED_BEFORE}</div></td><td><p class="_yd"></p><div><div><p>Delete strategy</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_2"><td><div class="_yc"><span><code>object_count</code></span></div><div class="_yb">integer</div></td><td><p class="_yd"></p><div><div><p>Object count</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>objects_left_to_delete_count</code>: unsigned int32, </div><div class="_uoj"><code>deleted_object_ids</code>:  List  [<div class="_uoj">numeric string</div>], </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>

<span><h2 id="objective-validation">Objective Validation</h2>
<div class="_57yz _57z1 _3-8p"><div class="_57y-"><p>These older objectives are deprecated with the release of <a href="/docs/graph-api/changelog/version17.0#marketing-api">Marketing API v17.0</a>. Please refer to the <a href="#odax-mapping">Outcome-Driven Ads Experiences mapping table</a> below to find the new objectives and their corresponding destination types, optimization goals and promoted objects.</p>
</div></div><p>Your campaign objective choice can limit the settings available to you.</p>

<h3 id="optimization-goals">Optimization Goals</h3>

<p>Certain campaign objectives support only certain ad set <code>optimization_goals</code>. See <a href="/docs/marketing-api/bidding/overview#opt-goal-validation">Bidding Overview, Validation</a>.</p>

<h3 id="compatible-ad-types">Compatible Ad Types</h3>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th style="width:30%">Objective</th><th>Compatible Ad Types</th></tr></thead><tbody class="_5m37" id="u_0_19_Bw"><tr class="row_0"><td><p><code>APP_INSTALLS</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/mobile-app-ads">App Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/dynamic-creative/segment-asset-customization">Segment Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/placement-asset-customization">Placement Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/multi-language-ads">Multi-Language Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-ads">Dynamic Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/overview">Dynamic Creative</a></li>
</ul>
</td></tr><tr class="row_1 _5m29"><td><p><code>BRAND_AWARENESS</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/dynamic-creative/segment-asset-customization">Segment Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/placement-asset-customization">Placement Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/multi-language-ads">Multi-Language Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/overview">Dynamic Creative</a></li>
</ul>
</td></tr><tr class="row_2"><td><p><code>CONVERSIONS</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/guides/collection">Collection Ads</a></li>
<li><a href="/docs/marketing-api/mobile-app-ads">App Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/guides/messenger#destination">Ads that click to Messenger</a></li>
<li><a href="/docs/marketing-api/guides/offer-ads">Offer Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/segment-asset-customization">Segment Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/placement-asset-customization">Placement Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/multi-language-ads">Multi-Language Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-ads">Dynamic Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/overview">Dynamic Creative</a></li>
</ul>
</td></tr><tr class="row_3 _5m29"><td><p><code>EVENT_RESPONSES</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/event-ads">Event and Local Ads</a></li>
</ul>
</td></tr><tr class="row_4"><td><p><code>LEAD_GENERATION</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/lead-ads">Lead Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/dynamic-creative/placement-asset-customization">Placement Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/overview">Dynamic Creative</a></li>
</ul>
</td></tr><tr class="row_5 _5m29"><td><p><code>LINK_CLICKS</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/guides/collection">Collection Ads</a></li>
<li><a href="/docs/marketing-api/mobile-app-ads">App Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/guides/offer-ads">Offer Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/segment-asset-customization">Segment Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/placement-asset-customization">Placement Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/multi-language-ads">Multi-Language Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-ads">Dynamic Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/overview">Dynamic Creative</a></li>
</ul>
</td></tr><tr class="row_6"><td><p><code>MESSAGES</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/guides/messenger#destination">Messenger Ads</a></li>
</ul>
</td></tr><tr class="row_7 _5m29"><td><p><code>POST_ENGAGEMENT</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
</ul>
</td></tr><tr class="row_8"><td><p><code>PRODUCT_CATALOG_SALES</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/collection">Collection Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/dynamic-ads">Dynamic Ads</a></li>
<li><a href="/docs/marketing-api/collaborative-ads">Collaborative Ads</a></li>
</ul>
</td></tr><tr class="row_9 _5m29"><td><p><code>REACH</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/dynamic-creative/segment-asset-customization">Segment Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/placement-asset-customization">Placement Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/multi-language-ads">Multi-Language Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/overview">Dynamic Creative</a></li>
</ul>
</td></tr><tr class="row_10"><td><p><code>STORE_VISITS</code></p>
</td><td><ul>
<li>Image Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/guides/collection">Collection Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/guides/offer-ads">Offer Ads</a></li>
</ul>
</td></tr><tr class="row_11 _5m29"><td><p><code>VIDEO_VIEWS</code></p>
</td><td><ul>
<li>Video Ads</li>
<li>Carousel Ads</li>
<li><a href="/docs/marketing-api/guides/instant-experiences">Instant Experience Ads</a></li>
<li><a href="/docs/marketing-api/guides/instagramads">Instagram Ads</a> (see <a href="/docs/marketing-api/guides/instagramads/get-started#campaign">placement limitations</a>)</li>
<li><a href="/docs/marketing-api/dynamic-creative/segment-asset-customization">Segment Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/placement-asset-customization">Placement Asset Customization Ads</a></li>
<li><a href="/docs/marketing-api/multi-language-ads">Multi-Language Ads</a></li>
<li><a href="/docs/marketing-api/dynamic-creative/overview">Dynamic Creative</a></li>
</ul>
</td></tr></tbody></table></div><h3 id="objective_creative">Objectives and Creative Fields</h3>

<p>See our <a href="https://www.facebook.com/business/ads-guide/">ads guide</a> for a list of creatives supported per objective. In the API, the objective determines which <a href="/docs/reference/ads-api/adcreative">ad creatives</a> are valid.</p>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th style="width:30%">Objective</th><th>Creative Fields</th></tr></thead><tbody class="_5m37" id="u_0_1a_J0"><tr class="row_0"><td><p><code>APP_INSTALLS</code></p>
</td><td><p><code>object_story_id</code> or <code>object_story_spec</code></p>
</td></tr><tr class="row_1 _5m29"><td><p><code>CONVERSIONS</code></p>
</td><td><p><code>object_story_id</code> or <code>object_story_spec</code></p>
<br><p>Notes:</p>

<ul>
<li>If you are creating link ads not connected to a page, use the following creative fields: <code>title</code>, <code>body</code>, <code>object_url</code>, and <code>image_file</code> or <code>image_hash</code>.</li>
<li>Creative cannot include link ads pointing to an app store.</li>
</ul>
</td></tr><tr class="row_2"><td><p><code>EVENT_RESPONSES</code></p>
</td><td><p><code>object_story_id</code> or <code>object_story_spec</code></p>
</td></tr><tr class="row_3 _5m29"><td><p><code>LEAD_GENERATION</code></p>
</td><td><p><code>object_story_id</code> or <code>object_story_spec</code></p>
</td></tr><tr class="row_4"><td><p><code>LINK_CLICKS</code></p>
</td><td><p><code>object_story_id</code> or <code>object_story_spec</code></p>
<br><p>Notes:</p>

<ul>
<li>Creative cannot include link ads pointing to an app store.</li>
<li>If you select <code>LINK_CLICKS</code> as both optimization goal and billing event, you must include <code>call_to_action</code>.</li>
</ul>
</td></tr><tr class="row_5 _5m29"><td><p><code>MESSAGES</code></p>
</td><td><p><code>object_story_spec</code></p>
</td></tr><tr class="row_6"><td><p><code>PAGE_LIKES</code></p>
</td><td><p><code>object_story_id</code>, <code>object_story_spec</code>, <code>object_id</code>, and <code>body</code></p>
</td></tr><tr class="row_7 _5m29"><td><p><code>POST_ENGAGEMENT</code></p>
</td><td><p><code>object_story_id</code> or <code>object_story_spec</code></p>
<br><p>Note: Creative cannot include link ads pointing to an app store.</p>
</td></tr><tr class="row_8"><td><p><code>VIDEO_VIEWS</code></p>
</td><td><p><code>object_story_id</code> or <code>object_story_spec</code></p>
</td></tr></tbody></table></div><h3 id="objective_tracking">Objectives and Tracking Specs</h3>

<p>Tracking specs are applied by default based on the objective specified, please see the full list of defaults by objective <a href="/docs/marketing-api/tracking-specs#default">here</a>.</p>

<p>There are two important scenarios to take into account:</p>

<ul>
<li>Tracking pixels are not applied by default, and you must specify it explicitly when your objective is <code>CONVERSIONS</code>.</li>
<li>Mobile app ads will no longer track installs or app events by default. <strong>You must explicitly specify to track installs or app events for mobile app ads otherwise your ad will not track.</strong></li>
</ul>

<p>To specify to track an install or app event, set the following in your <a href="/docs/reference/ads-api/adgroup">ad</a>:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">tracking_specs</span><span class="pun">=[{</span><span class="str">'action.type'</span><span class="pun">:[</span><span class="str">'mobile_app_install'</span><span class="pun">],</span><span class="str">'application'</span><span class="pun">:[{</span><span class="pln">your_app_id</span><span class="pun">}]},{</span><span class="str">'action.type'</span><span class="pun">:[</span><span class="str">'app_custom_event'</span><span class="pun">],</span><span class="str">'application'</span><span class="pun">:[{</span><span class="pln">your_app_id</span><span class="pun">}]}]</span></pre><h3 id="promoted-object">Objective and Promoted Objects</h3>

<p>Certain objectives require the <code>promoted_object</code> to be set in ad sets. See <a href="/docs/marketing-api/reference/ad-promoted-object">Promoted Object</a> for more information.</p>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th style="width:30%">Objective</th><th>Required promoted_object Fields</th></tr></thead><tbody class="_5m37" id="u_0_1b_fG"><tr class="row_0"><td><p><code>APP_INSTALLS</code></p>
</td><td><ul>
<li><code>application_id</code> and <code>object_store_url</code></li>
<li>If <code>optimization_goal</code> is <code>OFFSITE_CONVERSIONS</code>: <code>application_id</code>, <code>object_store_url</code>, and <code>custom_event_type</code></li>
</ul>
</td></tr><tr class="row_1 _5m29"><td><p><code>CONVERSIONS</code></p>
</td><td><ul>
<li><code>pixel_id</code> (Conversion pixel ID)</li>
<li><code>pixel_id</code> (Facebook pixel ID) and <code>custom_event_type</code></li>
<li><code>pixel_id</code> (Facebook pixel ID), <code>pixel_rule</code>, and <code>custom_event_type</code></li>
<li><code>event_id</code> (Facebook event ID) and <code>custom_event_type</code></li>
<li>For mobile app events: <code>application_id</code>, <code>object_store_url</code>, and <code>custom_event_type</code></li>
<li>For offline conversions: <code>offline_conversion_data_set_id</code> (Offline dataset ID), and <code>custom_event_type</code></li>
</ul>
</td></tr><tr class="row_2"><td><p><code>LINK_CLICKS</code></p>
</td><td><p>For mobile app or Instant Experiences app engagement link clicks: <code>application_id</code> and <code>object_store_url</code>.</p>
</td></tr><tr class="row_3 _5m29"><td><p><code>PRODUCT_CATALOG_SALES</code></p>
</td><td><ul>
<li><code>product_set_id</code>, or</li>
<li><code>product_set_id</code> and <code>custom_event_type</code></li>
</ul>
</td></tr><tr class="row_4"><td><p><code>PAGE_LIKES</code></p>
</td><td><p><code>page_id</code></p>
</td></tr><tr class="row_5 _5m29"><td><p><code>OFFER_CLAIMS</code></p>
</td><td><p><code>page_id</code></p>
</td></tr></tbody></table></div><h3 id="placement">Objective and Placements</h3>

<p>Certain types of ad <a href="/docs/reference/ads-api/targeting-specs/#placement">placements</a> are valid only for specific objectives or creatives. See <a href="https://www.facebook.com/business/help/279271845888065?id=369787570424415">Business Help Center, Available ad placements for marketing objectives</a>.</p>

<p>The table below shows some placements and their compatible objectives or creatives. You can pick a combination of those compatible placements. Note that:</p>

<ul>
<li>With <code>LEAD_GENERATION</code>, <code>device_platforms: desktop</code> cannot be selected together with <code>publisher_platforms: instagram</code>.</li>
<li>If your objective is website traffic, <code>story</code> for <code>facebook_positions</code> does not support <code>destination_type: messenger</code>.</li>
<li>If your objective is website traffic, <code>story</code> for <code>messenger_positions</code> does not support <code>destination_type: messenger</code>. </li>
<li>If your objective is website traffic, <code>ig_search</code> and <code>explore_home</code> for <code>instagram_positions</code> do not support <code>destination_type: whatsapp &amp; messenger</code>.</li>
</ul>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th style="width:20%">
        Objective
      </th><th style="width:20%">
        Creative
      </th><th>
        Placement
      </th></tr></thead><tbody class="_5m37" id="u_0_1c_PH"><tr class="row_0"><td><p><code>APP_INSTALLS</code>, promoting an Instant Experiences app</p>
</td><td><p>Desktop app ads</p>
</td><td><p><code>device_platforms</code>: <code>desktop</code></p>
</td></tr><tr class="row_1 _5m29"><td><p><code>APP_INSTALLS</code>, promoting a mobile app</p>
</td><td><p>Photo or video mobile app ads</p>
</td><td><p><code>device_platforms</code>: <code>mobile</code></p>
<br><p><code>publisher_platforms</code>: <code>facebook</code>, <code>feed</code>, <code>instagram</code>, <code>audience_network</code></p>
<br><p><code>facebook_positions</code>: <code>feed</code>, <code>video_feeds</code>, <code>instant articles</code> and <code>story</code></p>
<br><p><code>audience_network_positions</code>: <code>classic</code>, <code>rewarded_video</code></p>
<br><p><code>messenger_positions</code>: <code>story</code></p>
</td></tr><tr class="row_2"><td><p><code>BRAND_AWARENESS</code></p>
</td><td><p>all</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code>, <code>instagram</code>, <code>audience_network</code>.</p>
<br><p><code>facebook_positions</code>: <code>feed</code>, <code>video_feeds</code>, <code>instream_video</code> and <code>story</code>, which is currently under limited availability</p>
<br><p><code>instagram_positions</code>: <code>stream</code></p>
<br><p><code>audience_network_positions</code>: <code>classic</code>, <code>instream_video</code></p>
</td></tr><tr class="row_3 _5m29"><td><p><code>CONVERSIONS</code></p>
</td><td><p>Photo or video link ads from a page</p>
</td><td><p>We support <code>BRAND_AWARENESS</code>, <code>APP_INSTALL</code>, <code>POST_ENGAGEMENT</code>, <code>VIDEO_VIEWS</code>, <code>REACH</code>, <code>WEBSITE_CONVERSIONS</code>, and <code>TRAFFIC</code>.
Also supported: <code>right_hand_column</code> and <code>story</code> for <code>facebook_positions</code> and <code>messenger_positions</code>: <code>messenger_home</code> and <code>story</code>.</p>
<br><p><code>facebook_positions</code>: <code>story</code> only supports the objective <code>WEBSITE_CONVERSIONS</code></p>
<br><p><code>messenger_positions</code>: <code>story</code> only supports the objective <code>WEBSITE_CONVERSIONS</code></p>
<br><p>Exception: <code>instream_video</code> is not supported for this objective.</p>
</td></tr><tr class="row_4"><td><p><code>CONVERSIONS</code></p>
</td><td><p>Link ads not connected to a page</p>
</td><td><p><code>facebook_positions</code>: <code>right_hand_column</code></p>
</td></tr><tr class="row_5 _5m29"><td><p><code>CONVERSIONS</code> (promoting mobile app)</p>
</td><td><p>Photo or video mobile app ads</p>
</td><td><p><code>device_platforms</code>: <code>mobile</code>.</p>
<br><p><code>facebook_positions</code>: <code>right_hand_column</code> and <code>story</code>. <code>story</code> as a <code>facebook_positions</code> for this objective does not support <code>destination_type</code>: <code>messenger</code>.</p>
<br><p><code>messenger_positions</code>: <code>messenger_home</code></p>
<br><p><code>story</code> as a <code>messenger_positions</code> for this objective does not support <code>destination_type: messenger</code>.</p>
</td></tr><tr class="row_6"><td><p><code>EVENT_RESPONSES</code></p>
</td><td><p>Event ads</p>
</td><td><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_7 _5m29"><td><p><code>EVENT_RESPONSES</code></p>
</td><td><p>Page post ads</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code>.</p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_8"><td><p><code>LEAD_GENERATION</code></p>
</td><td><p>Page post ads</p>
</td><td><p><code>device_platforms</code>: <code>mobile</code>, <code>desktop</code></p>
<br><p><code>publisher_platforms</code>: <code>facebook</code>, <code>instagram</code></p>
<br><p><code>facebook_positions</code>: <code>feed</code> and <code>story</code>, which is in limited availability</p>
<br><p>instagram_positions: stream</p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_9 _5m29"><td><p><code>LINK_CLICKS</code></p>
</td><td><p>Photo or video link ads from a page</p>
</td><td><p>All, including <code>right_hand_column</code> and <code>messenger_positions</code>: <code>messenger_home</code> and <code>story</code>.</p>
</td></tr><tr class="row_10"><td><p><code>LINK_CLICKS</code></p>
</td><td><p>Link ads not connected to a page</p>
</td><td><p><code>facebook_positions</code>: <code>right_hand_column</code></p>
</td></tr><tr class="row_11 _5m29"><td><p><code>LINK_CLICKS</code>, promoting an Instant Experiences app</p>
</td><td><p>Desktop app ads</p>
</td><td><p><code>device_platforms</code>: <code>desktop</code></p>
<br><p><code>facebook_positions</code>: <code>right_hand_column</code></p>
</td></tr><tr class="row_12"><td><p><code>LINK_CLICKS</code>, promoting a mobile app</p>
</td><td><p>Photo or video mobile app ads</p>
</td><td><p><code>device_platforms</code>: <code>mobile</code>, <code>facebook_positions</code>: <code>right_hand_column</code></p>
</td></tr><tr class="row_13 _5m29"><td><p><code>PAGE_LIKES</code></p>
</td><td><p>Video creatives</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code></p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_14"><td><p><code>POST_ENGAGEMENT</code></p>
</td><td><p>Page post ads with video or photo</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code>, <code>instagram</code></p>
<br><p><code>device_platforms</code>: <code>mobile</code>, <code>desktop</code></p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_15 _5m29"><td><p><code>POST_ENGAGEMENT</code></p>
</td><td><p>Page post ads with text only</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code>, <code>instagram</code></p>
<br><p><code>device_platforms</code>: <code>mobile</code>, <code>desktop</code></p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_16"><td><p><code>POST_ENGAGEMENT</code></p>
</td><td><p>New campaign</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code>, <code>instagram</code></p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_17 _5m29"><td><p><code>PRODUCT_CATALOG_SALES</code></p>
</td><td><p>dynamic ads</p>
</td><td><p>All, including <code>right_hand_column</code> for <code>facebook_positions</code>.</p>
</td></tr><tr class="row_18"><td><p><code>REACH</code></p>
</td><td><p>Reach ads</p>
</td><td><p>All except <code>right_hand_column</code> for <code>facebook_positions</code> as of 3.0.</p>
<br><p>Includes <code>messenger_positions</code>: <code>story</code> and <code>story</code> for <code>facebook_positions</code>.</p>
</td></tr><tr class="row_19 _5m29"><td><p><code>STORE_VISITS</code></p>
</td><td><p>store visit ads</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code></p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr><tr class="row_20"><td><p><code>VIDEO_VIEWS</code></p>
</td><td><p>Video ads</p>
</td><td><p><code>publisher_platforms</code>: <code>facebook</code>, <code>instagram</code>, <code>audience_network</code>.</p>
<br><p>Includes <code>story</code> for <code>facebook_positions</code> but not with the <code>optimation_goal</code> set to <code>TWO_SECOND_CONTINUOUS_VIDEO_VIEWS</code>.</p>
<br><p>As of 3.0, you cannot use <code>right_hand_column</code> for <code>facebook_positions</code></p>
</td></tr></tbody></table></div><h3 id="attribution_spec">Objective, Optimization Goal and <code>attribution_spec</code></h3>

<p>Use click-through and view-through attribution windows for <a href="/docs/marketing-api/reference/ad-campaign#Creating">ad set</a> to track conversions then use for ads delivery optimization. This is different from the attribution window you use for ads reporting. With <code>attribution_spec</code>, select a combination of click-through or view-through windows of 1 day or 7 days. The combinations you can use depend on your ad set's <code>optimization_goal</code> and campaign's <code>objective</code>.</p>

<p><strong>Recommended Default <code>attribution_spec</code></strong></p>

<p>You may not have provided <code>attribution_spec</code> when you created ads sets optimized for Value Optimization. This is an optimization available for conversions, app installs, and product catalog sales objectives. In the past, we defaulted to a 1-day click through attribution window.</p>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>
        Objective
      </th><th>
        Optimization Goal
      </th><th>
        Allowed Combination
      </th></tr></thead><tbody class="_5m37" id="u_0_1d_6/"><tr class="row_0"><td><p><code>CONVERSIONS, PRODUCT_CATALOG_SALES</code></p>
</td><td><p><code>OFFSITE_CONVERSIONS</code></p>
</td><td><p>1-day click</p>
<br><p>7-day click</p>
<br><p>1-day click and 1-day view</p>
<br><p>7-day click and 1-day view</p>
</td></tr><tr class="row_1 _5m29"><td><p><code>APP_INSTALLS, LINK_CLICKS</code></p>
</td><td><p><code>OFFSITE_CONVERSIONS</code></p>
</td><td><p>1-day click</p>
<br><p>7-day click</p>
</td></tr><tr class="row_2"><td><p><code>APP_INSTALLS</code></p>
</td><td><p><code>APP_INSTALLS</code></p>
</td><td><p>1-day click</p>
<br><p>1-day click and 1-day view</p>
</td></tr><tr class="row_3 _5m29"><td><p><code>CONVERSIONS</code></p>
</td><td><p><code>INCREMENTAL_OFFSITE_
CONVERSIONS</code></p>
</td><td><p>Null click, Null view</p>
</td></tr></tbody></table></div><p>For all other <code>optimization_goal</code> and <code>objective</code> combinations, you can only use 1-day click for <code>attribution_spec</code>.</p>

<h3 id="odax">Outcome-Driven Ads Experiences Objective Validation</h3>
<div class="_57yz _57z1 _3-8p"><div class="_57y-"><p>From v20.0 onwards, Impressions optimization goal is deprecated for the legacy Post Engagement objective and the <code>ON_POST</code> destination_type.</p>
</div></div><h4>Objective values</h4>

<p>The following are newer objectives:</p>

<ul>
<li><code>OUTCOME_APP_PROMOTION</code></li>
<li><code>OUTCOME_AWARENESS</code></li>
<li><code>OUTCOME_ENGAGEMENT</code></li>
<li><code>OUTCOME_LEADS</code></li>
<li><code>OUTCOME_SALES</code></li>
<li><code>OUTCOME_TRAFFIC</code></li>
</ul>

<p>These newer objectives will eventually replace the original objectives <code>APP_INSTALLS</code>, <code>BRAND_AWARENESS</code>, <code>CONVERSIONS</code>, <code>EVENT_RESPONSES</code>, <code>LEAD_GENERATION</code>, <code>LINK_CLICKS</code>, <code>LOCAL_AWARENESS</code>, <code>MESSAGES</code>, <code>OFFER_CLAIMS</code>, <code>PAGE_LIKES</code>, <code>POST_ENGAGEMENT</code>, <code>PRODUCT_CATALOG_SALES</code>, <code>REACH</code>, <code>STORE_VISITS</code>, <code>VIDEO_VIEWS</code>. We will continue supporting these original objectives throughout 2022.</p>

<h4>Limitations</h4>

<ul>
<li>Trying to duplicate existing objective campaigns to use the new objective values (<code>OUTCOME_APP_PROMOTION</code>, <code>OUTCOME_AWARENESS</code>, <code>OUTCOME_ENGAGEMENT</code>, <code>OUTCOME_LEADS</code>, <code>OUTCOME_SALES</code>, <code>OUTCOME_TRAFFIC</code>) may throw an error.</li>
</ul>

<h4>Example</h4>

<p><strong>Outcome-Driven Ads Experiences</strong></p>
<table class="uiGrid _51mz _57v1 _5f0n" cellspacing="0" cellpadding="0"><tbody><tr class="_51mx"><td class="_51m- vTop _57v2 _2cs2"></td><td class="_51m- vTop _57v2 _2cs2 _51mw"><pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">X POST \  
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'name="New ODAX Campaign"'</span><span class="pln"> \  
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'objective="OUTCOME_ENGAGEMENT"'</span><span class="pln"> \  
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'status="PAUSED"'</span><span class="pln"> \  
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'special_ad_categories=[]'</span><span class="pln"> \  
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=ACCESS_TOKEN \  
  https://graph.facebook.com/v11.0/
  act_AD_ACCOUNT_ID/campaigns</span></pre></td></tr></tbody></table><div style="margin-bottom:40px;"></div><p><strong>Legacy</strong></p>
<table class="uiGrid _51mz _57v1 _5f0n" cellspacing="0" cellpadding="0"><tbody><tr class="_51mx"><td class="_51m- vTop _57v2 _2cs2"></td><td class="_51m- vTop _57v2 _2cs2 _51mw"><pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">X POST \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'name="New Campaign"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'objective="APP_INSTALLS"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'status="PAUSED"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'special_ad_categories=[]'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=ACCESS_TOKEN \
  https://graph.facebook.com/v11.0/
  act_AD_ACCOUNT_ID/campaigns</span></pre></td></tr></tbody></table><div style="margin-top:40px;"></div><h4 id="odax-mapping">Objective Mapping</h4>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th style="width:18%;">Old Objective</th><th style="width:22%;">New Objective</th><th>Destination Type</th><th>Optimization Goal</th><th style="width:18%;">Promoted Object</th></tr></thead><tbody class="_5m37" id="u_0_1e_R9"><tr class="row_0"><td><code>BRAND_AWARENESS</code></td><td><code>OUTCOME_AWARENESS</code></td><td><div style="text-align:center;"> — </div></td><td><code>AD_RECALL_LIFT</code></td><td><code>page_id</code></td></tr><tr class="row_1 _5m29"><td rowspan="2" style="border-top:1px solid darkgrey;"><code>REACH</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>OUTCOME_AWARENESS</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td style="border-top:1px solid darkgrey;"><code>REACH</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_2"><td style="border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td><code>page_id</code></td></tr><tr class="row_3 _5m29"><td rowspan="12" style="border-top:1px solid darkgrey;"><code>LINK_CLICKS</code></td><td rowspan="12" style="border-top:1px solid darkgrey;"><code>OUTCOME_TRAFFIC</code></td><td rowspan="4" style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td style="border-top:1px solid darkgrey;"><code>LINK_CLICKS</code></td><td style="border-top:1px solid darkgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_4"><td style="border-left:1px solid lightgrey;"><code>LANDING_PAGE_VIEWS</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_5 _5m29"><td style="border-left:1px solid lightgrey;"><code>REACH</code></td><td><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_6"><td style="border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_7 _5m29"><td rowspan="3" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>MESSENGER</code></td><td style="border-top:1px solid darkgrey;"><code>LINK_CLICKS</code></td><td style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td></tr><tr class="row_8"><td style="border-left:1px solid lightgrey;"><code>REACH</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_9 _5m29"><td style="border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_10"><td rowspan="3" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>WHATSAPP</code></td><td style="border-top:1px solid darkgrey;"><code>LINK_CLICKS</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_11 _5m29"><td style="border-left:1px solid lightgrey;"><code>REACH</code></td><td><code>page_id</code></td></tr><tr class="row_12"><td style="border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td><code>page_id</code></td></tr><tr class="row_13 _5m29"><td rowspan="2" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>PHONE_CALL</code></td><td style="border-top:1px solid darkgrey;"><code>QUALITY_CALL</code></td><td style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td></tr><tr class="row_14"><td style="border-left:1px solid lightgrey;"><code>LINK_CLICKS</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_15 _5m29"><td rowspan="3" style="border-top:1px solid darkgrey;"><code>POST_ENGAGEMENT</code></td><td rowspan="3" style="border-top:1px solid darkgrey;"><code>OUTCOME_ENGAGEMENT</code></td><td rowspan="3" style="border-top:1px solid darkgrey;"><code>ON_POST</code></td><td style="border-top:1px solid darkgrey;"><code>POST_ENGAGEMENT</code></td><td style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td></tr><tr class="row_16"><td style="border-left:1px solid lightgrey;"><code>REACH</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_17 _5m29"><td style="border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_18"><td style="border-top:1px solid darkgrey;"><code>PAGE_LIKES</code></td><td style="border-top:1px solid darkgrey;"><code>OUTCOME_ENGAGEMENT</code></td><td style="border-top:1px solid darkgrey;"><code>ON_PAGE</code></td><td style="border-top:1px solid darkgrey;"><code>PAGE_LIKES</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_19 _5m29"><td rowspan="4" style="border-top:1px solid darkgrey;"><code>EVENT_RESPONSES</code></td><td rowspan="4" style="border-top:1px solid darkgrey;"><code>OUTCOME_ENGAGEMENT</code></td><td rowspan="4" style="border-top:1px solid darkgrey;"><code>ON_EVENT</code></td><td style="border-top:1px solid darkgrey;"><code>EVENT_RESPONSES</code></td><td style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td></tr><tr class="row_20"><td style="border-left:1px solid lightgrey;"><code>POST_ENGAGEMENT</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_21 _5m29"><td style="border-left:1px solid lightgrey;"><code>REACH</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_22"><td style="border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_23 _5m29"><td rowspan="3" style="border-top:1px solid darkgrey;"><code>APP_INSTALL</code></td><td rowspan="3" style="border-top:1px solid darkgrey;"><code>OUTCOME_APP_PROMOTION</code></td><td rowspan="3" style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td style="border-top:1px solid darkgrey;"><code>LINK_CLICKS</code></td><td style="border-top:1px solid darkgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_24"><td style="border-left:1px solid lightgrey;"><code>OFFSITE_CONVERSIONS</code></td><td><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_25 _5m29"><td style="border-left:1px solid lightgrey;"><code>APP_INSTALLS</code></td><td><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_26"><td rowspan="4" style="border-top:1px solid darkgrey;"><code>VIDEO_VIEWS</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>OUTCOME_AWARENESS</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td style="border-top:1px solid darkgrey;"><code>THRUPLAY</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_27 _5m29"><td style="border-left:1px solid lightgrey;"><code>TWO_SECOND_CONTINUOUS_VIDEO_VIEWS</code></td><td><code>page_id</code></td></tr><tr class="row_28"><td rowspan="2" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>OUTCOME_ENGAGEMENT</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>ON_VIDEO</code></td><td style="border-top:1px solid darkgrey;"><code>THRUPLAY</code></td><td style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td></tr><tr class="row_29 _5m29"><td style="border-left:1px solid lightgrey;"><code>TWO_SECOND_CONTINUOUS_VIDEO_VIEWS</code></td><td><div style="text-align:center;"> — </div></td></tr><tr class="row_30"><td rowspan="5" style="border-top:1px solid darkgrey;"><code>LEAD_GENERATION</code></td><td rowspan="5" style="border-top:1px solid darkgrey;"><code>OUTCOME_LEADS</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>ON_AD</code></td><td style="border-top:1px solid darkgrey;"><code>LEAD_GENERATION</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_31 _5m29"><td style="border-left:1px solid lightgrey;"><code>QUALITY_LEAD</code></td><td><code>page_id</code></td></tr><tr class="row_32"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>LEAD_FROM_MESSENGER</code></td><td style="border-top:1px solid darkgrey;"><code>LEAD_GENERATION</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_33 _5m29"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>LEAD_FROM_IG_DIRECT</code></td><td style="border-top:1px solid darkgrey;"><code>LEAD_GENERATION</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_34"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>PHONE_CALL</code></td><td style="border-top:1px solid darkgrey;"><code>QUALITY_CALL</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_35 _5m29"><td rowspan="3" style="border-top:1px solid darkgrey;"><code>MESSAGES</code></td><td rowspan="3" style="border-top:1px solid darkgrey;"><code>OUTCOME_ENGAGEMENT</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>MESSENGER</code></td><td style="border-top:1px solid darkgrey;"><code>CONVERSATIONS</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_36"><td style="border-left:1px solid lightgrey;"><code>LINK_CLICKS</code></td><td><code>page_id</code></td></tr><tr class="row_37 _5m29"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>MESSENGER</code></td><td style="border-top:1px solid darkgrey;"><code>LEAD_GENERATION</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_38"><td rowspan="20" style="border-top:1px solid darkgrey;"><code>CONVERSIONS</code><br>
          (See <a href="https://www.facebook.com/business/help/2035196646663270">Available conversion locations and events by objective in Meta Ads Manager</a> for more information on available conversion events by objective.)
        </td><td rowspan="8" style="border-top:1px solid darkgrey;"><code>OUTCOME_ENGAGEMENT</code></td><td rowspan="8" style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>OFFSITE_CONVERSIONS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_39 _5m29"><td style="border-left:1px solid lightgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_40"><td rowspan="2" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>LINK_CLICKS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_41 _5m29"><td style="border-left:1px solid lightgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_42"><td rowspan="2" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>REACH</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_43 _5m29"><td style="border-left:1px solid lightgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_44"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>LANDING_PAGE_VIEWS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_45 _5m29"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_46"><td rowspan="8" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>OUTCOME_LEADS</code></td><td rowspan="8" style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>OFFSITE_CONVERSIONS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_47 _5m29"><td style="border-left:1px solid lightgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_48"><td rowspan="2" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>LINK_CLICKS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_49 _5m29"><td style="border-left:1px solid lightgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_50"><td rowspan="2" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>REACH</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_51 _5m29"><td style="border-left:1px solid lightgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_52"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>LANDING_PAGE_VIEWS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_53 _5m29"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>IMPRESSIONS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_54"><td rowspan="4" style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>OUTCOME_SALES</code></td><td rowspan="2" style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td rowspan="2" style="border-top:1px solid darkgrey;"><code>OFFSITE_CONVERSIONS</code></td><td style="border-top:1px solid darkgrey;"><code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_55 _5m29"><td style="border-left:1px solid lightgrey;"><code>application_id</code>, <code>object_store_url</code></td></tr><tr class="row_56"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>MESSENGER</code></td><td style="border-top:1px solid darkgrey;"><code>CONVERSATIONS</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code>, <code>pixel_id</code>, <code>custom_event_type</code></td></tr><tr class="row_57 _5m29"><td style="border-top:1px solid darkgrey;border-left:1px solid lightgrey;"><code>PHONE_CALL</code></td><td style="border-top:1px solid darkgrey;"><code>QUALITY_CALL</code></td><td style="border-top:1px solid darkgrey;"><code>page_id</code></td></tr><tr class="row_58"><td style="border-top:1px solid darkgrey;"><code>PRODUCT_CATALOG_SALES</code></td><td style="border-top:1px solid darkgrey;"><code>OUTCOME_SALES</code></td><td style="border-top:1px solid darkgrey;"><code>WEBSITE</code></td><td style="border-top:1px solid darkgrey;"><code>LINK_CLICKS</code></td><td style="border-top:1px solid darkgrey;">
          Campaign: <code>product_catalog_id</code><br>
          Ad set: <code>product_set_id</code>, <code>custom_event_type</code></td></tr><tr class="row_59 _5m29"><td style="border-top:1px solid darkgrey;"><code>STORE_VISITS</code></td><td style="border-top:1px solid darkgrey;"><code>OUTCOME_AWARENESS</code></td><td style="border-top:1px solid darkgrey;"><div style="text-align:center;"> — </div></td><td style="border-top:1px solid darkgrey;"><code>REACH</code></td><td style="border-top:1px solid darkgrey;"><code>place_page_set_id</code></td></tr></tbody></table></div></span>