# Facebook Marketing API - Ad Account Reach Estimate

## Summary
This endpoint provides audience size estimation based on targeting specifications for Facebook ad accounts. It returns reach estimates in the form of upper and lower bounds for the potential audience size.

## Key Points
- Returns audience size estimates as upper and lower bounds for targeting specifications
- Requires targeting_spec parameter with countries field as mandatory
- Subject to rate limiting and may not work for all custom audiences
- Only supports GET operations - no create, update, or delete functionality
- Useful for mobile app campaigns with object_store_url parameter

## API Endpoints
- `GET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate`

## Parameters
- targeting_spec
- object_store_url
- users_lower_bound
- users_upper_bound

## Content
# Ad Account Reach Estimate

## Overview

The Ad Account Reach Estimate endpoint is used to get audience size estimation based on a targeting specification using a specific ad account. This endpoint returns a range-based size estimation in the form of two fields: `users_lower_bound` and `users_upper_bound`.

## Limitations

- Reach estimates for custom audiences may not be available for certain businesses
- Rate limiting applies to this endpoint

## Endpoint

```
GET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate
```

## Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| `object_store_url` | string | Used in mobile app campaigns. The URL of the app in the app store. | No |
| `targeting_spec` | Targeting object | The targeting structure for reach estimate. `countries` is required. See [targeting specs documentation](/docs/marketing-api/targeting-specs). | Yes |

## Response Format

The response returns a JSON formatted result:

```json
{
  "data": [],
  "paging": {}
}
```

### Response Fields

- **data**: A single [AdAccountReachEstimate](/docs/graph-api/reference/ad-account-reach-estimate/) node
- **paging**: Pagination information (see [Graph API guide](/docs/graph-api/using-graph-api/#paging))

## Example Request

```http
GET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate?targeting_spec=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22age_min%22%3A20%2C%22age_max%22%3A40%7D HTTP/1.1
Host: graph.facebook.com
```

## Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 80004 | Too many calls to this ad-account. Wait and try again. |
| 613 | API calls have exceeded the rate limit |
| 200 | Permissions error |
| 2641 | Ad includes or excludes locations that are currently restricted |
| 2635 | Calling a deprecated version of the Ads API |
| 190 | Invalid OAuth 2.0 Access Token |

## Supported Operations

- **Reading**: ✅ Supported
- **Creating**: ❌ Not supported
- **Updating**: ❌ Not supported
- **Deleting**: ❌ Not supported

## Examples
GET /v23.0/act_<AD_ACCOUNT_ID>/reachestimate?targeting_spec=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22age_min%22%3A20%2C%22age_max%22%3A40%7D

---
**Tags:** Facebook Marketing API, Reach Estimation, Audience Targeting, Ad Account, Graph API, v23.0
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/reachestimate/
**Processed:** 2025-06-25T15:38:15.371Z