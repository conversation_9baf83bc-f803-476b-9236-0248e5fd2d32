{"title": "Facebook Marketing API Ad Optimization Basics", "summary": "This guide introduces the fundamental endpoints of Facebook's Marketing API for ad optimization, focusing on audience management and campaign analytics. It covers the customaudiences endpoint for creating targeted user segments and the insights endpoint for tracking campaign performance metrics.", "content": "# Ad Optimization Basics\n\nThe Marketing API offers endpoints to manage audiences and analyze advertising campaign insights. Understanding these endpoints and their functionalities is important for both new and experienced developers looking to optimize their advertising strategies.\n\n## Ad Optimization Endpoints\n\n### The `customaudiences` endpoint\n\nThe [`customaudiences` endpoint](/docs/marketing-api/reference/ad-account/customaudiences) allows you to create and manage custom and lookalike audiences, tailoring ads to specific user segments based on demographics, interests, and behaviors.\n\n**Example API Request**\n\n```bash\ncurl -X POST \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences \\\n  -F 'name=My Custom Audience' \\\n  -F 'subtype=CUSTOM' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```\n\n### The `insights` endpoint\n\nThe [`insights` endpoint](/docs/marketing-api/reference/ad-account/insights) provides valuable analytics about the performance of campaigns, ad sets, and ads, allowing you to track key metrics such as impressions, clicks, and conversions.\n\n**Example API Request**\n\n```bash\ncurl -X GET \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/insights \\\n  -F 'fields=impressions,clicks,spend' \\\n  -F 'time_range={\"since\":\"2023-01-01\",\"until\":\"2023-12-31\"}' \\\n  -F 'access_token=<ACCESS_TOKEN>'\n```", "keyPoints": ["Marketing API provides endpoints for audience management and campaign analytics", "customaudiences endpoint enables creation of targeted user segments based on demographics, interests, and behaviors", "insights endpoint offers performance analytics for campaigns, ad sets, and ads", "Key metrics available include impressions, clicks, spend, and conversions", "Both endpoints require proper authentication with access tokens"], "apiEndpoints": ["/act_<AD_ACCOUNT_ID>/customaudiences", "/act_<AD_ACCOUNT_ID>/insights"], "parameters": ["name", "subtype", "access_token", "fields", "time_range", "impressions", "clicks", "spend"], "examples": ["POST request to create custom audience with name and subtype parameters", "GET request to retrieve insights with specific fields and time range filters"], "tags": ["facebook-marketing-api", "ad-optimization", "custom-audiences", "campaign-insights", "analytics", "audience-targeting"], "relatedTopics": ["Custom Audiences", "Lookalike Audiences", "Campaign Analytics", "Ad Performance Metrics", "Monitoring and Analytics", "User Segmentation"], "difficulty": "beginner", "contentType": "guide", "originalUrl": "https://developers.facebook.com/docs/marketing-api/get-started/ad-optimization-basics", "processedAt": "2025-06-25T15:49:43.687Z", "processor": "openrouter-claude-sonnet-4"}