<div class="_1dyy" id="u_0_6_Au"><div class="_33zv _3e1u"><div class="_33zw" tabindex="0" role="button">On This Page<div><i class="img sp_zD_MvjcHflF sx_a3c10c" alt="" data-visualcompletion="css-img"></i><i class="hidden_elem img sp_zD_MvjcHflF sx_6874ff" alt="" data-visualcompletion="css-img"></i></div></div><div class="_5-24 hidden_elem"><a href="#insights-api">Insights API</a></div><div class="_5-24 hidden_elem"><a href="#before-you-begin">Before you begin</a></div><div class="_5-24 hidden_elem"><a href="#campaign-statistics">Campaign Statistics</a></div><div class="_5-24 hidden_elem"><a href="#makingacall">Making Calls</a></div><div class="_5-24 hidden_elem"><a href="#request">Request</a></div><div class="_5-24 hidden_elem"><a href="#response">Response</a></div><div class="_5-24 hidden_elem"><a href="#levels">Levels</a></div><div class="_5-24 hidden_elem"><a href="#request-2">Request</a></div><div class="_5-24 hidden_elem"><a href="#response-2">Response</a></div><div class="_5-24 hidden_elem"><a href="#attribution-windows">Attribution windows</a></div><div class="_5-24 hidden_elem"><a href="#field-expansion">Field Expansion</a></div><div class="_5-24 hidden_elem"><a href="#request-3">Request</a></div><div class="_5-24 hidden_elem"><a href="#response-3">Response</a></div><div class="_5-24 hidden_elem"><a href="#sorting">Sorting</a></div><div class="_5-24 hidden_elem"><a href="#request-4">Request</a></div><div class="_5-24 hidden_elem"><a href="#response-4">Response</a></div><div class="_5-24 hidden_elem"><a href="#ads-labels">Ads Labels</a></div><div class="_5-24 hidden_elem"><a href="#request-5">Request</a></div><div class="_5-24 hidden_elem"><a href="#response-5">Response</a></div><div class="_5-24 hidden_elem"><a href="#clicks-definition">Clicks definition</a></div><div class="_5-24 hidden_elem"><a href="#deleted-and-archived-objects">Deleted and Archived Objects</a></div><div class="_5-24 hidden_elem"><a href="#request-6">Request</a></div><div class="_5-24 hidden_elem"><a href="#response-6">Response</a></div><div class="_5-24 hidden_elem"><a href="#deleted-objects-insights">Deleted Objects Insights</a></div><div class="_5-24 hidden_elem"><a href="#troubleshooting">Troubleshooting</a></div><div class="_5-24 hidden_elem"><a href="#timeouts">Timeouts</a></div><div class="_5-24 hidden_elem"><a href="#rate-limiting">Rate Limiting</a></div><div class="_5-24 hidden_elem"><a href="#discrepancy-with-ads-manager">Discrepancy with Ads Manager</a></div><div class="_5-24 hidden_elem"><a href="#learn-more">Learn More</a></div></div></div><div id="documentation_body_pagelet" data-referrer="documentation_body_pagelet"><div class="_34yh" id="u_0_0_K1"><div class="_4cel"><span data-click-area="main"><div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8"><div class="_4-u3 _588p"><h1 id="insights-api">Insights API</h1>

<p>Provides a single, consistent interface to retrieve ad statistics.</p>
<ul>
<li><a href="/docs/marketing-api/insights/breakdowns">Breakdowns</a> - Group results</li>
<li><a href="/docs/marketing-api/insights/action-breakdowns">Action Breakdowns</a> - Understanding the response from action breakdowns.</li>
<li><a href="/docs/marketing-api/insights/async">Async Jobs</a> - For requests with large results, use asynchronous jobs</li>
<li><a href="/docs/marketing-api/insights/best-practices/">Limits and Best Practices</a> - Call limits, filtering and best practices. </li>
</ul>

<p>Before you can get data on your ad's performance, you should set up your ads to track the metrics you are interested in. For that, you can use <a href="/docs/reference/ads-api/adcreative">URL Tags</a>, <a href="/docs/marketing-api/audiences-api/pixel">Meta Pixel</a>, and the <a href="/docs/marketing-api/conversions-api">Conversions API</a>.</p>
</div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="before-you-begin">Before you begin</h2>

<p>You will need:</p>

<ul>
<li>The <code>ads_read</code> permission.</li>
<li>An <a href="https://developers.facebook.com/apps/">app</a>. See <a href="/docs/development">Meta App Development</a> for more information.</li>
</ul>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="campaign-statistics">Campaign Statistics</h2>

<p>To get the statistics of a campaign's last 7 day performance:</p>
<pre class="_5s-8 prettyprint lang-curl prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">"date_preset=last_7d"</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=ACCESS_TOKEN"</span><span class="pln"> \
  </span><span class="str">"https://graph.facebook.com/API_VERSION/AD_CAMPAIGN_ID/insights"</span></pre><p>To learn more, see the <a href="/docs/marketing-api/insights">Ad Insights Reference</a>.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="makingacall">Making Calls</h2>

<p>The Insights API is available as an edge on any ads object.</p>
<div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>
API Method
</th></tr></thead><tbody class="_5m37" id="u_0_1_rQ"><tr class="row_0"><td><p><a href="/docs/marketing-api/reference/ad-account/insights"><code>act_&lt;AD_ACCOUNT_ID&gt;/insights</code></a></p>
</td></tr><tr class="row_1 _5m29"><td><p><a href="/docs/marketing-api/reference/ad-campaign-group/insights"><code>&lt;CAMPAIGN_ID&gt;/insights</code></a></p>
</td></tr><tr class="row_2"><td><p><a href="/docs/marketing-api/reference/ad-campaign/insights"><code>&lt;ADSET_ID&gt;/insights</code></a></p>
</td></tr><tr class="row_3 _5m29"><td><p><a href="/docs/marketing-api/reference/adgroup/insights"><code>&lt;AD_ID&gt;/insights</code></a></p>
</td></tr></tbody></table></div><h3 id="request">Request</h3>

<p>You can request specific fields with a comma-separated list in the <code>fields</code> parameters. For example:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"fields=impressions"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=ACCESS_TOKEN"</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/</span><code><span class="str">v23.0</span></code><span class="str">/&lt;AD_ID&gt;/insights"</span><span class="pln">
    </span></pre><h3 id="response">Response</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pun">{</span><span class="pln">
  </span><span class="str">"data"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"impressions"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2466376"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2009-03-28"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-01"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">],</span><span class="pln">
  </span><span class="str">"paging"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="str">"cursors"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"before"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"after"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">}</span><span class="pln">
</span><span class="pun">}</span></pre><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="levels">Levels</h2>

<p>Aggregate results at a defined object level. This automatically deduplicates data.</p>

<h3 id="request-2">Request</h3>

<p>For example, get a campaign's insights on ad level.</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"level=ad"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"fields=impressions,ad_id"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=ACCESS_TOKEN"</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/</span><code><span class="str">v23.0</span></code><span class="str">/CAMPAIGN_ID/insights"</span></pre><h3 id="response-2">Response</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pun">{</span><span class="pln">
  </span><span class="str">"data"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"impressions"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"9708"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"ad_id"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"6142546123068"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2009-03-28"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-01"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"impressions"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"18841"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"ad_id"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"6142546117828"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2009-03-28"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-01"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">],</span><span class="pln">
  </span><span class="str">"paging"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="str">"cursors"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"before"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"after"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MQZDZD"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">}</span><span class="pln">
</span><span class="pun">}</span></pre><p>If you don't have access to all ad objects at the requested level, the insights call returns no data. For example, while requesting insights with <code>level</code> set to <code>ad</code>, if you don't have access to one or more ad objects under the ad account, this API call will return a permission error.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="attribution-windows">Attribution windows</h2>

<p>The <strong>conversion attribution window</strong> provides timeframes that define when we attribute an event to an ad on a Meta app. For background information, see <a href="https://www.facebook.com/business/help/****************">Meta Business Help Center, About attribution windows</a>. We measure the actions that occur when a conversion event occurs and look back in time 1-day and 7-days. To view actions attributed to different attribution windows, make a request to <code>/{ad-account-id}/insights</code>. If you do not provide <code>action_attribution_windows</code> we use <code>7d_click</code> and provide it under <code>value</code>.</p>

<p>For example specify <code>action_attribution_windows</code> and 'value' is fixed at <code>7d_click</code> attribution window. Make a request to <code>act_10151816772662695/insights?action_attribution_windows=['1d_click','1d_view']</code> and get this result:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="str">"spend"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">2352.45</span><span class="pun">,</span><span class="pln">
</span><span class="str">"actions"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
</span><span class="pun">{</span><span class="pln">
</span><span class="str">"action_type"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"link_click"</span><span class="pun">,</span><span class="pln">
</span><span class="str">"value"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">6608</span><span class="pun">,</span><span class="pln">
</span><span class="str">"1d_view"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">86</span><span class="pun">,</span><span class="pln">
</span><span class="str">"1d_click"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">6510</span><span class="pln">
</span><span class="pun">},</span><span class="pln">
</span><span class="str">"cost_per_action_type"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
</span><span class="pun">{</span><span class="pln">
</span><span class="str">"action_type"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"link_click"</span><span class="pun">,</span><span class="pln">
</span><span class="str">"value"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">0.35600030266344</span><span class="pun">,</span><span class="pln">
</span><span class="str">"1d_view"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">27.354069767442</span><span class="pun">,</span><span class="pln">
</span><span class="str">"1d_click"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">0.36135944700461</span><span class="pln">
</span><span class="pun">},</span><span class="pln">

</span><span class="com">// if attribution window is _not_ specified in query. And note that the number under 'value' key is the same even if attribution window is specified.</span><span class="pln">
</span><span class="com">// act_10151816772662695/insights</span><span class="pln">
</span><span class="str">"spend"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">2352.45</span><span class="pun">,</span><span class="pln">
</span><span class="str">"actions"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
</span><span class="pun">{</span><span class="pln">
</span><span class="str">"action_type"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"link_click"</span><span class="pun">,</span><span class="pln">
</span><span class="str">"value"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">6608</span><span class="pln">
</span><span class="pun">},</span><span class="pln">
</span><span class="str">"cost_per_action_type"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
</span><span class="pun">{</span><span class="pln">
</span><span class="str">"action_type"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"link_click"</span><span class="pun">,</span><span class="pln">
</span><span class="str">"value"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">0.35600030266344</span><span class="pln">
</span><span class="pun">},</span></pre><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="field-expansion">Field Expansion</h2>

<p>Request fields at the node level and by fields specified in <a href="/docs/graph-api/using-graph-api/#field-expansion">field expansion</a>.</p>

<h3 id="request-3">Request</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"fields=insights{impressions}"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=ACCESS_TOKEN"</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/</span><code><span class="str">v23.0</span></code><span class="str">/AD_ID"</span></pre><h3 id="response-3">Response</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pun">{</span><span class="pln">
  </span><span class="str">"id"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"6042542123268"</span><span class="pun">,</span><span class="pln">
  </span><span class="str">"name"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"My Website Clicks Ad"</span><span class="pun">,</span><span class="pln">
  </span><span class="str">"insights"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="str">"data"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
      </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"impressions"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"9708"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-03-06"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-01"</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">],</span><span class="pln">
    </span><span class="str">"paging"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"cursors"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"before"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"after"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">}</span><span class="pln">
</span><span class="pun">}</span></pre><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="sorting">Sorting</h2>

<p>Sort results by providing the <code>sort</code> parameter with <code>{fieldname}_descending</code> or <code>{fieldname}_ascending</code>:</p>

<h3 id="request-4">Request</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"sort=reach_descending"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"level=ad"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"fields=reach"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=ACCESS_TOKEN"</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/</span><code><span class="str">v23.0</span></code><span class="str">/AD_SET_ID/insights"</span></pre><h3 id="response-4">Response</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">
</span><span class="pun">{</span><span class="pln">
  </span><span class="str">"data"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"reach"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">10742</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2009-03-28"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-01"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"reach"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">5630</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2009-03-28"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-03"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"reach"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">3231</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2009-03-28"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-02"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"reach"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">936</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2009-03-29"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-04-02"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">],</span><span class="pln">
  </span><span class="str">"paging"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="str">"cursors"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"before"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"after"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MQZDZD"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">}</span><span class="pln">
</span><span class="pun">}</span></pre><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="ads-labels">Ads Labels</h2>

<p>Stats for all labels whose names are identical. Aggregated into a single value at an ad object level. See the <a href="/docs/marketing-api/reference/ad-label">Ads Labels Reference</a> for more information.</p>

<h3 id="request-5">Request</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \  
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"fields=id,name,insights{unique_clicks,cpm,total_actions}"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"level=ad"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">'filtering=[{"field":"ad.adlabels","operator":"ANY", "value":["Label Name"]}]'</span><span class="pln">  \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">'time_range={"since":"2015-03-01","until":"2015-03-31"}'</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=ACCESS_TOKEN"</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/</span><code><span class="str">v23.0</span></code><span class="str">/AD_OBJECT_ID/insights"</span></pre><h3 id="response-5">Response</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pun">{</span><span class="pln">
  </span><span class="str">"data"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"unique_clicks"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">74</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"cpm"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">0.81081081081081</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"total_actions"</span><span class="pun">:</span><span class="pln"> </span><span class="lit">49</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2015-03-01"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2015-03-31"</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
  </span><span class="pun">],</span><span class="pln"> 
  </span><span class="str">"paging"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="str">"cursors"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"before"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MA=="</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"after"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MA=="</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">}</span><span class="pln">
</span><span class="pun">}</span></pre><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="clicks-definition">Clicks definition</h2>

<p>To better understand the click metrics that Meta offers today, please read the definitions and usage of each below:</p>

<ul>
<li><p><strong>Link Clicks, <code>actions:link_click</code></strong> - The number of clicks on ad links to select destinations or experiences, on or off Meta-owned properties. See <a href="https://www.facebook.com/business/help/659185130844708">Ads Help Center, Link Clicks</a></p></li>
<li><p><strong>Clicks (All), <code>clicks</code></strong> - The metric counts multiple types of clicks on your ad, including certain types of interactions with the ad container, links to other destinations, and links to expanded ad experiences. See <a href="https://www.facebook.com/business/help/787506997938504">Ads Help Center, Clicks(All)</a></p></li>
</ul>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="deleted-and-archived-objects">Deleted and Archived Objects</h2>

<p>Ad units may be <code>DELETED</code> or <code>ARCHIVED</code>. The stats of deleted or archived objects appear when you query their parents. This means if you query <code>impressions</code> at the ad set level, results include <code>impressions</code> from all ads in the set it, regardless of whether the the ads are in a deleted or archived state. See also, <a href="/docs/marketing-api/best-practices/storing_adobjects">Storing and Retrieving Ad Objects Best Practice</a>.</p>

<p>However, if you query using filtering, status filtering will be applied by default to return only Active objects. As a result, the total stats of the parent node may be greater than the stats of its children.</p>

<p>You can get the stats of <code>ARCHIVED</code> objects from their parent nodes though, by providing an extra <code>filtering</code> parameter.</p>

<h3 id="request-6">Request</h3>

<p>To get the stats of all <code>ARCHIVED</code> ads in an ad account listed one by one:</p>
<pre class="_5s-8 prettyprint lang-curl prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">"level=ad"</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">"filtering=[{'field':'ad.effective_status','operator':'IN','value':['ARCHIVED']}]"</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=&lt;ACCESS_TOKEN&gt;"</span><span class="pln"> \
  </span><span class="str">"https://graph.facebook.com/</span><code><span class="str">v23.0</span></code><span class="str">/act_&lt;AD_ACCOUNT_ID&gt;/insights/"</span></pre><h3 id="response-6">Response</h3>

<p>Note that only archived objects are returned in this response.</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pun">{</span><span class="pln">
  </span><span class="str">"data"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"impressions"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"1741"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-03-11"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-03-12"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">],</span><span class="pln">
  </span><span class="str">"paging"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="str">"cursors"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"before"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pun">,</span><span class="pln">
      </span><span class="str">"after"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">}</span><span class="pln">
</span><span class="pun">}</span></pre><h3 id="deleted-objects-insights">Deleted Objects Insights</h3>

<p>You can query insights on deleted objects if you have their IDs or by using the <code>ad.effective_status</code> filter.</p>

<h3>Request</h3>

<p>For example, if you have the ad set ID:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"fields=id,name,status,insights{impressions}"</span><span class="pln"> \
</span><span class="pun">-</span><span class="pln">d </span><span class="str">"access_token=ACCESS_TOKEN"</span><span class="pln"> \
</span><span class="str">"https://graph.facebook.com/</span><code><span class="str">v23.0</span></code><span class="str">/AD_SET_ID"</span><span class="pln">
    </span></pre><p>In this example, we query with <code>ad.effective_status</code>:</p>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pln">POST https</span><span class="pun">:</span><span class="com">//graph.facebook.com/&lt;VERSION&gt;/act_ID/insights?access_token=token&amp;appsecret_proof=proof&amp;fields=ad_id,impressions&amp;date_preset=lifetime&amp;level=ad&amp;filtering=[{"field":"ad.effective_status","operator":"IN","value":["DELETED"]}]</span></pre><h3>Response</h3>
<pre class="_5s-8 prettyprint lang-code prettyprinted" style=""><span class="pun">{</span><span class="pln">
  </span><span class="str">"id"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"6042147342661"</span><span class="pun">,</span><span class="pln">
  </span><span class="str">"name"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"My Like Campaign"</span><span class="pun">,</span><span class="pln">
  </span><span class="str">"status"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"DELETED"</span><span class="pun">,</span><span class="pln">
  </span><span class="str">"insights"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="str">"data"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">[</span><span class="pln">
      </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"impressions"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"1741"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"date_start"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-03-11"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"date_stop"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"2016-03-12"</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">],</span><span class="pln">
    </span><span class="str">"paging"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="str">"cursors"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"before"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"after"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"MAZDZD"</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
  </span><span class="pun">}</span><span class="pln">
</span><span class="pun">}</span></pre><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="troubleshooting">Troubleshooting</h2>

<h3 id="timeouts">Timeouts</h3>

<p>The most common issues causing failure at this endpoint are too many requests and time outs:</p>

<ul>
<li>On <code>/GET</code> or synchronous requests, you can get out-of-memory or timeout errors.</li>
<li>On <code>/POST</code> or asynchronous requests, you can possibly get timeout errors.  For asynchronous requests, it can take up to an hour to complete a request including retry attempts. For example if you make a query that tries to fetch large volume of data for many ad level objects.</li>
</ul>

<h4>Recommendations</h4>

<ul>
<li>There is no explicit limit for when a query will fail.  When it times out, try to break down the query into smaller queries by putting in filters like date range.</li>
<li>Unique metrics are time consuming to compute. Try to query unique metrics in a separate call to improve performance of non-unique metrics.</li>
</ul>

<h3 id="rate-limiting">Rate Limiting</h3>

<p>The Meta Insights API utilizes rate limiting to ensure an optimal reporting experience for all of our partners. For more information and suggestions, see our Insights API <a href="/docs/marketing-api/insights/best-practices/">Limits &amp; Best Practices</a>.</p>

<h3 id="discrepancy-with-ads-manager">Discrepancy with Ads Manager</h3>
<div class="_57yz _57z1 _3-8p"><div class="_57y-"><p>Beginning June 10, 2025, to reduce discrepancies with Meta Ads Manager, <code>use_unified_attribution_setting</code> and <code>action_report_time parameters</code> will be disregarded and API responses will mimic Ads Manager settings:</p>

<ul>
<li>Attributed <code>value</code>s will be based on Ad-Set-level attribution settings (similar to <code>use_unified_attribution_setting=true</code>), and inline/on-ad actions will be included in <code>1d_click</code> or <code>1d_view</code> attribution window data. After this change, standalone <code>inline</code> attribution window data will no longer be returned.</li>
<li>Actions will be reported using <code>action_report_time=mixed</code>: on-Meta actions (like Link Clicks) will use impression-based reporting time; whereas off-Meta actions (like Web Purchases) will leverage conversion-based reporting time.</li>
</ul>
</div></div><p>The default behavior of the API is different from the default behavior in Ads Manager. If you would like to observe the same behavior as in Ads Manager, please set the field <code>use_unified_attribution_setting</code> to true.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="learn-more">Learn More</h2>

<ul>
<li><a href="/docs/marketing-api/reference/ad-account/insights">Ad Account Insights</a></li>
<li><a href="/docs/marketing-api/reference/ad-campaign-group/insights">Ad Campaign Insights</a></li>
<li><a href="/docs/marketing-api/reference/ad-campaign/insights">Ad Set Insights</a></li>
<li><a href="/docs/marketing-api/reference/adgroup/insights/">Ad Insights</a></li>
</ul>

<p>Any endpoints not in the above list are not covered in this API. If you plan to include reports from Meta in your solution, see <a href="/terms">Meta Platform Terms</a> and <a href="/devpolicy/#marketingapi">Developer Policies for Marketing API</a>.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div></div></span><div class="_4-u2 _57mb _1u44 _4-u8 _3la3"><div class="_4-u3 _588p _4_k"><fb:like href="https://developers.facebook.com/docs/marketing-api/insights/" layout="button_count" share="1"></fb:like><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div id="developer_documentation_toolbar" data-referrer="developer_documentation_toolbar" data-click-area="toolbar"></div><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '675141479195042');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '574561515946252');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '1754628768090156');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '217404712025032');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1" /></noscript></div></div></div>