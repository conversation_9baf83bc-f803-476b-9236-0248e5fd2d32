{"title": "Facebook Marketing API - Ad Account Ad Rules Library Reference", "summary": "Complete reference documentation for the Facebook Marketing API's Ad Account Ad Rules Library endpoint, covering how to read, create, and manage automated ad rules for ad accounts. This endpoint allows developers to programmatically manage rule-based automation for advertising campaigns.", "content": "# Ad Account Ad Rules Library\n\nThe Ad Account Ad Rules Library endpoint allows you to manage automated rules for ad accounts in the Facebook Marketing API.\n\n## Reading\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/adrules_library\n```\n\n### Example Request\n\n**HTTP:**\n```http\nGET /v23.0/{ad-account-id}/adrules_library HTTP/1.1\nHost: graph.facebook.com\n```\n\n**PHP SDK:**\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/adrules_library',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n**JavaScript SDK:**\n```javascript\nFB.api(\n    \"/{ad-account-id}/adrules_library\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Response Fields\nReading from this edge returns a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n- **data**: A list of AdRule nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Error Codes\n| Error | Description |\n|-------|-------------|\n| 200 | Permissions error |\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 80004 | Too many calls to this ad-account. Wait and try again |\n| 368 | Action deemed abusive or disallowed |\n\n## Creating\n\nYou can make a POST request to create new ad rules:\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/adrules_library\n```\n\n### Required Parameters\n\n**account_id** (numeric string)\n- Ad Account ID (inferred from path)\n\n**evaluation_spec** (Object)\n- Defines the evaluation spec for rule execution\n- **evaluation_type** (enum): SCHEDULE, TRIGGER\n- **filters** (list<Object>):\n  - **field** (string): Required\n  - **value** (numeric, string, boolean, list, or object): Required\n  - **operator** (enum): GREATER_THAN, LESS_THAN, EQUAL, NOT_EQUAL, IN_RANGE, NOT_IN_RANGE, IN, NOT_IN, CONTAIN, NOT_CONTAIN, ANY, ALL, NONE\n- **trigger** (Object):\n  - **type** (enum): METADATA_CREATION, METADATA_UPDATE, STATS_MILESTONE, STATS_CHANGE, DELIVERY_INSIGHTS_CHANGE\n  - **field** (string)\n  - **value** (numeric, string, boolean, list, or object)\n  - **operator** (enum): Same as filters operator\n\n**execution_spec** (Object)\n- Defines the execution spec for rule execution\n- **execution_type** (enum): DCO, PING_ENDPOINT, NOTIFICATION, PAUSE, REBALANCE_BUDGET, CHANGE_BUDGET, CHANGE_BID, ROTATE, UNPAUSE, CHANGE_CAMPAIGN_BUDGET, ADD_INTEREST_RELAXATION, ADD_QUESTIONNAIRE_INTERESTS, INCREASE_RADIUS, UPDATE_CREATIVE, UPDATE_LAX_BUDGET, UPDATE_LAX_DURATION, AUDIENCE_CONSOLIDATION, AUDIENCE_CONSOLIDATION_ASK_FIRST, AD_RECOMMENDATION_APPLY\n- **is_once_off** (boolean)\n- **execution_options** (list<Object>):\n  - **field** (string): Required\n  - **value** (numeric, string, boolean, list, or object): Required\n  - **operator** (enum): EQUAL, IN\n\n**name** (string)\n- The friendly name of the rule\n\n### Optional Parameters\n\n**schedule_spec** (Object)\n- Specifies the schedule for rule evaluation\n- **schedule_type** (enum): DAILY, HOURLY, SEMI_HOURLY, CUSTOM\n- **schedule** (list<Object>):\n  - **start_minute** (int64)\n  - **end_minute** (int64)\n  - **days** (list<int64>)\n\n**status** (enum)\n- Rule status: ENABLED, DISABLED, DELETED, HAS_ISSUES\n\n### Return Type\nReturns a struct with the created rule ID:\n```json\n{\n  \"id\": \"numeric_string\"\n}\n```\n\n### Error Codes\n| Error | Description |\n|-------|-------------|\n| 200 | Permissions error |\n| 100 | Invalid parameter |\n| 2703 | Rules that turn off ads can't have cost conditions |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 368 | Action deemed abusive or disallowed |\n\n## Updating\nYou can't perform update operations on this endpoint.\n\n## Deleting\nYou can't perform delete operations on this endpoint.", "keyPoints": ["The Ad Rules Library endpoint manages automated rules for Facebook ad accounts", "Reading returns a list of AdRule nodes with pagination support", "Creating rules requires evaluation_spec, execution_spec, and name parameters", "Rules can be triggered by schedules or specific events like metadata changes", "Update and delete operations are not supported on this endpoint"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/adrules_library", "POST /act_{ad_account_id}/adrules_library"], "parameters": ["account_id", "evaluation_spec", "evaluation_type", "filters", "execution_spec", "execution_type", "name", "schedule_spec", "status", "trigger"], "examples": ["HTTP GET request example", "PHP SDK implementation", "JavaScript SDK usage", "Android SDK example", "iOS SDK example"], "tags": ["Facebook Marketing API", "Ad Rules", "Automation", "Ad Account", "Graph API", "REST API"], "relatedTopics": ["AdRule object reference", "Graph API pagination", "OAuth 2.0 Access Tokens", "Rate limiting", "Ad account management", "Campaign automation"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adrules_library/", "processedAt": "2025-06-25T16:20:31.315Z", "processor": "openrouter-claude-sonnet-4"}