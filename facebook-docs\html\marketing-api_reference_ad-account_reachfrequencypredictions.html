<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_2p"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_NK"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_B5"></div></span></div></div>

<h1 id="overview"></h1><h1>Ad Account Reach and Frequency Prediction</h1>

<div class="_57yz _57z1 _3-8p"><div class="_57y-"><p>Beginning with v23.0, the <code>instagram_destination_id</code> field will return the <code>ig_user_id</code> rather than the <code>instagram_actor_id</code>. The <code>instagram_actor_id</code> is also no longer supported in the <code>destination_ids</code> parameter; update your API calls to use the <code>ig_user_id</code> instead.</p>
</div></div>

<h2 id="Reading">Reading</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Creating">Creating</h2><div class="_844_"><div class="_3-98">You can make a POST request to <code>reachfrequencypredictions</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-account/reachfrequencypredictions/"><code>/act_{ad_account_id}/reachfrequencypredictions</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/reach-frequency-prediction/">ReachFrequencyPrediction</a> will be created.</div><div><h3 id="parameters">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_5_Vo"><tr class="row_0"><td><div class="_yc"><span><code>budget</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Expected lifetime budget in cents in the currency for the ad account. Must be greater than the default budget limit.</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>campaign_group_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of the campaign which this prediction belongs to.</p>
</div></div><p></p></td></tr><tr class="row_2 _5m27"><td><div class="_yc"><span><code>day_parting_schedule</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p>Ad set schedule, representing a delivery schedule for a single day<br><br>Example:<br><code>[{"start_minute":360,"end_minute":1440,"days":[0,1,2,3,4,5,6]}]</code><br><br> The day part should be same for all week days. There needs to be at least 3 hours of delivery each day.<br></p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>deal_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of the deal which this prediction belongs to.</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>destination_id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>The ID of the Page or the ID of the app which the ad promotes.<br> <br></p>

<p>Using the correct advertiser Page or app ID makes your predictions more accurate. Reach and cost predictions for feed are specific to a given ID. They take into account other ads running from the same Page, as well as the past creative quality of ads from the Page, which impacts cost. <br> <br></p>

<p>If the ad set has <code>desktopfeed</code> or <code>mobilefeed</code> placement, specify <code>destination_id</code> or pass app or Page ID in <code>destination_ids</code> field. We recommend using  <code>destination_ids</code>.</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>destination_ids</code></span></div><div class="_yb">list&lt;numeric string or integer&gt;</div></td><td><p class="_yd"></p><div><div><p>Array of ID's of the Facebook Page or App which the ad promotes. Also include the Instagram account ID if <code>instagramstream</code> placement is used. <br> <br></p>

<p>If the <code>objective</code> is <code>MOBILE_APP_INSTALLS</code>, provide only the app ID. In this case, do not provide Instagram account ID, even with <code>instagramstream</code> placement.</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>end_time</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Same as <code>stop_time</code>.</p>
</div></div><p></p></td></tr><tr class="row_7 _5m29"><td><div class="_yc"><span><code>frequency_cap</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>If <code>interval_frequency_cap_reset_period</code> is specified, this field represents the frequency cap to be set for a custom period. For example: show ad 3 times per user every 48 hours. <br> <br></p>

<p>However when you read the values back, this represents the lifetime frequency cap for the campaign duration. A separate read-only field called <code>interval_frequency_cap</code> provides the frequency cap value originally set for the custom period. <br> <br></p>

<p>If <code>interval_frequency_cap_reset_period</code> is not specified, this field represents the lifetime frequency cap set for the campaign duration.</p>

<p>Target Frequency equivalent is <code>target_frequency</code>. You must also set <code>is_balanced_frequency</code> to <code>true</code>.</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>instream_packages</code></span></div><div class="_yb">array&lt;enum {NORMAL, PREMIUM, SPORTS, ENTERTAINMENT, BEAUTY, FOOD, SPANISH, REGULAR_ANIMALS_PETS, REGULAR_FOOD, REGULAR_GAMES, REGULAR_POLITICS, REGULAR_SPORTS, REGULAR_STYLE, REGULAR_TV_MOVIES}&gt;</div></td><td><p class="_yd"></p><div><div><p>Instream package of the campaign. Reserve buying campaigns and self-serve contextual package campaigns need to set the targeting packages here. Those campaigns will only deliver to pages included in the targeting packages</p>
</div></div><p></p></td></tr><tr class="row_9 _5m29"><td><div class="_yc"><span><code>interval_frequency_cap_reset_period</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Custom period to reset frequency cap. In hours. Expressed as multiples of 24. <br> <br></p>

<p>For example, to show ad no more than 3 times every 48 hours, reset period should be set to 48 (hours) and <code>frequency_cap</code> should be set to 3. Implemented using a rolling window.</p>

<p>Target Frequency equivalent is <code>target_frequency_reset_period.</code> You must also set <code>is_balanced_frequency</code> to <code>true</code>.</p>
</div></div><p></p></td></tr><tr class="row_10"><td><div class="_yc"><span><code>num_curve_points</code></span></div><div class="_yb">int64</div></td><td><div>Default value: <code>400</code></div><p class="_yd"></p><div><div><p>How many grid points to return from the curve.<br>If the value is not specified, the default value (800) is used. <br>If the value is larger than 800 then 800 will be used.</p>
</div></div><p></p></td></tr><tr class="row_11 _5m29"><td><div class="_yc"><span><code>objective</code></span></div><div class="_yb">string</div></td><td><div>Default value: <code>REACH</code></div><p class="_yd"></p><div><div><p>Objective of your reach and frequency campaign. Facebook uses this to create an optimized bid based on your objective. This does not modify you objective set at the ad campaign level. Of all possible ad objectives, you can only use these values in Facebook Reach and Frequency campaigns: <code>BRAND_AWARENESS</code>, <code>LINK_CLICKS</code>, <code>POST_ENGAGEMENT</code>, <code>MOBILE_APP_INSTALLS</code>, <code>WEBSITE_CONVERSIONS</code>, <code>REACH</code>, and <code>VIDEO_VIEWS</code>.</p>
</div></div><p></p></td></tr><tr class="row_12"><td><div class="_yc"><span><code>optimization_goal</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>optimization_goal</p>
</div></div><p></p></td></tr><tr class="row_13 _5m29"><td><div class="_yc"><span><code>prediction_mode</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Set <code>0</code> to create a prediction of budget based on expected reach. <code>reach</code> value must be provided. <br> <br>  Set <code>1</code> to create a prediction of reach based on expected budget. <code>budget</code> value must be provided.</p>
</div></div><p></p></td></tr><tr class="row_14"><td><div class="_yc"><span><code>reach</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>The desired reach of the set, must be at least the minimum reach for the target country. This number is 1,000,000, in most cases.</p>
</div></div><p></p></td></tr><tr class="row_15 _5m29"><td><div class="_yc"><span><code>rf_prediction_id_to_share</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>ID of a previously created prediction. The new prediction will also use the audience from the given prediction.</p>
</div></div><p></p></td></tr><tr class="row_16"><td><div class="_yc"><span><code>start_time</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Unix timestamp for the set start time.</p>
</div></div><p></p></td></tr><tr class="row_17 _5m29"><td><div class="_yc"><span><code>stop_time</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Unix timestamp for the set stop time. Must be no greater than 8 weeks ahead of the current time. It should end after 6AM on the last day, in the ad account's timezone.</p>
</div></div><p></p></td></tr><tr class="row_18"><td><div class="_yc"><span><code>story_event_type</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Whether or not to include mobile devices that cannot display different ad formats: <br>- Use <code>256</code>, to run canvas ads<br>- Use <code>128</code> to run video ads<br>- Use <code>0</code> if you do not include video or canvas ads<br>- Use <code>384</code> (256 + 128), to include both canvas and video.<br><br></p>

<p>You cannot create video ads if you set this flag to <code>0</code> during prediction. You can create non-video ads if the flag is set to <code>128</code>. This field is required if you target all mobile devices.<br><br>You cannot create canvas ads if this flag is set to <code>0</code> during prediction. However, you can create non-canvas ads even the flag is set to <code>256</code>.</p>
</div></div><p></p></td></tr><tr class="row_19 _5m29"><td><div class="_yc"><span><code>target_spec</code></span></div><div class="_yb">Targeting object</div></td><td><p class="_yd"></p><div><div><p><a href="/docs/marketing-api/targeting-specs">Targeting spec</a> for reach and frequency prediction. The length of JSON serialized API targeting spec should not exceed 65000  characters after internal reformatting. <br><br></p>

<p>You cannot:<br>
- Use <code>rightcolumn</code> together with any feed for placement. <br>
- Specify more than one country.<br> 
- Provide minimal iOS version for <code>user_os</code>.<br><br></p>

<p>Website Custom Audiences and <code>friends_of_connection</code> are not supported.</p>
</div></div><p></p></td></tr><tr class="row_20 _5m27"><td><div class="_yc"><span><code>trending_topics_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>Describe your Reels Trending Ads configuration.</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div>}</div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>2625</td><td>The request for a reach frequency campaign is invalid.</td></tr><tr><td>105</td><td>The number of parameters exceeded the maximum for this operation</td></tr><tr><td>2641</td><td>Your ad includes or excludes locations that are currently restricted</td></tr><tr><td>2628</td><td>There is an error in updating the state for the given prediction.</td></tr><tr><td>613</td><td>Calls to this api have exceeded the rate limit.</td></tr></tbody></table></div></div></div>

<h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Deleting">Deleting</h2><div class="_844_">You can't perform this operation on this endpoint.</div>