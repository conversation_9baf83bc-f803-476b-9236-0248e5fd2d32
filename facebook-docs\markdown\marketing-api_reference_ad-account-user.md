# Ad Account User

Graph API Version

[v23.0](#)

# Ad Account User

## Reading

Data of App Scoped User.

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=act_%7Bad-account-id%7D%3Ffields%3D%257Bfieldname_of_type_AdAccountUser%257D&version=v23.0)

```
`GET /v23.0/act_{ad-account-id}?fields={fieldname_of_type_AdAccountUser} HTTP/1.1
Host: graph.facebook.com`
```

### Parameters

This endpoint doesn't have any parameters.

### Fields

Field

Description

`id`

numeric string

ID of the App Scoped User

`name`

string

User public full name

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

`tasks`

list<string>

Tasks of App Scoped User

[Default](https://developers.facebook.com/docs/graph-api/using-graph-api/#fields)

## Creating

You can't perform this operation on this endpoint.

## Updating

You can't perform this operation on this endpoint.

## Deleting

You can't perform this operation on this endpoint.