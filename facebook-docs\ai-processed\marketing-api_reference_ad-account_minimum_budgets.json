{"title": "Facebook Marketing API - Ad Account Minimum Budgets", "summary": "This endpoint retrieves the minimum daily budget value for ad sets in Auction campaigns. It provides budget requirements based on manual bid amounts and returns MinimumBudget objects with pagination support.", "content": "# Ad Account Minimum Budgets\n\n## Overview\n\nThe Ad Account Minimum Budgets endpoint allows you to retrieve the minimum daily budget value for an ad set in an Auction campaign, given the bid_amount if using manual bid.\n\n## Reading\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/minimum_budgets\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `bid_amount` | integer | Provide this value if you want to get values for manual bid |\n\n### Response Format\n\nReading from this edge will return a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Response Fields\n\n- **`data`**: A list of MinimumBudget nodes\n- **`paging`**: Pagination information (see Graph API guide for details)\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/minimum_budgets HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/minimum_budgets',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/minimum_budgets\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n#### Android SDK\n```java\nnew GraphRequest(\n    AccessToken.getCurrentAccessToken(),\n    \"/{ad-account-id}/minimum_budgets\",\n    null,\n    HttpMethod.GET,\n    new GraphRequest.Callback() {\n        public void onCompleted(GraphResponse response) {\n            /* handle the result */\n        }\n    }\n).executeAsync();\n```\n\n#### iOS SDK\n```objc\nFBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]\n                               initWithGraphPath:@\"/{ad-account-id}/minimum_budgets\"\n                                      parameters:params\n                                      HTTPMethod:@\"GET\"];\n[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,\n                                      id result,\n                                      NSError *error) {\n    // Handle the result\n}];\n```\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n\n## Limitations\n\n- **Creating**: You can't perform this operation on this endpoint\n- **Updating**: You can't perform this operation on this endpoint  \n- **Deleting**: You can't perform this operation on this endpoint\n\nThis is a read-only endpoint that only supports GET operations.", "keyPoints": ["Retrieves minimum daily budget values for ad sets in Auction campaigns", "Supports manual bid amount parameter for customized budget calculations", "Returns paginated list of MinimumBudget objects", "Read-only endpoint - no create, update, or delete operations supported", "Available in Graph API v23.0 with multiple SDK implementations"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/minimum_budgets"], "parameters": ["bid_amount (integer) - Manual bid value for budget calculation", "ad-account-id (path parameter) - Target ad account identifier"], "examples": ["HTTP GET request to minimum_budgets endpoint", "PHP SDK implementation with error handling", "JavaScript SDK API call with response handling", "Android SDK GraphRequest implementation", "iOS SDK FBSDKGraphRequest implementation"], "tags": ["Facebook Marketing API", "Ad Account", "Minimum Budgets", "Auction Campaigns", "Budget Management", "Graph API"], "relatedTopics": ["MinimumBudget object reference", "Graph API pagination", "Ad set budget management", "Manual bidding strategies", "Auction campaign optimization"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/minimum_budgets/", "processedAt": "2025-06-25T16:28:34.971Z", "processor": "openrouter-claude-sonnet-4"}