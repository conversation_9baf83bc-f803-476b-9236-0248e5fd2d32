{"title": "Facebook Marketing API - Business User Reference", "summary": "Complete reference documentation for the Business User endpoint in Facebook's Marketing API. Covers reading, creating, updating, and deleting business users, including their roles, permissions, and associated business assets.", "content": "# Business User\n\n**API Version:** v23.0\n\n**Access Restrictions:** In Graph API v9.0, access to this endpoint was restricted. In Graph API v10.0, access has been restored to all apps, but apps can now only target businesses (or child businesses of those businesses) that have claimed them.\n\n## Overview\n\nRepresents a business user in Facebook Business Manager. A business user can be:\n- **Employee**: Can see all information in business settings and be assigned roles by business admins\n- **Admin**: Can control all aspects of the business including modifying or deleting the account and adding or removing people from the employee list\n\n## Reading Business Users\n\n### Endpoint\n```\nGET /v23.0/{business-user-id}\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Fields\n\n| Field | Type | Description | Default |\n|-------|------|-------------|----------|\n| `id` | numeric string | The business user's ID | ✓ |\n| `business` | Business | Business user is associated with this business | ✓ |\n| `email` | string | User's email as provided in Business Manager | |\n| `finance_permission` | string | Financial permission role (EDITOR, ANALYST, etc.) | |\n| `first_name` | string | User's first name as provided in Business Manager | |\n| `ip_permission` | string | Ads right permission role (Reviewer, etc.) | |\n| `last_name` | string | User's last name as provided in Business Manager | |\n| `name` | string | Name of user as provided in Business Manager | ✓ |\n| `pending_email` | string | Email pending verification | |\n| `role` | string | Role in Business Manager (Admin, Employee, etc.) | |\n| `title` | string | The title of the user in this business | |\n| `two_fac_status` | string | Two-factor authentication status | |\n\n### Edges\n\n| Edge | Type | Description |\n|------|------|-------------|\n| `assigned_business_asset_groups` | Edge<BusinessAssetGroup> | Business asset groups assigned to this user |\n| `assigned_pages` | Edge<Page> | Pages assigned to this user |\n| `assigned_product_catalogs` | Edge<ProductCatalog> | Product catalogs assigned to this user |\n\n## Creating Business Users\n\n### Endpoint\n```\nPOST /{business_id}/business_users\n```\n\n### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|----------|\n| `email` | string | Email of user to be added to this business | ✓ |\n| `invited_user_type` | array<enum> | FB or MWA (defaults to FB if not specified) | |\n| `role` | enum | Role of user (see role options below) | |\n\n### Role Options\n- `FINANCE_EDITOR`\n- `FINANCE_ANALYST` \n- `ADS_RIGHTS_REVIEWER`\n- `ADMIN`\n- `EMPLOYEE`\n- `DEVELOPER`\n- `PARTNER_CENTER_ADMIN`\n- `PARTNER_CENTER_ANALYST`\n- `PARTNER_CENTER_OPERATIONS`\n- `PARTNER_CENTER_MARKETING`\n- `PARTNER_CENTER_EDUCATION`\n- `MANAGE`\n- `DEFAULT`\n- `FINANCE_EDIT`\n- `FINANCE_VIEW`\n\n### Return Type\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n## Updating Business Users\n\n### Endpoint\n```\nPOST /{business_user_id}\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `email` | string | The email of the user at this business |\n| `first_name` | string | First name for this business user |\n| `last_name` | string | Last name for this business user |\n| `role` | enum | The role of the user at this business |\n| `skip_verification_email` | boolean | Whether to skip sending verification email |\n\n### Return Type\n```json\n{\n  \"success\": true\n}\n```\n\n## Deleting Business Users\n\n### Endpoint\n```\nDELETE /{business_user_id}\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Return Type\n```json\n{\n  \"success\": true\n}\n```\n\n## Common Error Codes\n\n| Error | Description |\n|-------|-------------|\n| 100 | Invalid parameter |\n| 104 | Incorrect signature |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | The action attempted has been deemed abusive or is otherwise disallowed |\n| 415 | Two factor authentication required |\n| 457 | The session has an invalid origin |\n| 613 | Calls to this api have exceeded the rate limit |\n| 3914 | Cannot remove the last admin from Business Manager |\n\n## Important Notes\n\n- This endpoint supports read-after-write functionality\n- At least one admin is required in Business Manager at all times\n- Use 'MWA' for inviting users with Meta accounts managed by their organization\n- Business persona emails require verification even when skipping verification emails", "keyPoints": ["Business users can be employees or admins with different permission levels", "API access is restricted to businesses that have claimed the requesting app", "Supports full CRUD operations (Create, Read, Update, Delete)", "At least one admin must remain in Business Manager at all times", "Supports read-after-write functionality for immediate data retrieval"], "apiEndpoints": ["GET /v23.0/{business-user-id}", "POST /{business_id}/business_users", "POST /{business_user_id}", "DELETE /{business_user_id}"], "parameters": ["email", "role", "invited_user_type", "first_name", "last_name", "skip_verification_email", "finance_permission", "ip_permission", "two_fac_status"], "examples": ["GET /v23.0/{business-user-id} HTTP/1.1", "POST /{business_id}/business_users with email and role parameters", "Return type: {\"id\": \"numeric string\"}", "Return type: {\"success\": true}"], "tags": ["Facebook Marketing API", "Business User", "Business Manager", "User Management", "Permissions", "CRUD Operations", "Graph API"], "relatedTopics": ["Business", "BusinessAssetGroup", "Page", "ProductCatalog", "Graph API", "Business Manager", "OAuth 2.0", "Two-factor authentication"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/business-user", "processedAt": "2025-06-25T15:45:15.078Z", "processor": "openrouter-claude-sonnet-4"}