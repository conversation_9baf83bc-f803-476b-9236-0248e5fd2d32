<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_eM"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_B7"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_Uu"></div></span></div></div>

<h1 id="overview">Business</h1>

<p>Represent a specific business on Facebook. Make the API call to the business ID.</p>
<div>To find the ID of a business, go to <a href="https://business.facebook.com/"><b>Business Manager</b></a> &gt; <b>Business Settings</b> &gt; <b>Business Info</b>. There, you will see information about the business, including the ID.   </div>

<h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>Represents a business on Facebook. Includes any specified properties and assets belonging to the business.</p>
</div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_II"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_k6">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_sb">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_pA">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_qZ">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_BT">iOS SDK</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=%7Bbusiness-id%7D&amp;version=v23.0" target="_blank">Graph API Explorer</a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_b_ii" style=""><code><span class="pln">GET </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/{</span><span class="pln">business</span><span class="pun">-</span><span class="pln">id</span><span class="pun">}</span><span class="pln"> HTTP</span><span class="pun">/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id="fields">Fields</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><code>id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>The business account ID.</p>
</div></div><p></p><div class="_2pic"><a href="https://developers.facebook.com/docs/graph-api/using-graph-api/#fields" target="blank"><span class="_1vet">Default</span></a></div></td></tr><tr><td><div class="_yc"><span><code>block_offline_analytics</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>Specifies whether offline analytics for business is blocked.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>collaborative_ads_managed_partner_business_info</code></span></div><div class="_yb _yc"><a href="https://developers.facebook.com/docs/graph-api/reference/managed-partner-business/">ManagedPartnerBusiness</a></div></td><td><p class="_yd"></p><div><div><p>collaborative_ads_managed_partner_business_info</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>collaborative_ads_managed_partner_eligibility</code></span></div><div class="_yb _yc"><span>BusinessManagedPartnerEligibility</span></div></td><td><p class="_yd"></p><div><div><p>collaborative_ads_managed_partner_eligibility</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>created_by</code></span></div><div class="_yb _yc"><span>BusinessUser|SystemUser</span></div></td><td><p class="_yd"></p><div><div><p>The creator of this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>created_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>The creation time of this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>extended_updated_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>The update time of the extended credits for this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>is_hidden</code></span></div><div class="_yb _yc"><span>bool</span></div></td><td><p class="_yd"></p><div><div><p>If <code>true</code>, indicates the business is hidden.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>link</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>URI for business profile page.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>name</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>The name of the business.</p>
</div></div><p></p><div class="_2pic"><a href="https://developers.facebook.com/docs/graph-api/using-graph-api/#fields" target="blank"><span class="_1vet">Default</span></a></div></td></tr><tr><td><div class="_yc"><span><code>payment_account_id</code></span></div><div class="_yb _yc"><span>numeric string</span></div></td><td><p class="_yd"></p><div><div><p>The ID for the payment account of this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>primary_page</code></span></div><div class="_yb _yc"><a href="https://developers.facebook.com/docs/graph-api/reference/page/">Page</a></div></td><td><p class="_yd"></p><div><div><p>The primary Facebook Page for this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>profile_picture_uri</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>The profile picture URI of the business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>timezone_id</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><div><p>This business's timezone.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>two_factor_type</code></span></div><div class="_yb _yc"><span>enum</span></div></td><td><p class="_yd"></p><div><div><p>The two factor type authentication used for this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>updated_by</code></span></div><div class="_yb _yc"><span>BusinessUser|SystemUser</span></div></td><td><p class="_yd"></p><div><div><p>The person's name who last updated this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>updated_time</code></span></div><div class="_yb _yc"><span>datetime</span></div></td><td><p class="_yd"></p><div><div><p>The time when this business was last updated.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>verification_status</code></span></div><div class="_yb _yc"><span>enum {expired, failed, ineligible, not_verified, pending, pending_need_more_info, pending_submission, rejected, revoked, verified}</span></div></td><td><p class="_yd"></p><div><div><p>Verification status for this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>vertical</code></span></div><div class="_yb _yc"><span>string</span></div></td><td><p class="_yd"></p><div><div><p>The vertical industry that this business associates with, or belongs to.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><code>vertical_id</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><div><p>The ID for the vertical industry.</p>
</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id="edges">Edges</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/ad_studies/"><code>ad_studies</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdStudy&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The studies this business has access to, such as any advertising lift studies or split testing studies.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/adnetworkanalytics_results/"><code>adnetworkanalytics_results</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdNetworkAnalyticsAsyncQueryResult&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Obtain the results of an asynchronous Audience Network query for this publisher entity.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/ads_reporting_mmm_reports/"><code>ads_reporting_mmm_reports</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdsReportBuilderMMMReport&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Marketing mix modeling (MMM) reports generated for this business</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/ads_reporting_mmm_schedulers/"><code>ads_reporting_mmm_schedulers</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdsReportBuilderMMMReportScheduler&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Marketing mix modeling (MMM) reports schedulers for this business</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/adspixels/"><code>adspixels</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdsPixel&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The business has access to these pixels.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/agencies/"><code>agencies</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Business&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Agencies associated with this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/an_placements/"><code>an_placements</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdPlacement&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Placements used by this Audience Network business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/business_asset_groups/"><code>business_asset_groups</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessAssetGroup&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Business asset groups owned by this business. The business can grant permissions to assets in this group.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/business_invoices/"><code>business_invoices</code></a></span></div><div class="_yb _yc"><span>Edge&lt;OmegaCustomerTrx&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The extended credit invoices of this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/business_users/"><code>business_users</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessUser&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Business users associated with this business. Includes employees and admins at the business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/client_apps/"><code>client_apps</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Application&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business has access to these client apps.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/client_offsite_signal_container_business_objects/"><code>client_offsite_signal_container_business_objects</code></a></span></div><div class="_yb _yc"><span>Edge&lt;OffsiteSignalContainerBusinessObject&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The business has access to these client offsite signal container business objects</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/client_pages/"><code>client_pages</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Page&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business has access to these client pages.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/client_pixels/"><code>client_pixels</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdsPixel&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business has access to these client pixels.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/client_product_catalogs/"><code>client_product_catalogs</code></a></span></div><div class="_yb _yc"><span>Edge&lt;ProductCatalog&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business has access to these client product catalogs.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/client_whatsapp_business_accounts/"><code>client_whatsapp_business_accounts</code></a></span></div><div class="_yb _yc"><span>Edge&lt;WhatsAppBusinessAccount&gt;</span></div></td><td><p class="_yd"></p><div><div><p>WhatsApp business accounts that were shared to this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/clients/"><code>clients</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Business&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Clients of this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/collaborative_ads_collaboration_requests/"><code>collaborative_ads_collaboration_requests</code></a></span></div><div class="_yb _yc"><span>Edge&lt;CPASCollaborationRequest&gt;</span></div></td><td><p class="_yd"></p><div><div><p>All <a href="/docs/marketing-api/collaborative-ads#collaborative-ads">Collaborative Ads</a>. collaboration requests initiated by the business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/collaborative_ads_suggested_partners/"><code>collaborative_ads_suggested_partners</code></a></span></div><div class="_yb _yc"><span>Edge&lt;CPASAdvertiserPartnershipRecommendation&gt;</span></div></td><td><p class="_yd"></p><div><div><p><a href="/docs/marketing-api/collaborative-ads#collaborative-ads">Collaborative Ads</a> suggested partners for a business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/commerce_merchant_settings/"><code>commerce_merchant_settings</code></a></span></div><div class="_yb _yc"><span>Edge&lt;CommerceMerchantSettings&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Commerce Merchant Settings belonging to this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/event_source_groups/"><code>event_source_groups</code></a></span></div><div class="_yb _yc"><span>Edge&lt;EventSourceGroup&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The business owns these event source groups. Includes various signals sources such as pixels.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/extendedcredits/"><code>extendedcredits</code></a></span></div><div class="_yb _yc"><span>Edge&lt;ExtendedCredit&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Extended credits for this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/initiated_audience_sharing_requests/"><code>initiated_audience_sharing_requests</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessAssetSharingAgreement&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The audience sharing requests initiated by this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/instagram_accounts/"><code>instagram_accounts</code></a></span></div><div class="_yb _yc"><span>Edge&lt;ShadowIGUser&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business has access to these Instagram accounts.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/managed_partner_ads_funding_source_details/"><code>managed_partner_ads_funding_source_details</code></a></span></div><div class="_yb _yc"><span>Edge&lt;FundingSourceDetailsCoupon&gt;</span></div></td><td><p class="_yd"></p><div><div><p>managed_partner_ads_funding_source_details</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/openbridge_configurations/"><code>openbridge_configurations</code></a></span></div><div class="_yb _yc"><span>Edge&lt;OpenBridgeConfiguration&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Get all the openbridge configurations associated to this business</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_apps/"><code>owned_apps</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Application&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business owns these apps.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_businesses/"><code>owned_businesses</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Business&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business aggregates and manages these client businesses.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_instagram_accounts/"><code>owned_instagram_accounts</code></a></span></div><div class="_yb _yc"><span>Edge&lt;ShadowIGUser&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business owns these Instagram accounts.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_offsite_signal_container_business_objects/"><code>owned_offsite_signal_container_business_objects</code></a></span></div><div class="_yb _yc"><span>Edge&lt;OffsiteSignalContainerBusinessObject&gt;</span></div></td><td><p class="_yd"></p><div><div><p>owned_offsite_signal_container_business_objects</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_pages/"><code>owned_pages</code></a></span></div><div class="_yb _yc"><span>Edge&lt;Page&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business owns these pages.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_pixels/"><code>owned_pixels</code></a></span></div><div class="_yb _yc"><span>Edge&lt;AdsPixel&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business owns these pixels.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_product_catalogs/"><code>owned_product_catalogs</code></a></span></div><div class="_yb _yc"><span>Edge&lt;ProductCatalog&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business owns these product catalogs.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/owned_whatsapp_business_accounts/"><code>owned_whatsapp_business_accounts</code></a></span></div><div class="_yb _yc"><span>Edge&lt;WhatsAppBusinessAccount&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business owns these WhatsApp Business Accounts.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/pending_client_ad_accounts/"><code>pending_client_ad_accounts</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessAdAccountRequest&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business requested access to these client ad accounts and is pending approval.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/pending_client_apps/"><code>pending_client_apps</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessApplicationRequest&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business requested access to these client apps and is pending approval.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/pending_client_pages/"><code>pending_client_pages</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessPageRequest&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business requested access to these client pages and is pending approval.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/pending_owned_ad_accounts/"><code>pending_owned_ad_accounts</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessAdAccountRequest&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business requested ownership of these ad accounts and is pending approval.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/pending_owned_pages/"><code>pending_owned_pages</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessPageRequest&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business requested ownership of these pages and is pending approval.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/pending_shared_offsite_signal_container_business_objects/"><code>pending_shared_offsite_signal_container_business_objects</code></a></span></div><div class="_yb _yc"><span>Edge&lt;OffsiteSignalContainerBusinessObject&gt;</span></div></td><td><p class="_yd"></p><div><div><p>This business received sharing requests for these offsite signal container business objects and is pending for approval.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/pending_users/"><code>pending_users</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessRoleRequest&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Admin for this business invited this user to the business. Pending user approval.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/preverified_numbers/"><code>preverified_numbers</code></a></span></div><div class="_yb _yc"><span>Edge&lt;WhatsAppBusinessPreVerifiedPhoneNumber&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Edge to get list of all pre-created phone numbers for this business</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/received_audience_sharing_requests/"><code>received_audience_sharing_requests</code></a></span></div><div class="_yb _yc"><span>Edge&lt;BusinessAssetSharingAgreement&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The audience sharing requests received by this business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/reseller_guidances/"><code>reseller_guidances</code></a></span></div><div class="_yb _yc"><span>Edge&lt;ResellerGuidance&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Guidance for a China reseller business.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/self_certified_whatsapp_business_submissions/"><code>self_certified_whatsapp_business_submissions</code></a></span></div><div class="_yb _yc"><span>Edge&lt;WhatsAppBusinessPartnerClientVerificationSubmission&gt;</span></div></td><td><p class="_yd"></p><div><div><p>Business Service Providers can submit their client information for verification on WhatsApp Business Platform. This endpoint returns statuses, submitted info, and rejection reasons for the submissions.</p>
</div></div><p></p></td></tr><tr><td><div class="_yc"><span><a href="/docs/marketing-api/reference/business/system_users/"><code>system_users</code></a></span></div><div class="_yb _yc"><span>Edge&lt;SystemUser&gt;</span></div></td><td><p class="_yd"></p><div><div><p>The business's system users.</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>104</td><td>Incorrect signature</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>80008</td><td>There have been too many calls to this WhatsApp Business account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting.</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr></tbody></table></div></div></div>

<h2 id="Creating">Creating</h2><div><div class="_57yz _57z0 _3-8p"><div class="_57y-"><p>To create other Business Managers, your business needs to obtain <code>BUSINESS_MANAGEMENT</code> during the <a href="/docs/apps/review">app review process</a>. If your app is in development mode, you can surpass this requirement, but to create only two child businesses.</p>
</div></div></div><div class="_844_"><div class="_3-98">You can make a POST request to <code>china_business_onboarding_attributions</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/business/china_business_onboarding_attributions/"><code>/{business_id}/china_business_onboarding_attributions</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> will be created.</div><div><h3 id="parameters-2">Parameters</h3>This endpoint doesn't have any parameters.</div><h3 id="return-type">Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div><div class="_uoj"><code>link_with_id</code>: string, </div>}</div><h3 id="error-codes-2">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can make a POST request to <code>businesses</code> edge from the following paths: <ul><li><a href="/docs/graph-api/reference/user/businesses/"><code>/{user_id}/businesses</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> will be created.</div><div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_g_Fz"><tr class="row_0"><td><div class="_yc"><span><code>child_business_external_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>child_business_external_id</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>email</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>The business email of the business admin</p>
</div></div><p></p></td></tr><tr class="row_2"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Username</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>primary_page</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>Primary Page ID</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>sales_rep_email</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Sales Rep email address</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>survey_business_type</code></span></div><div class="_yb">enum {AGENCY, ADVERTISER, APP_DEVELOPER, PUBLISHER}</div></td><td><p class="_yd"></p><div><div><p>Business Type</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>survey_num_assets</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Number of Assets in the business</p>
</div></div><p></p></td></tr><tr class="row_7 _5m29"><td><div class="_yc"><span><code>survey_num_people</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Number of People that will work on the business</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>timezone_id</code></span></div><div class="_yb">enum {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480}</div></td><td><p class="_yd"></p><div><div><p>Timezone ID</p>
</div></div><p></p></td></tr><tr class="row_9 _5m29"><td><div class="_yc"><span><code>vertical</code></span></div><div class="_yb">enum {NOT_SET, ADVERTISING, AUTOMOTIVE, CONSUMER_PACKAGED_GOODS, ECOMMERCE, EDUCATION, ENERGY_AND_UTILITIES, ENTERTAINMENT_AND_MEDIA, FINANCIAL_SERVICES, GAMING, GOVERNMENT_AND_POLITICS, MARKETING, ORGANIZATIONS_AND_ASSOCIATIONS, PROFESSIONAL_SERVICES, RETAIL, TECHNOLOGY, TELECOM, TRAVEL, NON_PROFIT, RESTAURANT, HEALTH, LUXURY, OTHER}</div></td><td><p class="_yd"></p><div><div><p>Vertical ID</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div><div class="_uoj"><code>name</code>: string, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>3912</td><td>There was a technical issue and the changes you made to your Business Manager weren't saved. Please try again.</td></tr><tr><td>102</td><td>Session key invalid or no longer valid</td></tr><tr><td>3974</td><td>The name you chose for this Business Manager is not valid. Try a different name.</td></tr><tr><td>3918</td><td>The Facebook Page you've tried to add is already owned by another Business Manager. You can still request access to this Page, but your request will need to be approved by the Business Manager that owns it.</td></tr><tr><td>3947</td><td>You are trying to create a Business Manager with the same name as one you are already a part of. Please pick a different name.</td></tr><tr><td>3992</td><td>Your payment account is disabled.</td></tr><tr><td>3973</td><td>The name you chose for this Business Manager is not valid. Please choose another.</td></tr><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can make a POST request to <code>owned_businesses</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/business/owned_businesses/"><code>/{business_id}/owned_businesses</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> will be created.</div><div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_h_8X"><tr class="row_0"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>name</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>page_permitted_tasks</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_i_l6"></a></div><div class="_yb">array&lt;enum {MANAGE, CREATE_CONTENT, MODERATE, MESSAGING, ADVERTISE, ANALYZE, MODERATE_COMMUNITY, MANAGE_JOBS, PAGES_MESSAGING, PAGES_MESSAGING_SUBSCRIPTIONS, READ_PAGE_MAILBOXES, VIEW_MONETIZATION_INSIGHTS, MANAGE_LEADS, PROFILE_PLUS_FULL_CONTROL, PROFILE_PLUS_MANAGE, PROFILE_PLUS_FACEBOOK_ACCESS, PROFILE_PLUS_CREATE_CONTENT, PROFILE_PLUS_MODERATE, PROFILE_PLUS_MODERATE_DELEGATE_COMMUNITY, PROFILE_PLUS_MESSAGING, PROFILE_PLUS_ADVERTISE, PROFILE_PLUS_ANALYZE, PROFILE_PLUS_REVENUE, PROFILE_PLUS_MANAGE_LEADS, CASHIER_ROLE, GLOBAL_STRUCTURE_MANAGEMENT}&gt;</div></td><td><p class="_yd"></p><div><div><p>page_permitted_tasks</p>
</div></div><p></p></td></tr><tr class="row_2"><td><div class="_yc"><span><code>sales_rep_email</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>sales_rep_email</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>shared_page_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>shared_page_id</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>should_generate_name</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>should_generate_name This parameter allows the automatic creation of a Child Business Manager when set to true, using a cleaned version of the name provided in the required name parameter. If this option is used, the updated name will be returned as part of the API response.</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>survey_business_type</code></span></div><div class="_yb">enum {AGENCY, ADVERTISER, APP_DEVELOPER, PUBLISHER}</div></td><td><p class="_yd"></p><div><div><p>survey_business_type</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>survey_num_assets</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>survey_num_assets</p>
</div></div><p></p></td></tr><tr class="row_7 _5m29"><td><div class="_yc"><span><code>survey_num_people</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>survey_num_people</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>timezone_id</code></span></div><div class="_yb">enum {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480}</div></td><td><p class="_yd"></p><div><div><p>timezone_id</p>
</div></div><p></p></td></tr><tr class="row_9 _5m29"><td><div class="_yc"><span><code>vertical</code></span></div><div class="_yb">enum {NOT_SET, ADVERTISING, AUTOMOTIVE, CONSUMER_PACKAGED_GOODS, ECOMMERCE, EDUCATION, ENERGY_AND_UTILITIES, ENTERTAINMENT_AND_MEDIA, FINANCIAL_SERVICES, GAMING, GOVERNMENT_AND_POLITICS, MARKETING, ORGANIZATIONS_AND_ASSOCIATIONS, PROFESSIONAL_SERVICES, RETAIL, TECHNOLOGY, TELECOM, TRAVEL, NON_PROFIT, RESTAURANT, HEALTH, LUXURY, OTHER}</div></td><td><p class="_yd"></p><div><div><p>vertical</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div><div class="_uoj"><code>name</code>: string, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>3913</td><td>It doesn't look like you have permission to create a new Business Manager.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>

<h2 id="Updating">Updating</h2><div class="_844_"><div class="_3-98">You can update a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a POST request to <a href="/docs/marketing-api/reference/business/"><code>/{business_id}</code></a>.<div><h3 id="parameters-3">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_j_kA"><tr class="row_0"><td><div class="_yc"><span><code>entry_point</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>entry point of claiming BusinessClaimAssetEntryPoint</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Business's name</p>
</div></div><p></p></td></tr><tr class="row_2"><td><div class="_yc"><span><code>primary_page</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Primary page of this business</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>timezone_id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Timezone id of this business</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>two_factor_type</code></span></div><div class="_yb">enum{none, admin_required, all_required}</div></td><td><p class="_yd"></p><div><div><p>Two-factor type of the business</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>vertical</code></span></div><div class="_yb">enum {NOT_SET, ADVERTISING, AUTOMOTIVE, CONSUMER_PACKAGED_GOODS, ECOMMERCE, EDUCATION, ENERGY_AND_UTILITIES, ENTERTAINMENT_AND_MEDIA, FINANCIAL_SERVICES, GAMING, GOVERNMENT_AND_POLITICS, MARKETING, ORGANIZATIONS_AND_ASSOCIATIONS, PROFESSIONAL_SERVICES, RETAIL, TECHNOLOGY, TELECOM, TRAVEL, NON_PROFIT, RESTAURANT, HEALTH, LUXURY, OTHER}</div></td><td><p class="_yd"></p><div><div><p>Vertical type of the business</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type-2">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div>}</div><h3 id="error-codes-3">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>3911</td><td>You need permission to set up a new Business Manager.</td></tr><tr><td>3974</td><td>The name you chose for this Business Manager is not valid. Try a different name.</td></tr><tr><td>3918</td><td>The Facebook Page you've tried to add is already owned by another Business Manager. You can still request access to this Page, but your request will need to be approved by the Business Manager that owns it.</td></tr><tr><td>3910</td><td>You need permission to edit the details of your Business Manager. Please talk to one of your Business Manager admins about changing your role or editing the Business Manager details.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>3912</td><td>There was a technical issue and the changes you made to your Business Manager weren't saved. Please try again.</td></tr><tr><td>3947</td><td>You are trying to create a Business Manager with the same name as one you are already a part of. Please pick a different name.</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr><tr><td>3973</td><td>The name you chose for this Business Manager is not valid. Please choose another.</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can update a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a POST request to <a href="/docs/marketing-api/reference/business/managed_businesses/"><code>/{business_id}/managed_businesses</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_k_+R"><tr class="row_0"><td><div class="_yc"><span><code>child_business_external_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>child_business_external_id</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>existing_client_business_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>Existing client business id provided by the client</p>
</div></div><p></p></td></tr><tr class="row_2"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Client business name that's managed by the aggregator business</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>sales_rep_email</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Email of sales representative of the business that's managed by the aggregator business</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>survey_business_type</code></span></div><div class="_yb">enum {AGENCY, ADVERTISER, APP_DEVELOPER, PUBLISHER}</div></td><td><p class="_yd"></p><div><div><p>Business type of surveyed business that's managed by the aggregator business</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>survey_num_assets</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Number of assets surveyed of business that's managed by the aggregator business</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>survey_num_people</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Number of people surveyed of business that's managed by the aggregator business</p>
</div></div><p></p></td></tr><tr class="row_7 _5m29"><td><div class="_yc"><span><code>timezone_id</code></span></div><div class="_yb">enum {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480}</div></td><td><p class="_yd"></p><div><div><p>Timezone id of business that's managed by the aggregator business</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>vertical</code></span></div><div class="_yb">enum {NOT_SET, ADVERTISING, AUTOMOTIVE, CONSUMER_PACKAGED_GOODS, ECOMMERCE, EDUCATION, ENERGY_AND_UTILITIES, ENTERTAINMENT_AND_MEDIA, FINANCIAL_SERVICES, GAMING, GOVERNMENT_AND_POLITICS, MARKETING, ORGANIZATIONS_AND_ASSOCIATIONS, PROFESSIONAL_SERVICES, RETAIL, TECHNOLOGY, TELECOM, TRAVEL, NON_PROFIT, RESTAURANT, HEALTH, LUXURY, OTHER}</div></td><td><p class="_yd"></p><div><div><p>Business vertical of business that's managed by the aggregator business</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div><div class="_uoj"><code>name</code>: string, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>42004</td><td>You couldn't create the client business on behalf your client successfully</td></tr><tr><td>3999</td><td>Creating a Business Manager requires a valid contact email address</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>3947</td><td>You are trying to create a Business Manager with the same name as one you are already a part of. Please pick a different name.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>3974</td><td>The name you chose for this Business Manager is not valid. Try a different name.</td></tr></tbody></table></div></div></div>

<h2 id="Deleting">Deleting</h2><div class="_844_"><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a DELETE request to <a href="/docs/marketing-api/reference/business/agencies/"><code>/{business_id}/agencies</code></a>.<div><h3 id="parameters-4">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_l_1h"><tr class="row_0"><td><div class="_yc"><span><code>business</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The agency's business.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3 id="return-type-3">Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3 id="error-codes-4">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a DELETE request to <a href="/docs/marketing-api/reference/business/clients/"><code>/{business_id}/clients</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_m_xl"><tr class="row_0"><td><div class="_yc"><span><code>business</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>The client's business.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a DELETE request to <a href="/docs/marketing-api/reference/business/pages/"><code>/{business_id}/pages</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_n_bR"><tr class="row_0"><td><div class="_yc"><span><code>page_id</code></span></div><div class="_yb">Page ID</div></td><td><p class="_yd"></p><div><div><p>Page ID.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>3996</td><td>The page does not belong to this Business Manager.</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>42001</td><td>This Page can't be removed because it's already linked to an Instagram business profile. To remove this Page from Business Manager, go to Instagram and convert to a personal account or change the Page linked to your business profile.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>457</td><td>The session has an invalid origin</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a DELETE request to <a href="/docs/marketing-api/reference/business/instagram_accounts/"><code>/{business_id}/instagram_accounts</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_o_H0"><tr class="row_0"><td><div class="_yc"><span><code>instagram_account</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>Instagram account ID.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a DELETE request to <a href="/docs/marketing-api/reference/business/ad_accounts/"><code>/{business_id}/ad_accounts</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_p_n4"><tr class="row_0"><td><div class="_yc"><span><code>adaccount_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Ad account ID.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from an&nbsp;<a href="/docs/marketing-api/reference/ad-account/">AdAccount</a> by making a DELETE request to <a href="/docs/marketing-api/reference/ad-account/agencies/"><code>/act_{ad_account_id}/agencies</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_q_kK"><tr class="row_0"><td><div class="_yc"><span><code>business</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>SELF_EXPLANATORY</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from a&nbsp;<a href="/docs/graph-api/reference/user/">User</a> by making a DELETE request to <a href="/docs/graph-api/reference/user/businesses/"><code>/{user_id}/businesses</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_r_5n"><tr class="row_0"><td><div class="_yc"><span><code>business</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Business ID</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>3914</td><td>It looks like you're trying to remove the last admin from this Business Manager. At least one admin is required in Business Manager.</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr></tbody></table></div></div><div class="_4g10"></div><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> from a&nbsp;<a href="/docs/marketing-api/reference/business/">Business</a> by making a DELETE request to <a href="/docs/marketing-api/reference/business/owned_businesses/"><code>/{business_id}/owned_businesses</code></a>.<div><h3>Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_s_5/"><tr class="row_0"><td><div class="_yc"><span><code>client_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>client_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>3912</td><td>There was a technical issue and the changes you made to your Business Manager weren't saved. Please try again.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>