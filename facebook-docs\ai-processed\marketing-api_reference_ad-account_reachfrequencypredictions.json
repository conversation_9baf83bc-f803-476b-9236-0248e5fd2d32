{"title": "Facebook Marketing API - Ad Account Reach and Frequency Predictions", "summary": "This endpoint allows creating reach and frequency predictions for Facebook ad campaigns. It helps advertisers estimate reach, frequency, and budget requirements for their campaigns before launching them.", "content": "# Ad Account Reach and Frequency Predictions\n\n**Important Update (v23.0):** Beginning with v23.0, the `instagram_destination_id` field will return the `ig_user_id` rather than the `instagram_actor_id`. The `instagram_actor_id` is also no longer supported in the `destination_ids` parameter; update your API calls to use the `ig_user_id` instead.\n\n## Overview\n\nThe Reach and Frequency Predictions endpoint allows you to create predictions for reach and frequency campaigns before launching them. This helps in planning and budgeting for your advertising campaigns.\n\n## Supported Operations\n\n- **Reading**: Not supported\n- **Creating**: ✅ Supported via POST request\n- **Updating**: Not supported\n- **Deleting**: Not supported\n\n## Creating Predictions\n\nMake a POST request to the `reachfrequencypredictions` edge:\n\n```\nPOST /act_{ad_account_id}/reachfrequencypredictions\n```\n\nThis will create a [ReachFrequencyPrediction](/docs/marketing-api/reference/reach-frequency-prediction/) object.\n\n## Key Parameters\n\n### Required Parameters\n- `budget`: Expected lifetime budget in cents\n- `target_spec`: Targeting specification for the prediction\n- `start_time`: Unix timestamp for campaign start\n- `stop_time`: Unix timestamp for campaign end\n\n### Important Parameters\n- `prediction_mode`: Set to `0` for budget-based prediction (requires `reach`), or `1` for reach-based prediction (requires `budget`)\n- `objective`: Campaign objective (default: `REACH`)\n- `destination_ids`: Array of Facebook Page or App IDs\n- `frequency_cap`: Lifetime frequency cap for the campaign\n- `day_parting_schedule`: Delivery schedule configuration\n\n### Example Day Parting Schedule\n```json\n[{\"start_minute\":360,\"end_minute\":1440,\"days\":[0,1,2,3,4,5,6]}]\n```\n\n## Supported Objectives\n- `BRAND_AWARENESS`\n- `LINK_CLICKS`\n- `POST_ENGAGEMENT`\n- `MOBILE_APP_INSTALLS`\n- `WEBSITE_CONVERSIONS`\n- `REACH`\n- `VIDEO_VIEWS`\n\n## Response Format\n\nReturns a struct with:\n```json\n{\n  \"id\": \"numeric_string\"\n}\n```\n\n## Common Error Codes\n- `100`: Invalid parameter\n- `2625`: Invalid reach frequency campaign request\n- `105`: Too many parameters\n- `2641`: Restricted locations in targeting\n- `2628`: Error updating prediction state\n- `613`: Rate limit exceeded\n\n## Important Notes\n\n1. **Targeting Restrictions**: Cannot use `rightcolumn` with feed placements, specify more than one country, or use Website Custom Audiences\n2. **Minimum Reach**: Must be at least 1,000,000 in most cases\n3. **Campaign Duration**: Maximum 8 weeks ahead of current time\n4. **Day Parting**: Must have at least 3 hours of delivery each day\n5. **Targeting Spec Limit**: JSON serialized targeting spec should not exceed 65,000 characters", "keyPoints": ["Only POST (create) operations are supported - no reading, updating, or deleting", "Two prediction modes: budget-based (mode 0) or reach-based (mode 1)", "Instagram destination IDs now use ig_user_id instead of instagram_actor_id (v23.0+)", "Minimum reach requirement is typically 1,000,000 for most countries", "Campaign duration cannot exceed 8 weeks from current time"], "apiEndpoints": ["POST /act_{ad_account_id}/reachfrequencypredictions"], "parameters": ["budget", "target_spec", "prediction_mode", "objective", "destination_ids", "frequency_cap", "start_time", "stop_time", "reach", "day_parting_schedule", "campaign_group_id", "deal_id", "optimization_goal", "story_event_type", "instream_packages"], "examples": ["Day parting schedule: [{\"start_minute\":360,\"end_minute\":1440,\"days\":[0,1,2,3,4,5,6]}]", "POST /act_{ad_account_id}/reachfrequencypredictions", "Response: {\"id\": \"numeric_string\"}"], "tags": ["Facebook Marketing API", "Reach and Frequency", "Campaign Prediction", "Ad Account", "Budget Planning", "Targeting"], "relatedTopics": ["ReachFrequencyPrediction object", "Targeting specifications", "Campaign objectives", "Ad account management", "Instagram integration", "Rate limiting", "Error handling"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "processedAt": "2025-06-25T15:38:50.325Z", "processor": "openrouter-claude-sonnet-4"}