{"title": "Facebook Marketing API - Brand Safety and Suitability", "summary": "Meta's Brand Safety and Suitability APIs provide comprehensive tools for advertisers to control ad placement and ensure brand-appropriate content adjacency across Facebook, Instagram, and Meta Audience Network. The suite includes multiple APIs for blocking unsuitable content, allowing trusted content, reporting delivery metrics, and verifying feed suitability.", "content": "# Brand Safety and Suitability\n\nMeta offers several brand suitability controls to help you place ads adjacent to organic content that is more suitable for your brand on Facebook, Instagram and Meta Audience Network. You can apply one of these controls or use them in combination. Meta keeps your brand safe by enforcing [Facebook Community Standards](https://www.facebook.com/business/brand-safety/media-responsibility) and [Instagram Community Guidelines](https://www.facebook.com/help/instagram/***************) for all content and publishers. [Learn more about brand suitability](https://www.facebook.com/business/help/****************?id=****************).\n\n## Available APIs and Features\n\n### Integration Setup\nAn overview of initial setup steps required for program participation. The main elements it addresses include: setting up a business in Business Manager, creating and obtaining access to ad accounts, and creating an app to access Meta's API.\n\n### Block Lists API\nBlock lists stop your ads from appearing with publishers you don't consider suitable for your brand or campaign.\n\n### Content Allow Lists API\nContent Allow Lists give you the ability to work with trusted Meta Business Partners to review and customize lists of brand suitable videos for running Facebook in-stream campaigns.\n\n### Content Delivery Reports API\nContent delivery reports provide transparency into where ads appeared and show impressions at the content level.\n\n### Feed Verification API\nFeed verification allows you to measure, verify and understand the suitability of content near your ads to help you make informed decisions in order to reach your marketing goals.\n\n### Partner-publisher Lists API\nPartner-publisher lists show publishers that have signed up for monetization and follow our Partner Monetization Policies.\n\n### Passback API\nPassback allows Meta Business Partners to share content risk labels and campaign performance data with Meta. The goals are to provide advertisers and partners with a mechanism to give feedback on content, for Meta to be able to take action on that feedback, and for Meta and partners to be able to compare content labels.\n\n### Publisher Delivery Reports API\nPublisher delivery reports provide transparency into where ads appeared and show impressions at the publisher level.", "keyPoints": ["Meta provides multiple brand safety controls that can be used individually or in combination across Facebook, Instagram, and Meta Audience Network", "Block Lists API prevents ads from appearing with unsuitable publishers for your brand", "Content Allow Lists API enables collaboration with Meta Business Partners to curate brand-suitable video content", "Delivery Reports APIs provide transparency at both content and publisher levels for ad placement verification", "Feed Verification and Passback APIs enable measurement and feedback mechanisms for content suitability assessment"], "apiEndpoints": ["Block Lists API", "Content Allow Lists API", "Content Delivery Reports API", "Feed Verification API", "Partner-publisher Lists API", "Passback API", "Publisher Delivery Reports API"], "parameters": [], "examples": [], "tags": ["brand safety", "content suitability", "ad placement", "publisher controls", "content verification", "delivery reports", "Meta Audience Network"], "relatedTopics": ["Facebook Community Standards", "Instagram Community Guidelines", "Meta Business Partners", "Partner Monetization Policies", "Business Manager setup", "Ad account management", "In-stream campaigns"], "difficulty": "intermediate", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-api/brand-safety-and-suitability", "processedAt": "2025-06-25T15:08:11.326Z", "processor": "openrouter-claude-sonnet-4"}