# Ad Account <PERSON><PERSON><PERSON> Library

On This Page

[Ad Account Adrules Library](#overview)

[Reading](#Reading)

[Example](#example)

[Parameters](#parameters)

[Fields](#fields)

[Error Codes](#error-codes)

[Creating](#Creating)

[Parameters](#parameters-2)

[Return Type](#return-type)

[Error Codes](#error-codes-2)

[Updating](#Updating)

[Deleting](#Deleting)

Graph API Version

[v23.0](#)

# Ad Account Adrules Library

[](#)

## Reading

AdAccountAdRulesLibrary

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDK[Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fadrules_library&version=v23.0)

```
`GET /v23.0/{ad-account-id}/adrules_library HTTP/1.1
Host: graph.facebook.com`
```
```
`/* PHP SDK v5.0.0 */
/* make the API call */
try {
  // Returns a `Facebook\FacebookResponse` object
  $response = $fb->get(
    '/{ad-account-id}/adrules_library',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
/* handle the result */`
```
```
`/* make the API call */
FB.api(
    "/{ad-account-id}/adrules_library",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);`
```
```
`/* make the API call */
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/adrules_library",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();`
```
```
`/* make the API call */
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/adrules_library"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of [AdRule](/docs/marketing-api/reference/ad-rule/) nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

200

Permissions error

100

Invalid parameter

190

Invalid OAuth 2.0 Access Token

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

368

The action attempted has been deemed abusive or is otherwise disallowed

[](#)

## Creating

You can make a POST request to `adrules_library` edge from the following paths:

*   [`/act_{ad_account_id}/adrules_library`](/docs/marketing-api/reference/ad-account/adrules_library/)

When posting to this edge, an [AdRule](/docs/marketing-api/reference/ad-rule/) will be created.

### Parameters

Parameter

Description

`account_id`

numeric string

Ad Account ID. This is inferred from the path.

`evaluation_spec`

Object

Defines the evaluation spec upon which a rule will be executed

Required

`evaluation_type`

enum{SCHEDULE, TRIGGER}

Required

`filters`

list<Object>

Required

`field`

string

Required

`value`

numeric, string, boolean, list<>, or object-like arrays

Required

`operator`

enum{GREATER\_THAN, LESS\_THAN, EQUAL, NOT\_EQUAL, IN\_RANGE, NOT\_IN\_RANGE, IN, NOT\_IN, CONTAIN, NOT\_CONTAIN, ANY, ALL, NONE}

Required

`trigger`

Object

`type`

enum{METADATA\_CREATION, METADATA\_UPDATE, STATS\_MILESTONE, STATS\_CHANGE, DELIVERY\_INSIGHTS\_CHANGE}

Required

`field`

string

`value`

numeric, string, boolean, list<>, or object-like arrays

`operator`

enum{GREATER\_THAN, LESS\_THAN, EQUAL, NOT\_EQUAL, IN\_RANGE, NOT\_IN\_RANGE, IN, NOT\_IN, CONTAIN, NOT\_CONTAIN, ANY, ALL, NONE}

`execution_spec`

Object

Defines the execution spec upon which a rule will be executed

Required

`execution_type`

enum{DCO, PING\_ENDPOINT, NOTIFICATION, PAUSE, REBALANCE\_BUDGET, CHANGE\_BUDGET, CHANGE\_BID, ROTATE, UNPAUSE, CHANGE\_CAMPAIGN\_BUDGET, ADD\_INTEREST\_RELAXATION, ADD\_QUESTIONNAIRE\_INTERESTS, INCREASE\_RADIUS, UPDATE\_CREATIVE, UPDATE\_LAX\_BUDGET, UPDATE\_LAX\_DURATION, AUDIENCE\_CONSOLIDATION, AUDIENCE\_CONSOLIDATION\_ASK\_FIRST, AD\_RECOMMENDATION\_APPLY}

Required

`is_once_off`

boolean

`execution_options`

list<Object>

`field`

string

Required

`value`

numeric, string, boolean, list<>, or object-like arrays

Required

`operator`

enum{EQUAL, IN}

Required

`name`

string

The friendly name of a rule, optional for inline rules

Required

`schedule_spec`

Object

Specifies the schedule with which a rule will be evaluated

`schedule_type`

enum{DAILY, HOURLY, SEMI\_HOURLY, CUSTOM}

Required

`schedule`

list<Object>

`start_minute`

int64

`end_minute`

int64

`days`

list<int64>

`status`

enum {ENABLED, DISABLED, DELETED, HAS\_ISSUES}

The status of a rule

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.

Struct {

`id`: numeric string,

}

### Error Codes

Error

Description

200

Permissions error

100

Invalid parameter

2703

Rules that turn off ads can't have cost conditions. You need to change the rule's conditions or action.

190

Invalid OAuth 2.0 Access Token

368

The action attempted has been deemed abusive or is otherwise disallowed

[](#)

## Updating

You can't perform this operation on this endpoint.

[](#)

## Deleting

You can't perform this operation on this endpoint.

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)