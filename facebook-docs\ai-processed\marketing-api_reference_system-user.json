{"title": "Facebook Marketing API - System User Reference", "summary": "Complete reference documentation for the System User endpoint in Facebook Marketing API v23.0. Covers reading system user data, creating new system users, and managing system user permissions within Business Manager.", "content": "# System User\n\n## Overview\n\nRepresents a system user in Facebook's Marketing API. System users are automated accounts that can be used to manage business assets and perform API operations on behalf of a business.\n\n## Reading System Users\n\n### Endpoint\n```\nGET /v23.0/{system-user-id}\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Fields\n\n| Field | Type | Description | Default |\n|-------|------|-------------|----------|\n| `id` | numeric string | System user ID | ✓ |\n| `created_by` | User | The creator of this system user | |\n| `created_time` | datetime | The creation time of this system user | |\n| `finance_permission` | string | Financial permission role (Editor, Analyst, etc.) | |\n| `ip_permission` | string | Ads right permission role (Reviewer, etc.) | |\n| `name` | string | Name used to identify this system user | ✓ |\n\n### Edges\n\n| Edge | Type | Description |\n|------|------|-------------|\n| `assigned_business_asset_groups` | Edge<BusinessAssetGroup> | Business asset groups assigned to this user |\n| `assigned_pages` | Edge<Page> | Pages assigned to this user |\n| `assigned_product_catalogs` | Edge<ProductCatalog> | Product catalogs assigned to this user |\n\n## Creating System Users\n\n### Endpoint\n```\nPOST /{business_id}/system_users\n```\n\n### Parameters\n\n| Parameter | Type | Required | Description |\n|-----------|------|----------|-------------|\n| `name` | string | ✓ | Name of system user to be added |\n| `role` | enum | | Role of system user (see roles below) |\n| `system_user_id` | int | | ID of system user |\n\n### Available Roles\n- `FINANCE_EDITOR`\n- `FINANCE_ANALYST` \n- `ADS_RIGHTS_REVIEWER`\n- `ADMIN`\n- `EMPLOYEE`\n- `DEVELOPER`\n- `PARTNER_CENTER_ADMIN`\n- `PARTNER_CENTER_ANALYST`\n- `PARTNER_CENTER_OPERATIONS`\n- `PARTNER_CENTER_MARKETING`\n- `PARTNER_CENTER_EDUCATION`\n- `MANAGE`\n- `DEFAULT`\n- `FINANCE_EDIT`\n- `FINANCE_VIEW`\n\n### Return Type\nReturns a struct with the created system user ID:\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n## Error Codes\n\n### Reading Errors\n- `100`: Invalid parameter\n- `110`: Invalid user id\n\n### Creating Errors\n- `104001`: App must be part of business to create system user\n- `3965`: Maximum admin system user limit reached\n- `3949`: Maximum system user limit reached\n- `100`: Invalid parameter\n- `102`: Session key invalid or no longer valid\n\n## Limitations\n- **Updating**: Not supported on this endpoint\n- **Deleting**: Not supported on this endpoint", "keyPoints": ["System users are automated accounts for managing business assets via API", "Reading system users requires only the system-user-id in the URL path", "Creating system users requires the app to be part of the target business", "System users have various permission roles for different business functions", "Business Manager has limits on total and admin system users"], "apiEndpoints": ["GET /v23.0/{system-user-id}", "POST /{business_id}/system_users"], "parameters": ["system-user-id", "name", "role", "system_user_id", "business_id"], "examples": ["GET /v23.0/{system-user-id} HTTP/1.1\nHost: graph.facebook.com"], "tags": ["Facebook Marketing API", "System User", "Business Manager", "API Reference", "Graph API", "Authentication"], "relatedTopics": ["Business Manager", "User permissions", "Business Asset Groups", "Pages", "Product Catalogs", "Graph API", "Access tokens"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/system-user", "processedAt": "2025-06-25T15:47:50.468Z", "processor": "openrouter-claude-sonnet-4"}