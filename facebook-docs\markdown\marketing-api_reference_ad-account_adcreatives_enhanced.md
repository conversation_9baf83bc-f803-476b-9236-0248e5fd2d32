# Facebook Marketing API - Ad Account Ad Creatives Reference

## Summary
Complete reference for managing ad creatives within Facebook ad accounts, including reading existing creatives, creating new ones with various specifications, and understanding the available parameters and error codes.

## Key Points
- Ad creatives contain all media and text content used in Facebook ads
- Supports various media types including images, videos, and interactive components
- Dynamic Creative allows automatic testing of different creative variations
- Platform customizations enable different media for different placements
- Rate limiting applies - monitor API call frequency to avoid errors

## API Endpoints
- `GET /v23.0/act_<AD_ACCOUNT_ID>/adcreatives`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives`

## Parameters
- name
- actor_id
- body
- title
- image_file
- image_hash
- image_url
- call_to_action
- object_story_spec
- object_story_id
- asset_feed_spec
- platform_customizations
- branded_content
- interactive_components_spec

## Content
# Ad Account Ad Creatives

The Ad Creatives endpoint manages creative content for an ad account that can be used in ads, including images, videos, and other media assets. Using an ad account creative for a particular ad is subject to validation rules based on the ad type and other requirements.

## Reading Ad Creatives

To retrieve an account's ad creatives, make an HTTP GET call to:

```
GET /v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Example Request

```bash
curl -X GET -G \
  -d 'fields="name"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Response Format

Reading from this edge returns a JSON formatted result:

```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Response Fields

- **data**: A list of AdCreative nodes
- **paging**: Pagination information for navigating through results
- **summary**: Aggregated information about the edge, such as counts

### Summary Fields

| Field | Type | Description |
|-------|------|-------------|
| `total_count` | unsigned int32 | Total number of creatives in the ad account |

## Creating Ad Creatives

You can create new ad creatives by making a POST request to the `adcreatives` edge:

```
POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Example Creation

```bash
curl -X POST \
  -F 'name="Sample Promoted Post"' \
  -F 'object_story_id="<PAGE_ID>_<POST_ID>"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

### Key Parameters

#### Basic Creative Information

- **name** (string): Name of the ad creative as seen in the ad account's library
- **actor_id** (int64): The actor ID (Page ID) of this creative
- **body** (string): The body text of the ad (supports emoji)
- **title** (string): Title for the creative

#### Media Specifications

- **image_file** (string): Reference to a local image file (max 8MB)
- **image_hash** (string): Hash for an uploaded image
- **image_url** (URL): URL for the image in the creative
- **image_crops** (object): Crop dimensions for the image

#### Call to Action

```json
{
  "type": "LEARN_MORE",
  "value": {
    "link": "https://example.com",
    "link_title": "Learn More",
    "link_description": "Click to learn more"
  }
}
```

#### Asset Feed Specification (Dynamic Creative)

For Dynamic Creative optimization:

```json
{
  "images": [
    {
      "hash": "image_hash_here",
      "url": "https://example.com/image.jpg"
    }
  ],
  "bodies": [
    {
      "text": "Primary ad text here"
    }
  ],
  "titles": [
    {
      "text": "Ad headline here"
    }
  ]
}
```

#### Object Story Specification

For creating new unpublished page posts:

```json
{
  "page_id": "<PAGE_ID>",
  "link_data": {
    "link": "https://example.com",
    "message": "Check this out!",
    "name": "Link Title",
    "description": "Link description"
  }
}
```

### Advanced Features

#### Branded Content

```json
{
  "partners": [
    {
      "fb_page_id": "<PARTNER_PAGE_ID>",
      "identity_type": "PARTNER_CREATOR"
    }
  ]
}
```

#### Platform Customizations

Specify different media for different placements:

```json
{
  "instagram": {
    "image_url": "https://example.com/instagram-image.jpg",
    "image_crops": {
      "100x100": [[0, 0, 100, 100]]
    }
  }
}
```

#### Interactive Components

Add polls and other interactive elements:

```json
{
  "components": [
    {
      "poll_spec": {
        "question_text": "What do you think?",
        "option_a_text": "Yes",
        "option_b_text": "No"
      }
    }
  ]
}
```

### Limitations

1. When creating ad creatives, if the `object_story_id` is already in use by an existing creative, the API returns the existing creative_id instead of creating a new one
2. Using `radius` can cause errors when targeting multiple locations
3. Images cannot exceed 8MB in size

### Return Type

Successful creation returns:

```json
{
  "id": "creative_id",
  "success": true
}
```

## Error Codes

### Reading Errors

| Code | Description |
|------|-------------|
| 200 | Permissions error |
| 100 | Invalid parameter |
| 80004 | Too many calls to ad account (rate limiting) |
| 190 | Invalid OAuth 2.0 Access Token |
| 2500 | Error parsing graph query |

### Creating Errors

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 200 | Permissions error |
| 500 | Message contains banned content |
| 1500 | Invalid URL supplied |
| 80004 | Rate limiting - too many calls |
| 105 | Too many parameters |
| 368 | Action deemed abusive or disallowed |
| 194 | Missing required parameter |
| 2635 | Deprecated API version |

## Updating and Deleting

Updating and deleting operations are not supported on this endpoint. Ad creatives are typically managed through creation of new versions rather than modification of existing ones.

## Examples
curl -X GET -G -d 'fields="name"' -d 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives

curl -X POST -F 'name="Sample Promoted Post"' -F 'object_story_id="<PAGE_ID>_<POST_ID>"' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives

---
**Tags:** Facebook Marketing API, Ad Creatives, Advertising, Media Management, Dynamic Creative, Branded Content  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adcreatives/  
**Processed:** 2025-06-25T16:11:35.485Z