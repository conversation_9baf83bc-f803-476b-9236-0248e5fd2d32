# Facebook Marketing API - Ad Account Minimum Budgets

## Summary
This endpoint retrieves the minimum daily budget value for ad sets in Auction campaigns. It provides budget requirements based on manual bid amounts and returns MinimumBudget objects with pagination support.

## Key Points
- Retrieves minimum daily budget values for ad sets in Auction campaigns
- Supports manual bid amount parameter for customized budget calculations
- Returns paginated list of MinimumBudget objects
- Read-only endpoint - no create, update, or delete operations supported
- Available in Graph API v23.0 with multiple SDK implementations

## API Endpoints
- `GET /v23.0/{ad-account-id}/minimum_budgets`

## Parameters
- bid_amount (integer) - Manual bid value for budget calculation
- ad-account-id (path parameter) - Target ad account identifier

## Content
# Ad Account Minimum Budgets

## Overview

The Ad Account Minimum Budgets endpoint allows you to retrieve the minimum daily budget value for an ad set in an Auction campaign, given the bid_amount if using manual bid.

## Reading

### Endpoint
```
GET /v23.0/{ad-account-id}/minimum_budgets
```

### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `bid_amount` | integer | Provide this value if you want to get values for manual bid |

### Response Format

Reading from this edge will return a JSON formatted result:

```json
{
    "data": [],
    "paging": {}
}
```

#### Response Fields

- **`data`**: A list of MinimumBudget nodes
- **`paging`**: Pagination information (see Graph API guide for details)

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/minimum_budgets HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/minimum_budgets',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/minimum_budgets",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

#### Android SDK
```java
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/minimum_budgets",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();
```

#### iOS SDK
```objc
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/minimum_budgets"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];
```

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |

## Limitations

- **Creating**: You can't perform this operation on this endpoint
- **Updating**: You can't perform this operation on this endpoint  
- **Deleting**: You can't perform this operation on this endpoint

This is a read-only endpoint that only supports GET operations.

## Examples
HTTP GET request to minimum_budgets endpoint

PHP SDK implementation with error handling

JavaScript SDK API call with response handling

Android SDK GraphRequest implementation

iOS SDK FBSDKGraphRequest implementation

---
**Tags:** Facebook Marketing API, Ad Account, Minimum Budgets, Auction Campaigns, Budget Management, Graph API  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/minimum_budgets/  
**Processed:** 2025-06-25T16:28:34.971Z