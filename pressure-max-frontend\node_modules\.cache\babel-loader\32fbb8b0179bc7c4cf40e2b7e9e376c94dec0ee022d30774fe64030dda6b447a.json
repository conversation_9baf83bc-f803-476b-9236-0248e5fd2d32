{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\pages\\\\Landing.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport { Zap, MessageSquare, Target, Sparkles, Star, ArrowRight, Phone, Mail, MapPin, Shield, BarChart3, Facebook, User, Eye, EyeOff } from 'lucide-react';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Landing = () => {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  } = useAuth();\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [showAuthModal, setShowAuthModal] = useState(false);\n  const loginForm = useForm();\n  const registerForm = useForm();\n  useEffect(() => {\n    const timer = setTimeout(() => setIsLoaded(true), 500);\n    return () => clearTimeout(timer);\n  }, []);\n\n  // Data for the landing page\n  const features = [{\n    icon: Target,\n    title: \"AI-Powered Ad Templates\",\n    subtitle: \"20+ Proven Templates\",\n    description: \"Ready-to-deploy Facebook ad templates specifically designed for pressure washing businesses. Each template is optimized for maximum lead generation and tested across thousands of campaigns.\",\n    metrics: [\"3.2% average CTR\", \"$12 average cost per lead\", \"28% conversion rate\"]\n  }, {\n    icon: MessageSquare,\n    title: \"Automated Lead Follow-up\",\n    subtitle: \"Instant Response\",\n    description: \"Never miss a lead with our automated follow-up system that nurtures prospects and converts them into customers.\",\n    metrics: [\"5-minute response time\", \"85% contact rate\", \"40% appointment booking rate\"]\n  }, {\n    icon: BarChart3,\n    title: \"Performance Analytics\",\n    subtitle: \"Data-Driven Insights\",\n    description: \"Track your ROI with detailed analytics and insights on campaign performance, lead quality, and conversion rates.\",\n    metrics: [\"Real-time reporting\", \"ROI tracking\", \"Lead quality scoring\"]\n  }];\n  const testimonials = [{\n    name: \"Mike Rodriguez\",\n    business: \"Rodriguez Pressure Wash\",\n    rating: 5,\n    text: \"This platform tripled my revenue in just 30 days. It's like having a dedicated marketing team working around the clock.\",\n    results: \"+300% Revenue\",\n    status: \"Verified Customer\"\n  }, {\n    name: \"Sarah Chen\",\n    business: \"Crystal Clean Systems\",\n    rating: 5,\n    text: \"I'm saving 3 hours of marketing work every day. The AI handles everything flawlessly, letting me focus on my customers.\",\n    results: \"3+ Hours Saved Per Day\",\n    status: \"Verified Customer\"\n  }, {\n    name: \"David Thompson\",\n    business: \"Thompson Power Washing\",\n    rating: 5,\n    text: \"The return on investment is incredible. I'm getting more qualified leads than I ever thought possible. This is the future of marketing for our industry.\",\n    results: \"+500% ROI\",\n    status: \"Verified Customer\"\n  }];\n\n  // Redirect to dashboard if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n  const onLogin = async data => {\n    setLoading(true);\n    const result = await login(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n  const onRegister = async data => {\n    setLoading(true);\n    const result = await register(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      if (response.data.oauthUrl) {\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      if (state !== storedState) {\n        throw new Error('Invalid state parameter');\n      }\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      if (response.data.user && response.data.tokens) {\n        const {\n          user: userData,\n          tokens\n        } = response.data;\n        localStorage.setItem('accessToken', tokens.accessToken);\n        localStorage.setItem('refreshToken', tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(userData));\n        localStorage.removeItem('facebook_oauth_state');\n        toast.success('Facebook login successful!');\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      window.history.replaceState({}, document.title, '/landing');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\uD83D\\uDE80 Pressure Max\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-subtitle\",\n            children: \"Powerful Facebook Marketing API Integration & Campaign Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"hero-description\",\n            children: \"Streamline your Facebook advertising with our comprehensive API testing interface, campaign management tools, and real-time analytics dashboard.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(Shield, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Secure Authentication\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(Zap, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Real-time API Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Campaign Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-container\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"auth-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"auth-tabs\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: activeTab === 'facebook' ? 'active' : '',\n                onClick: () => setActiveTab('facebook'),\n                children: [/*#__PURE__*/_jsxDEV(Facebook, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), \"Facebook Login\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: activeTab === 'login' ? 'active' : '',\n                onClick: () => setActiveTab('login'),\n                children: \"Email Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: activeTab === 'register' ? 'active' : '',\n                onClick: () => setActiveTab('register'),\n                children: \"Register\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), activeTab === 'facebook' ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"facebook-login-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"facebook-login-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [/*#__PURE__*/_jsxDEV(Facebook, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), \"Quick Facebook Login\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Connect with your Facebook account to access:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Automatic Marketing API permissions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Campaign management tools\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Real-time analytics\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"\\u2705 Lead form integration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleFacebookLogin,\n                disabled: loading,\n                className: \"facebook-login-btn\",\n                children: [/*#__PURE__*/_jsxDEV(Facebook, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this), loading ? 'Connecting...' : 'Continue with Facebook', /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"facebook-note\",\n                children: [\"Using App ID: \", /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: \"***************\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 35\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 63\n                }, this), \"This will automatically grant marketing API permissions.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this) : activeTab === 'login' ? /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: loginForm.handleSubmit(onLogin),\n              className: \"auth-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  ...loginForm.register('email', {\n                    required: 'Email is required'\n                  }),\n                  placeholder: \"Enter your email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this), loginForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: loginForm.formState.errors.email.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  ...loginForm.register('password', {\n                    required: 'Password is required'\n                  }),\n                  placeholder: \"Enter your password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this), loginForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: loginForm.formState.errors.password.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"submit-btn\",\n                children: [loading ? 'Logging in...' : 'Login', /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: registerForm.handleSubmit(onRegister),\n              className: \"auth-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"First Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  ...registerForm.register('firstName', {\n                    required: 'First name is required'\n                  }),\n                  placeholder: \"Enter your first name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.firstName && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.firstName.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Last Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  ...registerForm.register('lastName', {\n                    required: 'Last name is required'\n                  }),\n                  placeholder: \"Enter your last name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.lastName && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.lastName.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  ...registerForm.register('email', {\n                    required: 'Email is required'\n                  }),\n                  placeholder: \"Enter your email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.email && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.email.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  ...registerForm.register('password', {\n                    required: 'Password is required',\n                    minLength: {\n                      value: 6,\n                      message: 'Password must be at least 6 characters'\n                    }\n                  }),\n                  placeholder: \"Enter your password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 21\n                }, this), registerForm.formState.errors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"error\",\n                  children: registerForm.formState.errors.password.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                disabled: loading,\n                className: \"submit-btn\",\n                children: [loading ? 'Creating Account...' : 'Create Account', /*#__PURE__*/_jsxDEV(ArrowRight, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"landing-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Pressure Max API Testing Interface - Built with React & Supabase\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Backend API: \", /*#__PURE__*/_jsxDEV(\"code\", {\n          children: \"http://localhost:3000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(Landing, \"yJSIaCuL/QBUOsqyagGjQkvVgXA=\", false, function () {\n  return [useAuth, useNavigate, useForm, useForm];\n});\n_c = Landing;\nexport default Landing;\nvar _c;\n$RefreshReg$(_c, \"Landing\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useNavigate", "useForm", "facebookAPI", "Zap", "MessageSquare", "Target", "<PERSON><PERSON><PERSON>", "Star", "ArrowRight", "Phone", "Mail", "MapPin", "Shield", "BarChart3", "Facebook", "User", "Eye", "Eye<PERSON>ff", "toast", "jsxDEV", "_jsxDEV", "Landing", "_s", "user", "isAuthenticated", "login", "register", "logout", "getToken", "navigate", "activeTab", "setActiveTab", "showToken", "setShowToken", "loading", "setLoading", "isLoaded", "setIsLoaded", "showAuthModal", "setShowAuthModal", "loginForm", "registerForm", "timer", "setTimeout", "clearTimeout", "features", "icon", "title", "subtitle", "description", "metrics", "testimonials", "name", "business", "rating", "text", "results", "status", "urlParams", "URLSearchParams", "window", "location", "search", "code", "get", "state", "handleFacebookCallback", "onLogin", "data", "result", "success", "onRegister", "handleFacebookLogin", "redirectUri", "origin", "response", "getOAuthUrl", "oauthUrl", "localStorage", "setItem", "href", "Error", "error", "message", "storedState", "getItem", "handleOAuthCallback", "tokens", "userData", "accessToken", "refreshToken", "JSON", "stringify", "removeItem", "history", "replaceState", "document", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "Users", "onClick", "disabled", "onSubmit", "handleSubmit", "type", "required", "placeholder", "formState", "errors", "email", "password", "firstName", "lastName", "<PERSON><PERSON><PERSON><PERSON>", "value", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/pages/Landing.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { useForm } from 'react-hook-form';\nimport { facebookAPI } from '../services/api';\nimport {\n  Zap,\n  MessageSquare,\n  Target,\n  Sparkles,\n  Star,\n  ArrowRight,\n  Phone,\n  Mail,\n  MapPin,\n  Shield,\n  BarChart3,\n  Facebook,\n  User,\n  Eye,\n  EyeOff\n} from 'lucide-react';\nimport toast from 'react-hot-toast';\n\nconst Landing = () => {\n  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();\n  const navigate = useNavigate();\n  const [activeTab, setActiveTab] = useState('facebook');\n  const [showToken, setShowToken] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [showAuthModal, setShowAuthModal] = useState(false);\n\n  const loginForm = useForm();\n  const registerForm = useForm();\n\n  useEffect(() => {\n    const timer = setTimeout(() => setIsLoaded(true), 500);\n    return () => clearTimeout(timer);\n  }, []);\n\n  // Data for the landing page\n  const features = [\n    {\n      icon: Target,\n      title: \"AI-Powered Ad Templates\",\n      subtitle: \"20+ Proven Templates\",\n      description: \"Ready-to-deploy Facebook ad templates specifically designed for pressure washing businesses. Each template is optimized for maximum lead generation and tested across thousands of campaigns.\",\n      metrics: [\n        \"3.2% average CTR\",\n        \"$12 average cost per lead\",\n        \"28% conversion rate\"\n      ]\n    },\n    {\n      icon: MessageSquare,\n      title: \"Automated Lead Follow-up\",\n      subtitle: \"Instant Response\",\n      description: \"Never miss a lead with our automated follow-up system that nurtures prospects and converts them into customers.\",\n      metrics: [\n        \"5-minute response time\",\n        \"85% contact rate\",\n        \"40% appointment booking rate\"\n      ]\n    },\n    {\n      icon: BarChart3,\n      title: \"Performance Analytics\",\n      subtitle: \"Data-Driven Insights\",\n      description: \"Track your ROI with detailed analytics and insights on campaign performance, lead quality, and conversion rates.\",\n      metrics: [\n        \"Real-time reporting\",\n        \"ROI tracking\",\n        \"Lead quality scoring\"\n      ]\n    }\n  ];\n\n  const testimonials = [\n    {\n      name: \"Mike Rodriguez\",\n      business: \"Rodriguez Pressure Wash\",\n      rating: 5,\n      text: \"This platform tripled my revenue in just 30 days. It's like having a dedicated marketing team working around the clock.\",\n      results: \"+300% Revenue\",\n      status: \"Verified Customer\"\n    },\n    {\n      name: \"Sarah Chen\",\n      business: \"Crystal Clean Systems\",\n      rating: 5,\n      text: \"I'm saving 3 hours of marketing work every day. The AI handles everything flawlessly, letting me focus on my customers.\",\n      results: \"3+ Hours Saved Per Day\",\n      status: \"Verified Customer\"\n    },\n    {\n      name: \"David Thompson\",\n      business: \"Thompson Power Washing\",\n      rating: 5,\n      text: \"The return on investment is incredible. I'm getting more qualified leads than I ever thought possible. This is the future of marketing for our industry.\",\n      results: \"+500% ROI\",\n      status: \"Verified Customer\"\n    }\n  ];\n\n  // Redirect to dashboard if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard');\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Check for Facebook OAuth callback\n  useEffect(() => {\n    const urlParams = new URLSearchParams(window.location.search);\n    const code = urlParams.get('code');\n    const state = urlParams.get('state');\n    \n    if (code && state && !isAuthenticated) {\n      handleFacebookCallback(code, state);\n    }\n  }, [isAuthenticated]);\n\n  const onLogin = async (data) => {\n    setLoading(true);\n    const result = await login(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n\n  const onRegister = async (data) => {\n    setLoading(true);\n    const result = await register(data);\n    if (result.success) {\n      navigate('/dashboard');\n    }\n    setLoading(false);\n  };\n\n  const handleFacebookLogin = async () => {\n    try {\n      setLoading(true);\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.getOAuthUrl(redirectUri);\n      \n      if (response.data.oauthUrl) {\n        localStorage.setItem('facebook_oauth_state', response.data.state);\n        window.location.href = response.data.oauthUrl;\n      } else {\n        throw new Error('No OAuth URL received');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Failed to initiate Facebook login: ' + error.message);\n    }\n  };\n\n  const handleFacebookCallback = async (code, state) => {\n    try {\n      setLoading(true);\n      const storedState = localStorage.getItem('facebook_oauth_state');\n      \n      if (state !== storedState) {\n        throw new Error('Invalid state parameter');\n      }\n\n      const redirectUri = `${window.location.origin}/landing`;\n      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);\n      \n      if (response.data.user && response.data.tokens) {\n        const { user: userData, tokens } = response.data;\n        \n        localStorage.setItem('accessToken', tokens.accessToken);\n        localStorage.setItem('refreshToken', tokens.refreshToken);\n        localStorage.setItem('user', JSON.stringify(userData));\n        localStorage.removeItem('facebook_oauth_state');\n        \n        toast.success('Facebook login successful!');\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      setLoading(false);\n      toast.error('Facebook login failed: ' + error.message);\n      window.history.replaceState({}, document.title, '/landing');\n    }\n  };\n\n  return (\n    <div className=\"landing-page\">\n      {/* Hero Section */}\n      <div className=\"hero-section\">\n        <div className=\"hero-content\">\n          <div className=\"hero-text\">\n            <h1>🚀 Pressure Max</h1>\n            <p className=\"hero-subtitle\">\n              Powerful Facebook Marketing API Integration & Campaign Management\n            </p>\n            <p className=\"hero-description\">\n              Streamline your Facebook advertising with our comprehensive API testing interface, \n              campaign management tools, and real-time analytics dashboard.\n            </p>\n            \n            <div className=\"features-grid\">\n              <div className=\"feature-item\">\n                <Shield size={24} />\n                <span>Secure Authentication</span>\n              </div>\n              <div className=\"feature-item\">\n                <Zap size={24} />\n                <span>Real-time API Testing</span>\n              </div>\n              <div className=\"feature-item\">\n                <Users size={24} />\n                <span>Campaign Management</span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"auth-container\">\n            <div className=\"auth-card\">\n              <h2>Get Started</h2>\n              \n              <div className=\"auth-tabs\">\n                <button \n                  className={activeTab === 'facebook' ? 'active' : ''}\n                  onClick={() => setActiveTab('facebook')}\n                >\n                  <Facebook size={16} />\n                  Facebook Login\n                </button>\n                <button \n                  className={activeTab === 'login' ? 'active' : ''}\n                  onClick={() => setActiveTab('login')}\n                >\n                  Email Login\n                </button>\n                <button \n                  className={activeTab === 'register' ? 'active' : ''}\n                  onClick={() => setActiveTab('register')}\n                >\n                  Register\n                </button>\n              </div>\n\n              {activeTab === 'facebook' ? (\n                <div className=\"facebook-login-section\">\n                  <div className=\"facebook-login-info\">\n                    <h3>\n                      <Facebook size={20} />\n                      Quick Facebook Login\n                    </h3>\n                    <p>Connect with your Facebook account to access:</p>\n                    <ul>\n                      <li>✅ Automatic Marketing API permissions</li>\n                      <li>✅ Campaign management tools</li>\n                      <li>✅ Real-time analytics</li>\n                      <li>✅ Lead form integration</li>\n                    </ul>\n                  </div>\n                  \n                  <button\n                    onClick={handleFacebookLogin}\n                    disabled={loading}\n                    className=\"facebook-login-btn\"\n                  >\n                    <Facebook size={20} />\n                    {loading ? 'Connecting...' : 'Continue with Facebook'}\n                    <ArrowRight size={16} />\n                  </button>\n                  \n                  <p className=\"facebook-note\">\n                    Using App ID: <code>***************</code><br/>\n                    This will automatically grant marketing API permissions.\n                  </p>\n                </div>\n              ) : activeTab === 'login' ? (\n                <form onSubmit={loginForm.handleSubmit(onLogin)} className=\"auth-form\">\n                  <div className=\"form-group\">\n                    <label>Email:</label>\n                    <input\n                      type=\"email\"\n                      {...loginForm.register('email', { required: 'Email is required' })}\n                      placeholder=\"Enter your email\"\n                    />\n                    {loginForm.formState.errors.email && (\n                      <span className=\"error\">{loginForm.formState.errors.email.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Password:</label>\n                    <input\n                      type=\"password\"\n                      {...loginForm.register('password', { required: 'Password is required' })}\n                      placeholder=\"Enter your password\"\n                    />\n                    {loginForm.formState.errors.password && (\n                      <span className=\"error\">{loginForm.formState.errors.password.message}</span>\n                    )}\n                  </div>\n\n                  <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n                    {loading ? 'Logging in...' : 'Login'}\n                    <ArrowRight size={16} />\n                  </button>\n                </form>\n              ) : (\n                <form onSubmit={registerForm.handleSubmit(onRegister)} className=\"auth-form\">\n                  <div className=\"form-group\">\n                    <label>First Name:</label>\n                    <input\n                      type=\"text\"\n                      {...registerForm.register('firstName', { required: 'First name is required' })}\n                      placeholder=\"Enter your first name\"\n                    />\n                    {registerForm.formState.errors.firstName && (\n                      <span className=\"error\">{registerForm.formState.errors.firstName.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Last Name:</label>\n                    <input\n                      type=\"text\"\n                      {...registerForm.register('lastName', { required: 'Last name is required' })}\n                      placeholder=\"Enter your last name\"\n                    />\n                    {registerForm.formState.errors.lastName && (\n                      <span className=\"error\">{registerForm.formState.errors.lastName.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Email:</label>\n                    <input\n                      type=\"email\"\n                      {...registerForm.register('email', { required: 'Email is required' })}\n                      placeholder=\"Enter your email\"\n                    />\n                    {registerForm.formState.errors.email && (\n                      <span className=\"error\">{registerForm.formState.errors.email.message}</span>\n                    )}\n                  </div>\n\n                  <div className=\"form-group\">\n                    <label>Password:</label>\n                    <input\n                      type=\"password\"\n                      {...registerForm.register('password', { \n                        required: 'Password is required',\n                        minLength: { value: 6, message: 'Password must be at least 6 characters' }\n                      })}\n                      placeholder=\"Enter your password\"\n                    />\n                    {registerForm.formState.errors.password && (\n                      <span className=\"error\">{registerForm.formState.errors.password.message}</span>\n                    )}\n                  </div>\n\n                  <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n                    {loading ? 'Creating Account...' : 'Create Account'}\n                    <ArrowRight size={16} />\n                  </button>\n                </form>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <footer className=\"landing-footer\">\n        <p>Pressure Max API Testing Interface - Built with React & Supabase</p>\n        <p>Backend API: <code>http://localhost:3000</code></p>\n      </footer>\n    </div>\n  );\n};\n\nexport default Landing;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SACEC,GAAG,EACHC,aAAa,EACbC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,MAAM,QACD,cAAc;AACrB,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAC9E,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,UAAU,CAAC;EACtD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM2C,SAAS,GAAGvC,OAAO,CAAC,CAAC;EAC3B,MAAMwC,YAAY,GAAGxC,OAAO,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IACd,MAAM4C,KAAK,GAAGC,UAAU,CAAC,MAAMN,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IACtD,OAAO,MAAMO,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,QAAQ,GAAG,CACf;IACEC,IAAI,EAAEzC,MAAM;IACZ0C,KAAK,EAAE,yBAAyB;IAChCC,QAAQ,EAAE,sBAAsB;IAChCC,WAAW,EAAE,+LAA+L;IAC5MC,OAAO,EAAE,CACP,kBAAkB,EAClB,2BAA2B,EAC3B,qBAAqB;EAEzB,CAAC,EACD;IACEJ,IAAI,EAAE1C,aAAa;IACnB2C,KAAK,EAAE,0BAA0B;IACjCC,QAAQ,EAAE,kBAAkB;IAC5BC,WAAW,EAAE,iHAAiH;IAC9HC,OAAO,EAAE,CACP,wBAAwB,EACxB,kBAAkB,EAClB,8BAA8B;EAElC,CAAC,EACD;IACEJ,IAAI,EAAEjC,SAAS;IACfkC,KAAK,EAAE,uBAAuB;IAC9BC,QAAQ,EAAE,sBAAsB;IAChCC,WAAW,EAAE,kHAAkH;IAC/HC,OAAO,EAAE,CACP,qBAAqB,EACrB,cAAc,EACd,sBAAsB;EAE1B,CAAC,CACF;EAED,MAAMC,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,yBAAyB;IACnCC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,yHAAyH;IAC/HC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE,uBAAuB;IACjCC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,yHAAyH;IAC/HC,OAAO,EAAE,wBAAwB;IACjCC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,wBAAwB;IAClCC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,0JAA0J;IAChKC,OAAO,EAAE,WAAW;IACpBC,MAAM,EAAE;EACV,CAAC,CACF;;EAED;EACA3D,SAAS,CAAC,MAAM;IACd,IAAI0B,eAAe,EAAE;MACnBK,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACL,eAAe,EAAEK,QAAQ,CAAC,CAAC;;EAE/B;EACA/B,SAAS,CAAC,MAAM;IACd,MAAM4D,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,MAAMC,IAAI,GAAGL,SAAS,CAACM,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMC,KAAK,GAAGP,SAAS,CAACM,GAAG,CAAC,OAAO,CAAC;IAEpC,IAAID,IAAI,IAAIE,KAAK,IAAI,CAACzC,eAAe,EAAE;MACrC0C,sBAAsB,CAACH,IAAI,EAAEE,KAAK,CAAC;IACrC;EACF,CAAC,EAAE,CAACzC,eAAe,CAAC,CAAC;EAErB,MAAM2C,OAAO,GAAG,MAAOC,IAAI,IAAK;IAC9BjC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMkC,MAAM,GAAG,MAAM5C,KAAK,CAAC2C,IAAI,CAAC;IAChC,IAAIC,MAAM,CAACC,OAAO,EAAE;MAClBzC,QAAQ,CAAC,YAAY,CAAC;IACxB;IACAM,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMoC,UAAU,GAAG,MAAOH,IAAI,IAAK;IACjCjC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMkC,MAAM,GAAG,MAAM3C,QAAQ,CAAC0C,IAAI,CAAC;IACnC,IAAIC,MAAM,CAACC,OAAO,EAAE;MAClBzC,QAAQ,CAAC,YAAY,CAAC;IACxB;IACAM,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMqC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsC,WAAW,GAAG,GAAGb,MAAM,CAACC,QAAQ,CAACa,MAAM,UAAU;MACvD,MAAMC,QAAQ,GAAG,MAAMzE,WAAW,CAAC0E,WAAW,CAACH,WAAW,CAAC;MAE3D,IAAIE,QAAQ,CAACP,IAAI,CAACS,QAAQ,EAAE;QAC1BC,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEJ,QAAQ,CAACP,IAAI,CAACH,KAAK,CAAC;QACjEL,MAAM,CAACC,QAAQ,CAACmB,IAAI,GAAGL,QAAQ,CAACP,IAAI,CAACS,QAAQ;MAC/C,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,uBAAuB,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd/C,UAAU,CAAC,KAAK,CAAC;MACjBjB,KAAK,CAACgE,KAAK,CAAC,qCAAqC,GAAGA,KAAK,CAACC,OAAO,CAAC;IACpE;EACF,CAAC;EAED,MAAMjB,sBAAsB,GAAG,MAAAA,CAAOH,IAAI,EAAEE,KAAK,KAAK;IACpD,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiD,WAAW,GAAGN,YAAY,CAACO,OAAO,CAAC,sBAAsB,CAAC;MAEhE,IAAIpB,KAAK,KAAKmB,WAAW,EAAE;QACzB,MAAM,IAAIH,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MAEA,MAAMR,WAAW,GAAG,GAAGb,MAAM,CAACC,QAAQ,CAACa,MAAM,UAAU;MACvD,MAAMC,QAAQ,GAAG,MAAMzE,WAAW,CAACoF,mBAAmB,CAACvB,IAAI,EAAEE,KAAK,EAAEQ,WAAW,CAAC;MAEhF,IAAIE,QAAQ,CAACP,IAAI,CAAC7C,IAAI,IAAIoD,QAAQ,CAACP,IAAI,CAACmB,MAAM,EAAE;QAC9C,MAAM;UAAEhE,IAAI,EAAEiE,QAAQ;UAAED;QAAO,CAAC,GAAGZ,QAAQ,CAACP,IAAI;QAEhDU,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEQ,MAAM,CAACE,WAAW,CAAC;QACvDX,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEQ,MAAM,CAACG,YAAY,CAAC;QACzDZ,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEY,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAC,CAAC;QACtDV,YAAY,CAACe,UAAU,CAAC,sBAAsB,CAAC;QAE/C3E,KAAK,CAACoD,OAAO,CAAC,4BAA4B,CAAC;QAC3CzC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACd/C,UAAU,CAAC,KAAK,CAAC;MACjBjB,KAAK,CAACgE,KAAK,CAAC,yBAAyB,GAAGA,KAAK,CAACC,OAAO,CAAC;MACtDvB,MAAM,CAACkC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACjD,KAAK,EAAE,UAAU,CAAC;IAC7D;EACF,CAAC;EAED,oBACE3B,OAAA;IAAK6E,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3B9E,OAAA;MAAK6E,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3B9E,OAAA;QAAK6E,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9E,OAAA;UAAK6E,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9E,OAAA;YAAA8E,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBlF,OAAA;YAAG6E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJlF,OAAA;YAAG6E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAGhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJlF,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B9E,OAAA;cAAK6E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B9E,OAAA,CAACR,MAAM;gBAAC2F,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBlF,OAAA;gBAAA8E,QAAA,EAAM;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACNlF,OAAA;cAAK6E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B9E,OAAA,CAACjB,GAAG;gBAACoG,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBlF,OAAA;gBAAA8E,QAAA,EAAM;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACNlF,OAAA;cAAK6E,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B9E,OAAA,CAACoF,KAAK;gBAACD,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnBlF,OAAA;gBAAA8E,QAAA,EAAM;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA;UAAK6E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7B9E,OAAA;YAAK6E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9E,OAAA;cAAA8E,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEpBlF,OAAA;cAAK6E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9E,OAAA;gBACE6E,SAAS,EAAEnE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;gBACpD2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,UAAU,CAAE;gBAAAmE,QAAA,gBAExC9E,OAAA,CAACN,QAAQ;kBAACyF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,kBAExB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlF,OAAA;gBACE6E,SAAS,EAAEnE,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG;gBACjD2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,OAAO,CAAE;gBAAAmE,QAAA,EACtC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlF,OAAA;gBACE6E,SAAS,EAAEnE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG;gBACpD2E,OAAO,EAAEA,CAAA,KAAM1E,YAAY,CAAC,UAAU,CAAE;gBAAAmE,QAAA,EACzC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELxE,SAAS,KAAK,UAAU,gBACvBV,OAAA;cAAK6E,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC9E,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA,CAACN,QAAQ;oBAACyF,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,wBAExB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLlF,OAAA;kBAAA8E,QAAA,EAAG;gBAA6C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpDlF,OAAA;kBAAA8E,QAAA,gBACE9E,OAAA;oBAAA8E,QAAA,EAAI;kBAAqC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9ClF,OAAA;oBAAA8E,QAAA,EAAI;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpClF,OAAA;oBAAA8E,QAAA,EAAI;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9BlF,OAAA;oBAAA8E,QAAA,EAAI;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENlF,OAAA;gBACEqF,OAAO,EAAEjC,mBAAoB;gBAC7BkC,QAAQ,EAAExE,OAAQ;gBAClB+D,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBAE9B9E,OAAA,CAACN,QAAQ;kBAACyF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACrBpE,OAAO,GAAG,eAAe,GAAG,wBAAwB,eACrDd,OAAA,CAACZ,UAAU;kBAAC+F,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAETlF,OAAA;gBAAG6E,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,gBACb,eAAA9E,OAAA;kBAAA8E,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAAAlF,OAAA;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,4DAEjD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,GACJxE,SAAS,KAAK,OAAO,gBACvBV,OAAA;cAAMuF,QAAQ,EAAEnE,SAAS,CAACoE,YAAY,CAACzC,OAAO,CAAE;cAAC8B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACpE9E,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9E,OAAA;kBAAA8E,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBlF,OAAA;kBACEyF,IAAI,EAAC,OAAO;kBAAA,GACRrE,SAAS,CAACd,QAAQ,CAAC,OAAO,EAAE;oBAAEoF,QAAQ,EAAE;kBAAoB,CAAC,CAAC;kBAClEC,WAAW,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,EACD9D,SAAS,CAACwE,SAAS,CAACC,MAAM,CAACC,KAAK,iBAC/B9F,OAAA;kBAAM6E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE1D,SAAS,CAACwE,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC/B;gBAAO;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACzE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlF,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9E,OAAA;kBAAA8E,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBlF,OAAA;kBACEyF,IAAI,EAAC,UAAU;kBAAA,GACXrE,SAAS,CAACd,QAAQ,CAAC,UAAU,EAAE;oBAAEoF,QAAQ,EAAE;kBAAuB,CAAC,CAAC;kBACxEC,WAAW,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EACD9D,SAAS,CAACwE,SAAS,CAACC,MAAM,CAACE,QAAQ,iBAClC/F,OAAA;kBAAM6E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAE1D,SAAS,CAACwE,SAAS,CAACC,MAAM,CAACE,QAAQ,CAAChC;gBAAO;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC5E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlF,OAAA;gBAAQyF,IAAI,EAAC,QAAQ;gBAACH,QAAQ,EAAExE,OAAQ;gBAAC+D,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAC5DhE,OAAO,GAAG,eAAe,GAAG,OAAO,eACpCd,OAAA,CAACZ,UAAU;kBAAC+F,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAEPlF,OAAA;cAAMuF,QAAQ,EAAElE,YAAY,CAACmE,YAAY,CAACrC,UAAU,CAAE;cAAC0B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAC1E9E,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9E,OAAA;kBAAA8E,QAAA,EAAO;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1BlF,OAAA;kBACEyF,IAAI,EAAC,MAAM;kBAAA,GACPpE,YAAY,CAACf,QAAQ,CAAC,WAAW,EAAE;oBAAEoF,QAAQ,EAAE;kBAAyB,CAAC,CAAC;kBAC9EC,WAAW,EAAC;gBAAuB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,EACD7D,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACG,SAAS,iBACtChG,OAAA;kBAAM6E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEzD,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACG,SAAS,CAACjC;gBAAO;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAChF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlF,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9E,OAAA;kBAAA8E,QAAA,EAAO;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzBlF,OAAA;kBACEyF,IAAI,EAAC,MAAM;kBAAA,GACPpE,YAAY,CAACf,QAAQ,CAAC,UAAU,EAAE;oBAAEoF,QAAQ,EAAE;kBAAwB,CAAC,CAAC;kBAC5EC,WAAW,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACD7D,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACI,QAAQ,iBACrCjG,OAAA;kBAAM6E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEzD,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACI,QAAQ,CAAClC;gBAAO;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlF,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9E,OAAA;kBAAA8E,QAAA,EAAO;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrBlF,OAAA;kBACEyF,IAAI,EAAC,OAAO;kBAAA,GACRpE,YAAY,CAACf,QAAQ,CAAC,OAAO,EAAE;oBAAEoF,QAAQ,EAAE;kBAAoB,CAAC,CAAC;kBACrEC,WAAW,EAAC;gBAAkB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,EACD7D,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACC,KAAK,iBAClC9F,OAAA;kBAAM6E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEzD,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC/B;gBAAO;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC5E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlF,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9E,OAAA;kBAAA8E,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxBlF,OAAA;kBACEyF,IAAI,EAAC,UAAU;kBAAA,GACXpE,YAAY,CAACf,QAAQ,CAAC,UAAU,EAAE;oBACpCoF,QAAQ,EAAE,sBAAsB;oBAChCQ,SAAS,EAAE;sBAAEC,KAAK,EAAE,CAAC;sBAAEpC,OAAO,EAAE;oBAAyC;kBAC3E,CAAC,CAAC;kBACF4B,WAAW,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,EACD7D,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACE,QAAQ,iBACrC/F,OAAA;kBAAM6E,SAAS,EAAC,OAAO;kBAAAC,QAAA,EAAEzD,YAAY,CAACuE,SAAS,CAACC,MAAM,CAACE,QAAQ,CAAChC;gBAAO;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAC/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlF,OAAA;gBAAQyF,IAAI,EAAC,QAAQ;gBAACH,QAAQ,EAAExE,OAAQ;gBAAC+D,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAC5DhE,OAAO,GAAG,qBAAqB,GAAG,gBAAgB,eACnDd,OAAA,CAACZ,UAAU;kBAAC+F,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlF,OAAA;MAAQ6E,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAChC9E,OAAA;QAAA8E,QAAA,EAAG;MAAgE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvElF,OAAA;QAAA8E,QAAA,GAAG,eAAa,eAAA9E,OAAA;UAAA8E,QAAA,EAAM;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChF,EAAA,CAnWID,OAAO;EAAA,QAC0DtB,OAAO,EAC3DC,WAAW,EAOVC,OAAO,EACJA,OAAO;AAAA;AAAAuH,EAAA,GAVxBnG,OAAO;AAqWb,eAAeA,OAAO;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}