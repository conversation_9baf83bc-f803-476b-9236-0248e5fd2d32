<div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_9i"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_zg"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_hw"></div></span></div></div>

<h1 id="overview">Ad Account Saved Audiences</h1>

<h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>saved audiences</p>
</div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_18"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_wJ">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_so">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_TF">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_cY">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_UV">iOS SDK</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=%7Bad-account-id%7D%2Fsaved_audiences&amp;version=v23.0" target="_blank">Graph API Explorer</a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_b_Dn" style=""><code><span class="pln">GET </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/{</span><span class="pln">ad</span><span class="pun">-</span><span class="pln">account</span><span class="pun">-</span><span class="pln">id</span><span class="pun">}/</span><span class="pln">saved_audiences HTTP</span><span class="pun">/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_g_iq"><tr class="row_0"><td><div class="_yc"><span><code>business_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>optional param assist with filters such as recently used</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>fields</code></span></div><div class="_yb">list&lt;string&gt;</div></td><td><p class="_yd"></p><div><div><p>Fields to be retrieved. Default behavior is to return only the ids.</p>
</div></div><p></p></td></tr><tr class="row_2 _5m27"><td><div class="_yc"><span><code>filtering</code></span></div><div class="_yb">list&lt;Filter Object&gt;</div></td><td><p class="_yd"></p><div><div><p>Filters on the report data. This parameter is an array of filter objects.</p>
</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id="fields">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class="_3hux"><p>{
    "<code>data</code>": [],
    "<code>paging</code>": {}
}</p>
</pre><div class="_3-8o"><h4><code>data</code></h4>A list of <a target="_blank" href="/docs/marketing-api/reference/saved-audience/">SavedAudience</a> nodes.</div><div class="_3-8o"><h4><code>paging</code></h4>For more details about pagination, see the <a href="/docs/graph-api/using-graph-api/#paging">Graph API guide</a>.</div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr></tbody></table></div></div></div>

<h2 id="Creating">Creating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div>

<h2 id="Deleting">Deleting</h2><div class="_844_">You can't perform this operation on this endpoint.</div>