{"title": "Facebook Marketing API - Ad Account Subscribed Apps Reference", "summary": "Documentation for managing app subscriptions to Facebook ad accounts through the Marketing API. Covers reading subscribed apps, subscribing new apps, and unsubscribing apps from ad accounts.", "content": "# Ad Account Subscribed Apps\n\n*Graph API Version: v23.0*\n\n## Overview\n\nThis endpoint allows you to manage app subscriptions to Facebook ad accounts. You can retrieve a list of apps currently subscribed to an ad account, subscribe new apps, or unsubscribe existing apps.\n\n## Reading Subscribed Apps\n\nGet a list of apps subscribed to the ad account.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/subscribed_apps\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Response Format\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Response Fields\n- **data**: A list of AdAccountSubscribedApps nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Example Request\n```http\nGET /v23.0/{ad-account-id}/subscribed_apps HTTP/1.1\nHost: graph.facebook.com\n```\n\n## Subscribing Apps (Creating)\n\nYou cannot create new subscriptions through this endpoint directly.\n\n## Updating Subscriptions\n\nSubscribe an app to an ad account by making a POST request.\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/subscribed_apps\n```\n\n### Parameters\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `app_id` | string | The ID of the app to be subscribed to the ad account |\n\n### Return Type\n```json\n{\n  \"success\": bool\n}\n```\n\nThis endpoint supports read-after-write functionality.\n\n## Unsubscribing Apps (Deleting)\n\nRemove an app subscription from an ad account by making a DELETE request.\n\n### Endpoint\n```\nDELETE /act_{ad_account_id}/subscribed_apps\n```\n\n### Parameters\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `app_id` | string | The ID of the app to be unsubscribed from the ad account |\n\n### Return Type\n```json\n{\n  \"success\": bool\n}\n```\n\n## Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 200 | Permissions error |\n\n## Additional Resources\n\n- [Using Graph API Guide](https://developers.facebook.com/docs/graph-api/using-graph-api/)\n- [Graph API Pagination](https://developers.facebook.com/docs/graph-api/using-graph-api/#paging)\n- [Application Reference](https://developers.facebook.com/docs/graph-api/reference/application/)\n- [AdAccount Reference](https://developers.facebook.com/docs/marketing-api/reference/ad-account/)", "keyPoints": ["Endpoint allows reading, subscribing, and unsubscribing apps from ad accounts", "GET requests return a list of currently subscribed apps with pagination support", "POST requests can subscribe new apps using the app_id parameter", "DELETE requests can unsubscribe apps using the app_id parameter", "All operations return a success boolean and support standard error codes"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/subscribed_apps", "POST /act_{ad_account_id}/subscribed_apps", "DELETE /act_{ad_account_id}/subscribed_apps"], "parameters": ["app_id (string) - ID of app to subscribe/unsubscribe", "ad-account-id - Target ad account identifier", "data - Array of AdAccountSubscribedApps nodes", "paging - Pagination information object", "success - Boolean return value for operations"], "examples": ["GET /v23.0/{ad-account-id}/subscribed_apps HTTP/1.1", "Response format: {\"data\": [], \"paging\": {}}", "Return type: {\"success\": bool}"], "tags": ["Facebook Marketing API", "Ad Account", "App Subscriptions", "Graph API", "REST API", "CRUD Operations"], "relatedTopics": ["Graph API Usage", "Application Reference", "AdAccount Reference", "API Pagination", "Read-after-write functionality", "Error handling"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/subscribed_apps/", "processedAt": "2025-06-25T15:39:43.962Z", "processor": "openrouter-claude-sonnet-4"}