# Facebook Marketing API - Ad Account Reference

## Summary
Complete reference documentation for the Facebook Marketing API Ad Account object, including fields, edges, CRUD operations, and usage examples. Ad Accounts represent business entities that create and manage ads on Facebook.

## Key Points
- Ad Accounts represent business entities that create and manage Facebook ads
- Account limits vary between regular (6,000 ads) and bulk accounts (50,000 ads)
- iOS 14.5 changes require Admin privileges for agency_client_declaration field
- Ad volume tracking helps manage ads running or in review against page limits
- DSA (Digital Services Act) fields can be set for EU compliance requirements

## API Endpoints
- `GET /act_{ad_account_id}`
- `POST /act_{ad_account_id}`
- `GET /act_{ad_account_id}/ads_volume`
- `GET /act_{ad_account_id}/users`
- `POST /{business_id}/adaccount`
- `POST /act_{ad_account_id}/assigned_users`

## Parameters
- account_id
- account_status
- currency
- name
- spend_cap
- timezone_id
- end_advertiser
- media_agency
- partner
- default_dsa_payor
- default_dsa_beneficiary
- agency_client_declaration

## Content
# Facebook Marketing API - Ad Account Reference

## Overview

Represents a business, person or other entity who creates and manages ads on Facebook. Multiple people can manage an account, and each person can have one or more levels of access to an account.

## Important Updates

### iOS 14.5 Changes
In response to Apple's new policy, breaking changes affect SDKAdNetwork, Marketing API and Ads Insights API endpoints. The `agency_client_declaration` field requires Admin privileges for all operations starting with v10.0.

## Ad Volume Management

You can view the volume of ads running or in review for your ad accounts. These ads count against the ads limit per page.

### Querying Ad Volume
```bash
curl -G \
  -d "access_token=<access_token>" \
  "https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume"
```

Response:
```json
{"data":[{"ads_running_or_in_review_count":2}]}
```

### Running or In Review Status
An ad is considered running or in review when:
- `effective_status` is `1` (active)
- `configured_status` is `active` and `effective_status` is `9` (pending review) or `17` (pending processing)
- Ad account status is `1` (active), `8` (pending settlement), or `9` (in grace period)
- Ad set schedule allows current execution

## Account Limits

| Limit | Regular Account | Bulk Account |
|-------|----------------|-------------|
| Maximum ads | 6,000 | 50,000 |
| Maximum ad sets | 6,000 | 10,000 |
| Maximum campaigns | 6,000 | 10,000 |
| Maximum archived items | 100,000 each type | 100,000 each type |
| Maximum people with access | 25 | 25 |
| Maximum accounts per person | 25 | 25 |

## Reading Ad Accounts

### Basic Read
```bash
curl -G \
  -d 'fields=name,account_status,currency' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>
```

### Finding Users with Access
```bash
curl -G \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/users
```

### Digital Services Act Information
```bash
curl -X GET \
"https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>?fields=default_dsa_payor%2Cdefault_dsa_beneficiary&access_token=<ACCESS_TOKEN>"
```

## Key Fields

### Core Fields
- `id`: The string `act_{ad_account_id}`
- `account_id`: The ID of the Ad Account
- `account_status`: Status code (1=ACTIVE, 2=DISABLED, etc.)
- `name`: Name of the account
- `currency`: Currency used for the account
- `timezone_id`: Timezone ID
- `business`: Associated Business Manager

### Financial Fields
- `amount_spent`: Current amount spent
- `balance`: Bill amount due
- `spend_cap`: Maximum spending limit
- `funding_source`: Payment method ID
- `funding_source_details`: Detailed payment information

### Business Fields
- `business_name`: Business name
- `business_city`, `business_state`, `business_country_code`: Business address
- `end_advertiser`: Entity the ads target
- `media_agency`: Advertising agency
- `partner`: Advertising partner

## Creating Ad Accounts

```bash
curl \
-F "name=MyAdAccount" \
-F "currency=USD" \
-F "timezone_id=1" \
-F "end_advertiser=<END_ADVERTISER_ID>" \
-F "media_agency=<MEDIA_AGENCY_ID>" \
-F "partner=NONE" \
-F "access_token=<ACCESS_TOKEN>" \
"https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount"
```

### Required Parameters
- `name`: Account name
- `currency`: ISO 4217 currency code
- `timezone_id`: Timezone identifier
- `end_advertiser`: Target entity (Page ID, App ID, or NONE/UNFOUND)
- `media_agency`: Agency identifier
- `partner`: Partner identifier

## Updating Ad Accounts

### Update Basic Information
```bash
curl -X POST \
  -F 'name="Updated Account Name"' \
  -F 'spend_cap=1000.00' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>
```

### Update DSA Information
```bash
curl -X POST \
  -F 'default_dsa_payor="payor_info"' \
  -F 'default_dsa_beneficiary="beneficiary_info"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>
```

## Important Edges

- `/activities`: Account activities
- `/adcreatives`: Ad creatives
- `/ads`: Ads in the account
- `/adsets`: Ad sets
- `/campaigns`: Campaigns
- `/customaudiences`: Custom audiences
- `/customconversions`: Custom conversions
- `/insights`: Performance insights
- `/users`: Users with access

## Error Codes

Common error codes:
- `100`: Invalid parameter
- `190`: Invalid OAuth 2.0 Access Token
- `200`: Permissions error
- `613`: Rate limit exceeded
- `80004`: Too many calls to ad account

## Best Practices

1. **Rate Limiting**: Respect API rate limits to avoid throttling
2. **Permissions**: Ensure proper access levels for operations
3. **Field Selection**: Request only needed fields to optimize performance
4. **Error Handling**: Implement robust error handling for API calls
5. **Spend Caps**: Monitor and manage spend caps appropriately

## Examples
curl -G -d 'access_token=<access_token>' 'https://graph.facebook.com/<API_VERSION>/act_<ad_account_ID>/ads_volume'

curl -F 'name=MyAdAccount' -F 'currency=USD' -F 'timezone_id=1' 'https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/adaccount'

curl -X GET 'https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>?fields=default_dsa_payor%2Cdefault_dsa_beneficiary'

---
**Tags:** Facebook Marketing API, Ad Account, CRUD Operations, Ad Volume, Business Manager, iOS 14.5, DSA Compliance  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account  
**Processed:** 2025-06-25T16:18:47.157Z