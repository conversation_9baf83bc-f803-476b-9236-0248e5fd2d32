{"title": "Facebook Marketing API - Product Catalog Reference", "summary": "Complete reference documentation for the Product Catalog API endpoint in Facebook's Marketing API. Covers reading, creating, updating, and deleting product catalogs used for dynamic ads, including all available fields, parameters, and edge connections.", "content": "# Product Catalog\n\nRepresents a catalog for your business you can use to deliver ads with [dynamic ads](/docs/marketing-api/dynamic-ad).\n\n## Overview\n\nProduct catalogs contain a list of items like products, hotels or flights, and the information needed to display them in dynamic ads. You can associate pixels and apps with a product catalog and then display products in ads based on signals from pixels or apps.\n\n## Permissions\n\nYou need the appropriate [Marketing API Access Level](/docs/marketing-api/access#limits) and must accept the [Terms of Service](https://business.facebook.com/legal/product_catalog_terms/) by creating your first catalog through [Business Manager](https://business.facebook.com/).\n\n## Reading Product Catalogs\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `segment_use_cases` | array<enum> | Available values: AFFILIATE_SELLER_STOREFRONT, AFFILIATE_TAGGED_ONLY_DEPRECATED, COLLAB_ADS, COLLAB_ADS_FOR_MARKETPLACE_PARTNER, COLLAB_ADS_SEGMENT_WITHOUT_SEGMENT_SYNCING, DIGITAL_CIRCULARS, FB_LIVE_SHOPPING, IG_SHOPPING, IG_SHOPPING_SUGGESTED_PRODUCTS, MARKETPLACE_SHOPS, TEST |\n\n### Fields\n\n| Field | Type | Description |\n|-------|------|--------------|\n| `id` | numeric string | ID of a catalog (Default) |\n| `business` | Business | Business that owns a catalog |\n| `da_display_settings` | ProductCatalogImageSettings | Image display settings for Dynamic Ad formats |\n| `default_image_url` | string | URL for default image used when product images are unavailable |\n| `fallback_image_url` | list<string> | URL for fallback image used for auto-generated dynamic items |\n| `feed_count` | int32 | Total number of feeds used by catalog |\n| `is_catalog_segment` | bool | Indicates if this is a catalog segment |\n| `is_local_catalog` | bool | Indicates if this is a local catalog |\n| `name` | string | Name of the catalog (Default) |\n| `product_count` | int32 | Total number of products in catalog |\n| `vertical` | enum | Type of catalog (hotels, commerce, etc.) |\n\n### Available Edges\n\n- `agencies` - Agencies with catalog access\n- `assigned_users` - Users assigned to catalog\n- `automotive_models` - Automotive models in catalog\n- `categories` - Product categories\n- `external_event_sources` - Associated pixels and apps\n- `products` - Products in catalog\n- `product_sets` - Product sets in catalog\n- `hotels` - Hotels in catalog\n- `flights` - Flights in catalog\n- `vehicles` - Vehicles in catalog\n- And many more...\n\n## Creating Product Catalogs\n\n### Basic Catalog Creation\n\n```bash\ncurl \\\n  -F 'name=Catalog' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/product_catalogs\n```\n\n### Parameters for Creation\n\n| Parameter | Type | Required | Description |\n|-----------|------|----------|-------------|\n| `name` | string | Yes | Name of the catalog |\n| `vertical` | enum | No | Catalog vertical (default: commerce) |\n| `additional_vertical_option` | enum | No | Additional catalog configurations |\n| `catalog_segment_filter` | JSON rule | No | Filter for creating catalog segment |\n| `da_display_settings` | Object | No | Dynamic Ads display settings |\n\n### Batch Operations\n\nYou can perform batch operations on catalog items:\n\n```bash\ncurl \\\n  -F 'requests=[{\"method\":\"CREATE\",\"data\":{...}}]' \\\n  -F 'item_type=product' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>/items_batch\n```\n\n## Updating Product Catalogs\n\n### Available Update Parameters\n\n| Parameter | Type | Description |\n|-----------|------|--------------|\n| `name` | string | Update catalog name |\n| `da_display_settings` | Object | Update display settings |\n| `default_image_url` | URI | Update default image URL |\n| `fallback_image_url` | URI | Update fallback image URL |\n\n## Deleting Product Catalogs\n\n```bash\ncurl -X DELETE \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>\n```\n\n### Delete Parameters\n\n| Parameter | Type | Default | Description |\n|-----------|------|---------|-------------|\n| `allow_delete_catalog_with_live_product_set` | boolean | false | Allow deletion even with live product sets |\n\n## Managing External Event Sources\n\n### Associate Pixels/Apps\n\n```bash\ncurl \\\n  -F 'external_event_sources=[<PIXEL_ID>,<APP_ID>]' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/<VERSION>/<PRODUCT_CATALOG_ID>/external_event_sources\n```\n\n### Remove Association\n\n```bash\ncurl -X DELETE \\\n  -F 'external_event_sources=[<APP_ID>,<PIXEL_ID>]' \\\n  -F 'access_token=<TOKEN>' \\\n  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>/external_event_sources\n```\n\n## Error Codes\n\n| Code | Description |\n|------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | Action deemed abusive or disallowed |\n| 80009 | Too many calls to catalog account |\n| 80014 | Too many batch upload calls |\n\n## User Management\n\n### Assign Users\n\n```bash\ncurl \\\n  -F 'user=<USER_ID>' \\\n  -F 'tasks=[\"MANAGE\",\"ADVERTISE\"]' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>/assigned_users\n```\n\n### Available Tasks\n\n- `MANAGE` - Full catalog management\n- `ADVERTISE` - Create ads from catalog\n- `MANAGE_AR` - Manage AR features\n- `AA_ANALYZE` - Analytics access", "keyPoints": ["Product catalogs are essential for dynamic ads and contain products, hotels, flights, or other items", "Catalogs can be associated with pixels and apps to track user interactions and optimize ad delivery", "Batch operations are supported for efficient bulk management of catalog items", "Multiple verticals are supported including commerce, hotels, flights, and vehicles", "Proper permissions and Marketing API access levels are required for catalog operations"], "apiEndpoints": ["GET /{business_id}/owned_product_catalogs", "POST /{business_id}/product_catalogs", "GET/POST/DELETE /{product_catalog_id}", "POST /{product_catalog_id}/items_batch", "GET/POST/DELETE /{product_catalog_id}/external_event_sources", "POST /{product_catalog_id}/assigned_users", "POST /{product_catalog_id}/vehicles"], "parameters": ["name", "vertical", "segment_use_cases", "da_display_settings", "external_event_sources", "item_type", "requests", "allow_upsert", "tasks", "user", "default_image_url", "fallback_image_url"], "examples": ["curl -G -d \"access_token=<ACCESS_TOKEN>\" \"https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/owned_product_catalogs\"", "curl -F 'name=Catalog' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/<API_VERSION>/<BUSINESS_ID>/product_catalogs", "curl -F 'external_event_sources=[<PIXEL_ID>,<APP_ID>]' -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/<VERSION>/<PRODUCT_CATALOG_ID>/external_event_sources", "curl -X DELETE -F 'access_token=<ACCESS_TOKEN>' https://graph.facebook.com/<API_VERSION>/<PRODUCT_CATALOG_ID>"], "tags": ["Facebook Marketing API", "Product Catalog", "Dynamic Ads", "E-commerce", "API Reference", "CRUD Operations", "Batch Operations"], "relatedTopics": ["Dynamic Ads", "Business Manager", "Marketing API Access Levels", "Product Sets", "External Event Sources", "Pixels", "Product Feeds", "Catalog Segments"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/product-catalog", "processedAt": "2025-06-25T15:47:27.635Z", "processor": "openrouter-claude-sonnet-4"}