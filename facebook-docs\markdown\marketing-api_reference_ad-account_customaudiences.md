# Ad Account Customaudiences

On This Page

[Ad Account Customaudiences](#overview)

[Reading](#Reading)

[Example](#example)

[Parameters](#parameters)

[Fields](#fields)

[Error Codes](#error-codes)

[Creating](#Creating)

[Example](#example-2)

[Parameters](#parameters-2)

[Return Type](#return-type)

[Error Codes](#error-codes-2)

[Updating](#Updating)

[Deleting](#Deleting)

Graph API Version

[v23.0](#)

# Ad Account Customaudiences

[](#)

## Reading

The custom audiences associated with the ad account.

  
**Note:** To retrieve the IDs of lookalike audiences based on your custom audiences, use the `lookalike_audience_ids` field. See [Lookalike Audiences - Managing Audiences](/docs/marketing-api/audiences/guides/lookalike-audiences#read) for more information.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fcustomaudiences%3Ffields%3Did&version=v23.0)

```
`GET /v23.0/act_<AD_ACCOUNT_ID>/customaudiences?fields=id HTTP/1.1
Host: graph.facebook.com`
```
```
`/* PHP SDK v5.0.0 */
/* make the API call */
try {
  // Returns a `Facebook\FacebookResponse` object
  $response = $fb->get(
    '/act_<AD_ACCOUNT_ID>/customaudiences?fields=id',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
/* handle the result */`
```
```
`/* make the API call */
FB.api(
    "/act_<AD_ACCOUNT_ID>/customaudiences",
    {
        "fields": "id"
    },
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);`
```
```
`Bundle params = new Bundle();
params.putString("fields", "id");
/* make the API call */
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/act_<AD_ACCOUNT_ID>/customaudiences",
    params,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();`
```
```
`NSDictionary *params = @{
  @"fields": @"id",
};
/* make the API call */
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/act_<AD_ACCOUNT_ID>/customaudiences"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];`
```
```
`curl -X GET -G \
  -d 'fields="id"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`business_id`

numeric string or integer

Optional.  
This param assists with filters, such as recently used.

`fetch_primary_audience`

boolean

Default value: `false`

fetch\_primary\_audience

`fields`

list<string>

Fields to be retrieved. Default behavior is to return only the IDs.

`filtering`

list<Filter Object>

Filters on the report data. This parameter is an array of filter objects.

`field`

string

Required

`operator`

enum {EQUAL, NOT\_EQUAL, GREATER\_THAN, GREATER\_THAN\_OR\_EQUAL, LESS\_THAN, LESS\_THAN\_OR\_EQUAL, IN\_RANGE, NOT\_IN\_RANGE, CONTAIN, NOT\_CONTAIN, CONTAINS\_ANY, NOT\_CONTAINS\_ANY, IN, NOT\_IN, STARTS\_WITH, ENDS\_WITH, ANY, ALL, AFTER, BEFORE, ON\_OR\_AFTER, ON\_OR\_BEFORE, NONE, TOP}

Required

`value`

string

Required

`pixel_id`

numeric string

Optional.  
This param fetches audiences associated to specific pixel.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {}
}


```

#### `data`

A list of [CustomAudience](/docs/marketing-api/reference/custom-audience/) nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

### Error Codes

Error

Description

200

Permissions error

100

Invalid parameter

80003

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#custom-audience.

190

Invalid OAuth 2.0 Access Token

[](#)

## Creating

Your ability to create custom audiences may be limited.

It is expected that you have the same audience capabilities independent of your app's status, which could be _in development_ or _live_.

To create a custom audience you'll first need to create a blank audience. Then, you'll want to add people to the blank audience you just created by updating the [users edge](/docs/marketing-api/reference/custom-audience/users/) of the audience. **You can create a maximum of 500 custom audiences.**

  

You can make a POST request to `customaudiences` edge from the following paths:

*   [`/act_{ad_account_id}/customaudiences`](/docs/marketing-api/reference/ad-account/customaudiences/)

When posting to this edge, a [CustomAudience](/docs/marketing-api/reference/custom-audience/) will be created.

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fcustomaudiences%3Fname%3DMy%2Bnew%2BCustom%2BAudience%26subtype%3DCUSTOM%26description%3DPeople%2Bwho%2Bpurchased%2Bon%2Bmy%2Bwebsite%26customer_file_source%3DUSER_PROVIDED_ONLY&version=v23.0)

```
`POST /v23.0/act_<AD_ACCOUNT_ID>/customaudiences HTTP/1.1
Host: graph.facebook.com

name=My+new+Custom+Audience&subtype=CUSTOM&description=People+who+purchased+on+my+website&customer_file_source=USER_PROVIDED_ONLY`
```
```
`/* PHP SDK v5.0.0 */
/* make the API call */
try {
  // Returns a `Facebook\FacebookResponse` object
  $response = $fb->post(
    '/act_<AD_ACCOUNT_ID>/customaudiences',
    array (
      'name' => 'My new Custom Audience',
      'subtype' => 'CUSTOM',
      'description' => 'People who purchased on my website',
      'customer_file_source' => 'USER_PROVIDED_ONLY',
    ),
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
/* handle the result */`
```
```
`/* make the API call */
FB.api(
    "/act_<AD_ACCOUNT_ID>/customaudiences",
    "POST",
    {
        "name": "My new Custom Audience",
        "subtype": "CUSTOM",
        "description": "People who purchased on my website",
        "customer_file_source": "USER_PROVIDED_ONLY"
    },
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);`
```
```
`Bundle params = new Bundle();
params.putString("name", "My new Custom Audience");
params.putString("subtype", "CUSTOM");
params.putString("description", "People who purchased on my website");
params.putString("customer_file_source", "USER_PROVIDED_ONLY");
/* make the API call */
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/act_<AD_ACCOUNT_ID>/customaudiences",
    params,
    HttpMethod.POST,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();`
```
```
`NSDictionary *params = @{
  @"name": @"My new Custom Audience",
  @"subtype": @"CUSTOM",
  @"description": @"People who purchased on my website",
  @"customer_file_source": @"USER_PROVIDED_ONLY",
};
/* make the API call */
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/act_<AD_ACCOUNT_ID>/customaudiences"
                                      parameters:params
                                      HTTPMethod:@"POST"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];`
```
```
`curl -X POST \
  -F 'name="My new Custom Audience"' \
  -F 'subtype="CUSTOM"' \
  -F 'description="People who purchased on my website"' \
  -F 'customer_file_source="USER_PROVIDED_ONLY"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/customaudiences`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`allowed_domains`

list<string>

A list of domains that the audience is restricted to.

`claim_objective`

enum {AUTOMOTIVE\_MODEL, COLLABORATIVE\_ADS, HOME\_LISTING, MEDIA\_TITLE, PRODUCT, TRAVEL, VEHICLE, VEHICLE\_OFFER}

Specifies the objective of audiences with `CLAIM` subtype.

`content_type`

enum {AUTOMOTIVE\_MODEL, DESTINATION, FLIGHT, GENERIC, HOME\_LISTING, HOTEL, LOCAL\_SERVICE\_BUSINESS, MEDIA\_TITLE, OFFLINE\_PRODUCT, PRODUCT, VEHICLE, VEHICLE\_OFFER}

Specifies a mandatory content type for `TRAVEL` claim objective.

`customer_file_source`

enum {USER\_PROVIDED\_ONLY, PARTNER\_PROVIDED\_ONLY, BOTH\_USER\_AND\_PARTNER\_PROVIDED}

Source of customer information in the uploaded file.

`dataset_id`

numeric string or integer

The offline conversion dataset associated with this audience.

`description`

string

The description for this custom audience

`enable_fetch_or_create`

boolean

If `true`, we fetch a custom audience instead of creating one when an identical custom audience already exists. Identical custom audiences must have same `name`, `claim_objective`, `content_type`, `event_source_group/event_sources/sliced_event_source_group`, inclusions, exclusions and rule.

`event_source_group`

numeric string or integer

Specifies event source group for `TRAVEL` claim objective.

`event_sources`

array<JSON object>

Specifies event sources for `TRAVEL` claim objective.

`id`

int64

id

Required

`type`

enum {APP, OFFLINE\_EVENTS, PAGE, PIXEL}

type

Required

`facebook_page_id`

numeric string or integer

facebook\_page\_id

`is_value_based`

boolean

Whether the audience is used to seed a new value based lookalike audience.

`list_of_accounts`

list<int64>

List of user and page accounts

`lookalike_spec`

JSON-encoded string

The specification for creating a [lookalike audience](/docs/marketing-api/lookalike-audience-targeting/).

`name`

string

The name of this custom audience.

`opt_out_link`

string

Your opt-out URL so people can choose not to be targeted.

`origin_audience_id`

numeric string or integer

The ID of origin Custom Audience.The origin audience you create must have a minimum size of 100.

`pixel_id`

numeric string or integer

The pixel associated with this audience

`prefill`

boolean

You can specify `true` or `false`. `true` includes website traffic recorded prior to the audience creation, and `false` only includes website traffic beginning at the time of the audience creation.

`product_set_id`

numeric string or integer

The Product Set to target with this audience

`retention_days`

int64

Number of days to keep the user in this cluster. You can use any value between `1` and `180` days. Defaults to forever, if not specified.

`rule`

string

Audience rule to be applied on the referrer URL. Used for [website custom audiences](/docs/marketing-api/custom-audience-website/#audiencerules), [product audiences](/docs/marketing-api/dynamic-product-ads/product-audiences/#productaudience), and [video remarketing audiences](/docs/marketing-api/guides/videoads/#remarketing).

`rule_aggregation`

string

Aggregation rule

`subscription_info`

list<enum {WHATSAPP, MESSENGER}>

subscription\_info

`subtype`

enum {CUSTOM, PRIMARY, WEBSITE, APP, OFFLINE\_CONVERSION, CLAIM, MANAGED, PARTNER, VIDEO, LOOKALIKE, ENGAGEMENT, BAG\_OF\_ACCOUNTS, STUDY\_RULE\_AUDIENCE, FOX, MEASUREMENT, REGULATED\_CATEGORIES\_AUDIENCE, BIDDING, EXCLUSION, MESSENGER\_SUBSCRIBER\_LIST}

Type of custom audience, derived from original data source.  
Note: `COMBINATION` subtype is only used by Ads Manager, and is not available through the API.  
  
Number of audiences limit for selected subtype:  
`CUSTOM`: 500  
`LOOKALIKE`: 10000  

`use_for_products`

list<enum {ADS, MARKETING\_MESSAGES}>

use\_for\_products

`use_in_campaigns`

boolean

Default value: `true`

use\_in\_campaigns

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.

Struct {

`id`: numeric string,

`message`: string,

}

### Error Codes

Error

Description

200

Permissions error

2654

Failed to create custom audience

100

Invalid parameter

80003

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#custom-audience.

2663

Terms of service has not been accepted. To accept, go to https://www.facebook.com/customaudiences/app/tos

368

The action attempted has been deemed abusive or is otherwise disallowed

190

Invalid OAuth 2.0 Access Token

2667

Your account permissions don't allow you to create a custom audience for this event source.

[](#)

## Updating

You can't perform this operation on this endpoint.

[](#)

## Deleting

You can't perform this operation on this endpoint.

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '***************'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)