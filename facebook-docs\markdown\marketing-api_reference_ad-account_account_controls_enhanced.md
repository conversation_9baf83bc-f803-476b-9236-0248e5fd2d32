# Facebook Marketing API - Ad Account Account Controls Reference

## Summary
Complete reference for managing Ad Account Account Controls through the Facebook Marketing API, including reading, creating, and updating business constraints for ad accounts. This endpoint allows you to configure audience controls, placement controls, and other account-level restrictions.

## Key Points
- Manages business constraints and controls for Facebook ad accounts
- Supports reading current account controls and creating new restrictions
- Allows configuration of audience controls, placement exclusions, and geographic targeting
- Placement exclusions are limited to specific predefined options
- Deletion operations are not supported - only reading, creating, and updating

## API Endpoints
- `GET /v23.0/{ad-account-id}/account_controls`
- `POST /act_{ad_account_id}/account_controls`

## Parameters
- audience_controls
- age_min
- geo_locations
- excluded_geo_locations
- exclusions
- placement_controls
- placement_exclusions
- campaign_ids_to_set_ap

## Content
# Ad Account Account Controls

The Ad Account Account Controls endpoint allows you to manage business constraints and controls for Facebook ad accounts through the Marketing API.

## Reading Account Controls

Get default fields on an AdAccountBusinessConstraints node associated with an AdAccount. This returns information about current account-level restrictions and controls.

### Endpoint
```
GET /v23.0/{ad-account-id}/account_controls
```

### Example Request
```bash
GET /v23.0/{ad-account-id}/account_controls HTTP/1.1
Host: graph.facebook.com
```

### PHP SDK Example
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/account_controls',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

### JavaScript SDK Example
```javascript
FB.api(
    "/{ad-account-id}/account_controls",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Response Format
```json
{
    "data": [],
    "paging": {}
}
```

#### Response Fields
- **data**: A list of AdAccountBusinessConstraints nodes
- **paging**: Pagination information for large result sets

### Reading Error Codes
| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 200 | Permissions error |
| 190 | Invalid OAuth 2.0 Access Token |

## Creating Account Controls

Create new business constraints for an ad account by making a POST request to the account_controls edge.

### Endpoint
```
POST /act_{ad_account_id}/account_controls
```

### Parameters

#### Required Parameters
- **audience_controls** (JSON/object): Audience targeting controls
  - **age_min** (int64): Minimum age for audience targeting
  - **geo_locations** (JSON/object): Geographic location targeting
  - **excluded_geo_locations** (JSON/object): Geographic locations to exclude
  - **exclusions** (JSON/object): Additional audience exclusions

#### Optional Parameters
- **placement_controls** (JSON/object): Controls for ad placement
  - **placement_exclusions** (array): List of placements to exclude
    - Allowed values:
      - `AUDIENCE_NETWORK_CLASSIC`: Native, banner & interstitial positions
      - `AUDIENCE_NETWORK_REWARDED_VIDEO`: Rewarded videos
      - `AUDIENCE_NETWORK_INSTREAM_VIDEO`: Instream videos
      - `FACEBOOK_MARKETPLACE`: Marketplace section
      - `FACEBOOK_RIGHT_HAND_COLUMN`: Right hand column

- **campaign_ids_to_set_ap** (array<numeric string>): Campaign IDs to apply controls to

### Placement Controls Example
```json
{
  "placement_controls": {
    "placement_exclusions": ["audience_network_rewarded_video"]
  }
}
```

### Return Type
```json
{
  "id": "string",
  "success": "bool",
  "error_code": "string",
  "error_message": "string"
}
```

### Creating Error Codes
| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 200 | Permissions error |
| 2641 | Ad includes or excludes locations that are currently restricted |

## Updating Account Controls

To update existing account controls, use the POST endpoint with new parameter values. The system will update the existing AdAccountBusinessConstraints associated with the AdAccount.

## Deleting Account Controls

Deletion operations are not supported for this endpoint. Account controls must be updated rather than deleted.

## Important Notes

- Account controls apply business-level constraints to ad accounts
- Placement exclusions only support specific predefined placements
- Geographic restrictions may apply based on current platform policies
- All changes require appropriate permissions for the ad account

## Examples
GET request for reading account controls

PHP SDK implementation

JavaScript SDK implementation

Placement exclusions configuration

Response format examples

---
**Tags:** Facebook Marketing API, Ad Account, Business Constraints, Account Controls, Audience Targeting, Placement Controls, API Reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/account_controls/  
**Processed:** 2025-06-25T16:19:16.244Z