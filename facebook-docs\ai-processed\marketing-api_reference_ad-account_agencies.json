{"title": "Facebook Marketing API - Ad Account Agencies Reference", "summary": "This reference documentation covers the Ad Account Agencies endpoint in the Facebook Marketing API, which allows you to read and manage agencies associated with ad accounts. The endpoint supports reading agency relationships and deleting agency associations, but does not support creating or updating operations.", "content": "# Ad Account Agencies\n\nThe Ad Account Agencies endpoint allows you to manage agencies associated with ad accounts in the Facebook Marketing API.\n\n## Reading\n\nRetrieves agencies associated with ad accounts.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/agencies\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Response Format\nReturns a JSON formatted result:\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Data Fields\nA list of Business nodes with the following additional fields:\n\n- **access_requested_time** (datetime): The creation time of the access request [Default]\n- **access_status** (enum): The status of the access request [Default]\n- **access_updated_time** (datetime): The update time of the access request [Default]\n- **permitted_tasks** (list<string>): The permissions of tasks associated with the access request [Default]\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/agencies HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/agencies',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/agencies\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Error Codes\n- **200**: Permissions error\n- **100**: Invalid parameter\n- **190**: Invalid OAuth 2.0 Access Token\n- **80004**: Too many calls to this ad-account. Rate limiting applied.\n\n## Creating\nYou can't perform this operation on this endpoint.\n\n## Updating\nYou can't perform this operation on this endpoint.\n\n## Deleting\n\nYou can dissociate a Business from an AdAccount by making a DELETE request.\n\n### Endpoint\n```\nDELETE /act_{ad_account_id}/agencies\n```\n\n### Parameters\n- **business** (numeric string, required): Business ID to dissociate\n\n### Return Type\n```json\n{\n  \"success\": bool\n}\n```\n\n### Error Codes\n- **100**: Invalid parameter\n- **200**: Permissions error", "keyPoints": ["The endpoint only supports reading and deleting operations, not creating or updating", "Returns Business nodes with additional access-related fields like access_status and permitted_tasks", "Requires proper OAuth 2.0 access token and is subject to rate limiting", "DELETE operation allows dissociating agencies from ad accounts", "Response includes pagination support for large result sets"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/agencies", "DELETE /act_{ad_account_id}/agencies"], "parameters": ["ad-account-id", "business", "access_requested_time", "access_status", "access_updated_time", "permitted_tasks"], "examples": ["HTTP GET request example", "PHP SDK implementation", "JavaScript SDK usage", "Android SDK example", "iOS SDK example"], "tags": ["Facebook Marketing API", "Ad Account", "Agencies", "Business", "Graph API", "REST API"], "relatedTopics": ["Business object reference", "Ad Account reference", "Graph API pagination", "OAuth 2.0 authentication", "Rate limiting", "Error handling"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/agencies/", "processedAt": "2025-06-25T16:23:28.424Z", "processor": "openrouter-claude-sonnet-4"}