# Facebook Marketing API - Ad Creative

## Summary
Comprehensive guide to creating and managing ad creatives in the Facebook Marketing API. Covers creative objects, placements, previews, and the complete workflow for building visual ad content.

## Key Points
- Ad creatives contain all visual rendering data for Facebook ads
- Page post ads require an object_story_id field referencing a page post
- Ad creatives have three components: creative object, placement, and preview
- Placements determine where ads appear (mobile feed, desktop feed, right column)
- Ad previews can be generated by ad ID, creative ID, or creative spec

## API Endpoints
- `POST /act_{AD_ACCOUNT_ID}/adcreatives`
- `POST /act_{AD_ACCOUNT_ID}/ads`
- `GET /{CREATIVE_ID}`
- `POST /act_{AD_ACCOUNT_ID}/adsets`
- `GET /act_{AD_ACCOUNT_ID}/generatepreviews`

## Parameters
- name
- object_story_id
- creative_id
- adset_id
- status
- fields
- campaign_id
- daily_budget
- targeting
- optimization_goal
- billing_event
- bid_amount
- creative
- ad_format
- geo_locations
- publisher_platforms

## Content
# Ad Creative

Use Facebook ads with your existing customers and to reach new ones. Each guide describes Facebook ads products to help meet your advertising goals. There are several types of ad units with a variety of appearances, placement and creative options. For guidelines on ads units as creative content, see [Facebook Ads Guide](https://www.facebook.com/business/ads-guide/?tab0=Mobile%20News%20Feed).

## Creative

An ad creative is an object that contains all the data for visually rendering the ad itself. In the API, there are different types of ads that you can create on Facebook, all listed [here](/docs/reference/ads-api/adcreative#overview).

If you have a [campaign](/docs/marketing-api/reference/ad-campaign-group) with the Page Post Engagement Objective, you can now create an ad that promotes a post made by the page. This is considered a Page post ad. Page post ads require a field called `object_story_id`, which is the `id` property of a Page post. Learn more about [Ad Creative, Reference](/docs/reference/ads-api/adcreative#create).

An ad creative has three parts:

- Ad creative itself, defined by the visual attributes of the creative object
- Placement that the ad runs on
- Preview of the unit itself, per placement

### Creating an Ad Creative

To create the ad creative object, make the following call:

```bash
curl -X POST \
  -F 'name="Sample Promoted Post"' \
  -F 'object_story_id="<PAGE_ID>_<POST_ID>"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives
```

The response to the API call is the `id` of the creative object. Store this; you need it for the ad object:

```bash
curl -X POST \
  -F 'name="My Ad"' \
  -F 'adset_id="<AD_SET_ID>"' \
  -F 'creative={
       "creative_id": "<CREATIVE_ID>"
     }' \
  -F 'status="PAUSED"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads
```

### Limits

There are limits on the creative's text, image size, image aspect ratio and other aspects of the creative. See the [Ads Guide](https://www.facebook.com/business/ads-guide).

### Read

In the Ads API, each field you want to retrieve needs to be asked for explicitly, except for `id`. Each object's [Reference](/docs/reference/ads-api/adcreative/#read) has a section for reading back the object and lists what fields are readable. For the creative, it's the same fields as specified when creating the object, and `id`.

```bash
curl -G \
  -d 'fields=name,object_story_id' \
  -d 'access_token=<ACCESS_TOKEN>' \
https://graph.facebook.com/v23.0/<CREATIVE_ID>
```

## Placements

A placement is where your ad is shown on Facebook, such as on Feed on desktop, Feed on a mobile device or on the right column. See [Ads Product Guide](https://www.facebook.com/business/ads-guide/).

We encourage you to run ads across the full range of available placements. Facebook's ad auction is designed to deliver ad impressions to the placement most likely to drive campaign results at the lowest possible cost.

The easiest way to take advantage of this optimization is to leave this field blank. You can also select specific placements in an ad set's target_spec.

This example has a page post ad. The available placements are Mobile Feed, Desktop Feed and Right column of Facebook. In the API, see [Placement Options](/docs/reference/ads-api/targeting-specs/#placement). If you choose `desktopfeed` and `rightcolumn` as the `page_type`, the ad runs on Desktop Feed and Right column placements. Any ad created below this ad set has only the desktop placement.

```bash
curl -X POST \
  -F 'name=Desktop Ad Set' \
  -F 'campaign_id=<CAMPAIGN_ID>' \
  -F 'daily_budget=10000' \
  -F 'targeting={ 
    "geo_locations": {"countries":["US"]}, 
    "publisher_platforms": ["facebook","audience_network"] 
  }' \
  -F 'optimization_goal=LINK_CLICKS' \
  -F 'billing_event=IMPRESSIONS' \
  -F 'bid_amount=1000' \
  -F 'status=PAUSED' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets
```

## Preview an Ad

You preview an ad in one of two ways—with [ad preview API](/docs/reference/ads-api/generatepreview/) or the [ad preview plugin](/docs/reference/ads-api/ad-preview-plugin).

There are three ways to generate a preview with the API:

1. By ad ID
2. By ad creative ID
3. By supplying a creative spec

Following the [reference](/docs/reference/ads-api/generatepreview/#html) docs for the preview API, the minimum required API call is:

```bash
curl -G \
  --data-urlencode 'creative="<CREATIVE_SPEC>"' \
  -d 'ad_format="<AD_FORMAT>"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

The creative spec is an array of each field and value required to create the ad creative.

Take `object_story_id` and use it in the preview API call:

```bash
curl -G \
  -d 'creative={"object_story_id":"<PAGE_ID>_<POST_ID>"}' \
  -d 'ad_format=<AD_FORMAT>' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

The available values for `ad_format` differ a bit from `page_types`. For Desktop Feed and Right column placements, you need to make two API calls:

```bash
curl -G \
  -d 'creative={"object_story_id":"<PAGE_ID>_<POST_ID>"}' \
  -d 'ad_format=DESKTOP_FEED_STANDARD' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

```bash
curl -G \
  -d 'creative={"object_story_id":"<PAGE_ID>_<POST_ID>"}' \
  -d 'ad_format=RIGHT_COLUMN_STANDARD' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews
```

The response is an iFrame that's valid for 24 hrs.

## See More

- [Ad Creative](/docs/marketing-api/reference/ad-creative)
- [Facebook App Ads](/docs/app-ads)
- [Ads Guide](https://www.facebook.com/business/ads-guide)

## Examples
Creating ad creative with object_story_id

Creating ad with creative_id reference

Reading creative fields

Setting up ad set with placement targeting

Generating ad previews for different formats

---
**Tags:** facebook-marketing-api, ad-creative, advertising, placements, previews, page-post-ads  
**Difficulty:** intermediate  
**Content Type:** guide  
**Source:** https://developers.facebook.com/docs/marketing-api/creative  
**Processed:** 2025-06-25T16:16:41.786Z