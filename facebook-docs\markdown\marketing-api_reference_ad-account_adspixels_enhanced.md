# Facebook Marketing API - Ad Account Ads Pixels Edge

## Summary
Documentation for the Facebook Marketing API's Ad Account Ads Pixels edge, which allows reading and creating ads pixels associated with an ad account. This endpoint enables management of Facebook pixels for tracking website conversions and building custom audiences.

## Key Points
- Supports reading existing ads pixels and creating new ones for ad accounts
- Reading endpoint returns pixel data with pagination and summary information
- Creating requires only a pixel name parameter and returns the new pixel ID
- Update and delete operations are not supported on this endpoint
- Rate limiting applies - too many calls will result in error 80004

## API Endpoints
- `GET /v23.0/<PIXEL_ID>/`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/adspixels`

## Parameters
- name (string) - Name of the pixel when creating
- fields - Specify fields to return in response
- summary - Specify summary fields like total_count
- access_token - OAuth 2.0 access token for authentication

## Content
# Ad Account Ads Pixels

The Ad Account Ads Pixels edge allows you to manage Facebook pixels associated with an ad account. This API endpoint supports reading existing pixels and creating new ones for tracking website events and building custom audiences.

## Reading

Retrieve ads pixels associated with an ad account.

### Endpoint
```
GET /v23.0/<PIXEL_ID>/?fields=code
```

### Parameters
This endpoint doesn't have any parameters.

### Response Fields
The response returns a JSON formatted result with:
- `data`: A list of AdsPixel nodes
- `paging`: Pagination information (see Graph API guide for details)
- `summary`: Aggregated information about the edge
  - `total_count` (int32): Total number of objects on this edge

### Code Examples

**cURL:**
```bash
curl -X GET -G \
  -d 'fields="code"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/<PIXEL_ID>/
```

**PHP SDK:**
```php
try {
  $response = $fb->get(
    '/<PIXEL_ID>/?fields=code',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
}
```

**JavaScript SDK:**
```javascript
FB.api(
    "/<PIXEL_ID>/",
    {
        "fields": "code"
    },
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Error Codes
- 200: Permissions error
- 80004: Too many calls to this ad-account (rate limiting)
- 190: Invalid OAuth 2.0 Access Token
- 100: Invalid parameter

## Creating

Create a new ads pixel for an ad account.

### Endpoint
```
POST /v23.0/act_<AD_ACCOUNT_ID>/adspixels
```

### Parameters
- `name` (string, required): Name of the pixel

### Return Type
Returns a struct with:
- `id`: numeric string (pixel ID)

This endpoint supports read-after-write functionality.

### Code Examples

**cURL:**
```bash
curl -X POST \
  -F 'name="My WCA Pixel"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adspixels
```

**PHP SDK:**
```php
try {
  $response = $fb->post(
    '/act_<AD_ACCOUNT_ID>/adspixels',
    array (
      'name' => 'My WCA Pixel',
    ),
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
}
```

### Error Codes
- 6202: More than one pixel exists for this account
- 6200: A pixel already exists for this account
- 100: Invalid parameter
- 200: Permissions error
- 190: Invalid OAuth 2.0 Access Token

## Updating
Update operations are not supported on this endpoint.

## Deleting
Delete operations are not supported on this endpoint.

## Examples
Reading pixel with cURL: GET request with fields parameter

Creating pixel with PHP SDK: POST request with name parameter

JavaScript SDK implementation for reading pixels

Android and iOS SDK examples for mobile integration

---
**Tags:** Facebook Marketing API, Ads Pixels, Ad Account, Graph API, Pixel Management, Website Tracking, Custom Audiences  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adspixels/  
**Processed:** 2025-06-25T16:22:09.146Z