# Facebook Marketing API - Ad Account Adplayables Reference

## Summary
This reference documentation covers the Ad Account Adplayables endpoint in the Facebook Marketing API, which manages playable assets associated with an ad account. It provides methods for reading existing playable assets and creating new ones through file uploads.

## Key Points
- Manages playable assets associated with Facebook ad accounts
- Supports reading existing playable content and creating new assets
- Requires HTML file upload when creating new playable assets
- Returns PlayableContent nodes with pagination support
- Does not support updating or deleting operations

## API Endpoints
- `GET /v23.0/{ad-account-id}/adplayables`
- `POST /act_{ad_account_id}/adplayables`

## Parameters
- name (string, required for creation)
- source (file, required for creation)
- ad-account-id (path parameter)

## Content
# Ad Account Adplayables

The Ad Account Adplayables endpoint manages playable assets associated with a Facebook ad account. This endpoint allows you to read existing playable content and create new playable assets.

## Reading Adplayables

Retrieves the playable assets associated with an ad account.

### Endpoint
```
GET /v23.0/{ad-account-id}/adplayables
```

### Parameters
This endpoint doesn't require any parameters.

### Response Format
Returns a JSON formatted result:
```json
{
  "data": [],
  "paging": {}
}
```

#### Response Fields
- **data**: A list of PlayableContent nodes
- **paging**: Pagination information (see Graph API guide for details)

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/adplayables HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/adplayables',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/adplayables",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

#### Android SDK
```java
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/adplayables",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();
```

#### iOS SDK
```objc
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/adplayables"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];
```

### Error Codes
| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 190 | Invalid OAuth 2.0 Access Token |
| 100 | Invalid parameter |

## Creating Adplayables

Creates a new playable asset by uploading an HTML file.

### Endpoint
```
POST /act_{ad_account_id}/adplayables
```

### Parameters
| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| name | string | The name of the playable asset | Yes |
| source | file | The local file path of the HTML playable asset | Yes |

### Return Type
Returns a struct containing:
```json
{
  "id": "numeric string"
}
```

### Error Codes
| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |

## Updating and Deleting

Both updating and deleting operations are not supported for this endpoint.

## Related Resources
- [PlayableContent Reference](/docs/graph-api/reference/playable-content/)
- [Graph API Usage Guide](/docs/graph-api/using-graph-api/)
- [Graph API Pagination](/docs/graph-api/using-graph-api/#paging)

## Examples
HTTP GET request example

PHP SDK implementation

JavaScript SDK usage

Android SDK integration

iOS SDK implementation

---
**Tags:** Facebook Marketing API, Ad Account, Playable Assets, Graph API, HTML Playables  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adplayables/  
**Processed:** 2025-06-25T16:19:58.260Z