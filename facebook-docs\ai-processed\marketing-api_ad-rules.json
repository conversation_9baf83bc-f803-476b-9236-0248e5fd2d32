{"title": "Facebook Marketing API - Ad Rules Engine", "summary": "The Ad Rules Engine is a central rule management service that automates ad management through schedule-based or trigger-based rules. It eliminates the need for manual monitoring and actions by expressing conditions as logical expressions that can automatically manage ad performance.", "content": "# Ad Rules Engine\n\nA central rule management service that helps you easily, efficiently and intelligently manage ads. Without it, you must query the Marketing API to monitor an ad's performance and manually take actions on certain conditions. Since we can express most conditions as logical expressions, we can automate management two ways: using **Schedule-based** or **Trigger-based** rules.\n\nNew to this? Try the rules-based notification quickstart in your App Dashboard, Quickstarts.\n\n## Documentation Contents\n\n### Overview\n\nCore concepts and usage requirements. Learn about:\n- **Evaluation Spec** - Defines the conditions to evaluate\n- **Execution Spec** - Specifies when and how rules are executed\n- **Change Spec** - Defines what changes to make when conditions are met\n\n### Guides\n\nUse case based guides covering:\n- **Trigger Based Ad Rules** - Rules that execute when specific conditions are met\n- **Schedule Based Rules** - Rules that execute on a predefined schedule\n- **Advanced Scheduling** - Complex scheduling configurations\n- **Rebalance Budget Ad Rules** - Automatically redistribute budgets based on performance\n- **ROAS Ad Rules** - Rules based on Return on Ad Spend metrics\n- **API Calls** - Implementation details for API integration", "keyPoints": ["Automates ad management through logical expressions and conditions", "Supports both schedule-based and trigger-based rule execution", "Eliminates manual monitoring and intervention for ad performance management", "Core components include Evaluation Spec, Execution Spec, and Change Spec", "Provides specialized rules for budget rebalancing and ROAS optimization"], "apiEndpoints": [], "parameters": ["Evaluation Spec", "Execution Spec", "Change Spec"], "examples": [], "tags": ["ad-rules", "automation", "marketing-api", "ad-management", "facebook-ads", "rule-engine"], "relatedTopics": ["Schedule-based Rules", "Trigger-based Rules", "Advanced Scheduling", "Rebalance Budget Ad Rules", "ROAS Ad Rules", "Marketing API", "App Dashboard Quickstarts"], "difficulty": "intermediate", "contentType": "overview", "originalUrl": "https://developers.facebook.com/docs/marketing-api/ad-rules", "processedAt": "2025-06-25T15:06:47.017Z", "processor": "openrouter-claude-sonnet-4"}