{"title": "Ad Campaign Management", "breadcrumbs": [], "content": "<h1 id=\"ad-campaign-management\">Ad Campaign Management</h1>\n\n<p>Managing ad campaigns through the Marketing API involves several key operations: modifying campaign settings, pausing and resuming campaigns, and deleting campaigns.</p>\n\n\n<h2 id=\"modify-an-ad-campaign\">Modify an Ad Campaign</h2>\n\n<p>To update an existing ad campaign, you can send a <code>POST</code> request to the <code>/&lt;CAMPAIGN_ID&gt;</code> endpoint. You can change various settings, including the campaign's objective, budget, and targeting attributes.</p>\n\n<p><strong>Example API Request:</strong></p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CAMPAIGN_ID&gt; \\</span><span class=\"pln\">\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'objective=CONVERSIONS'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'daily_budget=2000'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=ACTIVE'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span></pre>\n\n<h2 id=\"pause-an-ad-campaign\">Pause an Ad Campaign</h2>\n\n<p>Temporarily stopping a campaign can help you reassess your strategy without deleting the campaign entirely. To pause a campaign, update its status to <code>PAUSED</code>.</p>\n\n<p><strong>Example API Request:</strong></p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CAMPAIGN_ID&gt; \\</span><span class=\"pln\">\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=PAUSED'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span></pre><p>To resume the campaign, you can set the status back to <code>ACTIVE</code>.</p>\n\n\n<h2 id=\"archive-an-ad-campaign\">Archive an Ad Campaign</h2>\n\n<p>If you want to temporarily stop a campaign without deleting it, you can archive it instead. To do this, send a <code>POST</code> request to the <code>/&lt;CAMPAIGN_ID&gt;</code> endpoint with the status parameter set to <code>ARCHIVED</code>.</p>\n\n<p><strong>Example API Request</strong></p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">  \ncurl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CAMPAIGN_ID&gt; \\</span><span class=\"pln\">\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=ARCHIVED \\\n  -F '</span><span class=\"pln\">access_token</span><span class=\"pun\">=&lt;</span><span class=\"pln\">ACCESS_TOKEN</span><span class=\"pun\">&gt;</span><span class=\"str\">'</span></pre><p>Note that archiving a campaign will stop it from running, but it can be easily restored by changing its status back to <code>ACTIVE</code>.</p>\n\n\n<h2 id=\"delete-an-ad-campaign\">Delete an Ad Campaign</h2>\n\n<p>When you need to permanently remove a campaign, send a <code>DELETE</code> request to the <code>/&lt;CAMPAIGN_ID&gt;</code> endpoint.</p>\n<div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>Be cautious when deleting campaigns, as this action cannot be undone. Always double-check the campaign ID before deletion to avoid accidental loss of data.</p>\n</div></div><p><strong>Example API Request</strong></p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X DELETE \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/&lt;CAMPAIGN_ID&gt; \\</span><span class=\"pln\">\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span></pre>\n\n<h2 id=\"learn-more\">Learn More</h2>\n\n<ul>\n<li><a href=\"/docs/marketing-api/reference/ad-campaign-group\">Campaign Reference</a></li>\n<li><a href=\"/docs/marketing-apis/guides/manage-your-ad-object-status\">Manage Your Ad Object's Status</a></li>\n<li><a href=\"/docs/liz-test/marketing-api/troubleshooting\">Troubleshooting</a></li>\n</ul>\n\n\n<div class=\"_ap2s clearfix\"><a class=\"_ap2t _ap2u\" href=\"/docs/marketing-api/get-started/basic-ad-creation/create-an-ad\"><div class=\"_ap2x\">←</div><div class=\"_ap2w\">Previous</div><div class=\"_ap2z\">Create an Ad</div></a></div>\n\n", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/get-started", "/docs/marketing-api/get-started/manage-campaigns", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started/authorization", "/docs/marketing-api/get-started/authentication", "/docs/marketing-api/get-started/basic-ad-creation", "/docs/marketing-api/get-started/ad-optimization-basics", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/ad-campaign-group", "/docs/marketing-apis/guides/manage-your-ad-object-status", "/docs/marketing-api/get-started/basic-ad-creation/create-an-ad"], "url": "https://developers.facebook.com/docs/marketing-api/get-started/manage-campaigns", "timestamp": "2025-06-25T15:49:13.166Z"}