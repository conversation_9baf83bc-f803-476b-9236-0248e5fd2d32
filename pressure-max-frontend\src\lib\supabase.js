import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://pocxgdrtvwxjaurqhoua.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvY3hnZHJ0dnd4amF1cnFob3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5ODE3MDgsImV4cCI6MjA2NjU1NzcwOH0.NoRnR1vody2Em2kc3GKXKjqAWZHAwnqCpGSIIX1H1Cg';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});
