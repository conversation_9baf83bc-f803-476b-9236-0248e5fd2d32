<div class="_1dyy" id="u_0_1b_5n"><div class="_33zv _3e1u"><div class="_33zw" tabindex="0" role="button">On This Page<div><i class="img sp_zD_MvjcHflF sx_a3c10c" alt="" data-visualcompletion="css-img"></i><i class="hidden_elem img sp_zD_MvjcHflF sx_6874ff" alt="" data-visualcompletion="css-img"></i></div></div><div class="_5-24 hidden_elem"><a href="#overview"></a></div><div class="_5-24 hidden_elem"><a href="#Reading">Reading</a></div><div class="_5-24 hidden_elem"><a href="#example">Example</a></div><div class="_5-24 hidden_elem"><a href="#parameters">Parameters</a></div><div class="_5-24 hidden_elem"><a href="#fields">Fields</a></div><div class="_5-24 hidden_elem"><a href="#error-codes">Error Codes</a></div><div class="_5-24 hidden_elem"><a href="#Creating">Creating</a></div><div class="_5-24 hidden_elem"><a href="#limitations">Limitations</a></div><div class="_5-24 hidden_elem"><a href="#example-2">Example</a></div><div class="_5-24 hidden_elem"><a href="#parameters-2">Parameters</a></div><div class="_5-24 hidden_elem"><a href="#return-type">Return Type</a></div><div class="_5-24 hidden_elem"><a href="#error-codes-2">Error Codes</a></div><div class="_5-24 hidden_elem"><a href="#Updating">Updating</a></div><div class="_5-24 hidden_elem"><a href="#Deleting">Deleting</a></div></div></div><div id="documentation_body_pagelet" data-referrer="documentation_body_pagelet"><div class="_34yh" id="u_0_1_nq"><div data-click-area="main"><div class="_4-u2 _57mb _1u44 _4-u8"><div class="_4-u3 _5rva _mog"><div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_qh"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_LM"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"><i class="img sp_WbXBGqjC54o sx_514a5c"></i></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_p+"></div></span></div></div></div></div><div class="_1xb4 _3-98"><div class="_4-u2 _57mb _1u44 _4-u8 _3la3"><div class="_4-u3 _588p"><h1 id="overview"></h1><h1>Ad Account Ad Creatives</h1><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>The Ad Creatives that belong to this Ad Account.</p>
</div><div><br><p>Contains creative content for an ad account that you can use in your ads. Includes images, videos and so on. Using an ad account creative for a particular ad is subject to validation rules based on the ad type and other rules. See <a href="https://www.facebook.com/business/ads-guide?tab0=Mobile%20News%20Feed">Facebook Ads Guide</a> and <a href="/docs/marketing-api/validation#objective_creative">Validation, Objectives and Creative</a>.</p><p>To retrieve an account's ad creatives, make an HTTP GET call to</p><p></p><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_RD"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_Xz">PHP Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_sY">Python Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_qv">Java Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_u+">Ruby Business SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_zb">cURL</button></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_b_u1" style=""><code><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\AdAccount</span><span class="pun">;</span><span class="pln">
</span><span class="kwd">use</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pln">\Object\Fields\AdCreativeFields</span><span class="pun">;</span><span class="pln">

$account </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">AdAccount</span><span class="pun">(</span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">);</span><span class="pln">
$adcreatives </span><span class="pun">=</span><span class="pln"> $account</span><span class="pun">-&gt;</span><span class="pln">getAdCreatives</span><span class="pun">(</span><span class="pln">array</span><span class="pun">(</span><span class="pln">
  </span><span class="typ">AdCreativeFields</span><span class="pun">::</span><span class="pln">NAME</span><span class="pun">,</span><span class="pln">
</span><span class="pun">));</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_c_IS" style=""><code><span class="kwd">from</span><span class="pln"> facebookads</span><span class="pun">.</span><span class="pln">adobjects</span><span class="pun">.</span><span class="pln">adaccount </span><span class="kwd">import</span><span class="pln"> </span><span class="typ">AdAccount</span><span class="pln">
</span><span class="kwd">from</span><span class="pln"> facebookads</span><span class="pun">.</span><span class="pln">adobjects</span><span class="pun">.</span><span class="pln">adcreative </span><span class="kwd">import</span><span class="pln"> </span><span class="typ">AdCreative</span><span class="pln">

ad_account </span><span class="pun">=</span><span class="pln"> </span><span class="typ">AdAccount</span><span class="pun">(</span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">)</span><span class="pln">
ad_account</span><span class="pun">.</span><span class="pln">get_ad_creatives</span><span class="pun">(</span><span class="pln">fields</span><span class="pun">=[</span><span class="typ">AdCreative</span><span class="pun">.</span><span class="typ">Field</span><span class="pun">.</span><span class="pln">object_story_id</span><span class="pun">])</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_d_xV" style=""><code><span class="typ">APINodeList</span><span class="pun">&lt;</span><span class="typ">AdCreative</span><span class="pun">&gt;</span><span class="pln"> adCreatives </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">AdAccount</span><span class="pun">(</span><span class="pln">act_</span><span class="pun">&lt;</span><span class="pln">AD_ACCOUNT_ID</span><span class="pun">&gt;,</span><span class="pln"> context</span><span class="pun">).</span><span class="pln">getAdCreatives</span><span class="pun">()</span><span class="pln">
  </span><span class="pun">.</span><span class="pln">requestNameField</span><span class="pun">()</span><span class="pln">
  </span><span class="pun">.</span><span class="pln">execute</span><span class="pun">();</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_e_eU" style=""><code><span class="pln">ad_account </span><span class="pun">=</span><span class="pln"> </span><span class="typ">FacebookAds</span><span class="pun">::</span><span class="typ">AdAccount</span><span class="pun">.</span><span class="kwd">get</span><span class="pun">(</span><span class="str">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class="pun">)</span><span class="pln">
creatives </span><span class="pun">=</span><span class="pln"> ad_account</span><span class="pun">.</span><span class="pln">adcreatives</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_f_XV" style=""><code><span class="pln">curl </span><span class="pun">-</span><span class="pln">G \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">'fields=name'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
  https</span><span class="pun">:</span><span class="com">//graph.facebook.com/v2.11/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></code></pre></div></div><p></p></div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_g_16"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_h_uM">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_i_fY">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_j_E/">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_k_/N">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_l_ST">iOS SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_m_9r">cURL</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives%3Ffields%3Dname&amp;version=v23.0" target="_blank">Graph API Explorer<i class="_3-99 img sp_c_epTrfICMy sx_7b2121"></i></a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_n_Tt" style=""><code><span class="pln">GET </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/</span><span class="pln">act_</span><span class="pun">&lt;</span><span class="pln">AD_ACCOUNT_ID</span><span class="pun">&gt;</span><span class="str">/adcreatives?fields=name HTTP/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_o_fN" style=""><code><span class="com">/* PHP SDK v5.0.0 */</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">try</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  </span><span class="com">// Returns a `Facebook\FacebookResponse` object</span><span class="pln">
  $response </span><span class="pun">=</span><span class="pln"> $fb</span><span class="pun">-&gt;</span><span class="kwd">get</span><span class="pun">(</span><span class="pln">
    </span><span class="str">'/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives?fields=name'</span><span class="pun">,</span><span class="pln">
    </span><span class="str">'{access-token}'</span><span class="pln">
  </span><span class="pun">);</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookResponseException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Graph returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookSDKException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Facebook SDK returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln">
$graphNode </span><span class="pun">=</span><span class="pln"> $response</span><span class="pun">-&gt;</span><span class="pln">getGraphNode</span><span class="pun">();</span><span class="pln">
</span><span class="com">/* handle the result */</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_p_ZA" style=""><code><span class="com">/* make the API call */</span><span class="pln">
FB</span><span class="pun">.</span><span class="pln">api</span><span class="pun">(</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives"</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"fields"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"name"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="kwd">function</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="kwd">if</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response </span><span class="pun">&amp;&amp;</span><span class="pln"> </span><span class="pun">!</span><span class="pln">response</span><span class="pun">.</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="com">/* handle the result */</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">);</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_q_TG" style=""><code><span class="typ">Bundle</span><span class="pln"> </span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">Bundle</span><span class="pun">();</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"fields"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"name"</span><span class="pun">);</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">(</span><span class="pln">
    </span><span class="typ">AccessToken</span><span class="pun">.</span><span class="pln">getCurrentAccessToken</span><span class="pun">(),</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives"</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">params</span><span class="pun">,</span><span class="pln">
    </span><span class="typ">HttpMethod</span><span class="pun">.</span><span class="pln">GET</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">.</span><span class="typ">Callback</span><span class="pun">()</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="kwd">public</span><span class="pln"> </span><span class="kwd">void</span><span class="pln"> onCompleted</span><span class="pun">(</span><span class="typ">GraphResponse</span><span class="pln"> response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
            </span><span class="com">/* handle the result */</span><span class="pln">
        </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">).</span><span class="pln">executeAsync</span><span class="pun">();</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_r_BA" style=""><code><span class="typ">NSDictionary</span><span class="pln"> </span><span class="pun">*</span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="pun">@{</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"fields"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"name"</span><span class="pun">,</span><span class="pln">
</span><span class="pun">};</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> </span><span class="pun">*</span><span class="pln">request </span><span class="pun">=</span><span class="pln"> </span><span class="pun">[[</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> alloc</span><span class="pun">]</span><span class="pln">
                               initWithGraphPath</span><span class="pun">:@</span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives"</span><span class="pln">
                                      parameters</span><span class="pun">:</span><span class="kwd">params</span><span class="pln">
                                      </span><span class="typ">HTTPMethod</span><span class="pun">:@</span><span class="str">"GET"</span><span class="pun">];</span><span class="pln">
</span><span class="pun">[</span><span class="pln">request startWithCompletionHandler</span><span class="pun">:^(</span><span class="typ">FBSDKGraphRequestConnection</span><span class="pln"> </span><span class="pun">*</span><span class="pln">connection</span><span class="pun">,</span><span class="pln">
                                      id result</span><span class="pun">,</span><span class="pln">
                                      </span><span class="typ">NSError</span><span class="pln"> </span><span class="pun">*</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="com">// Handle the result</span><span class="pln">
</span><span class="pun">}];</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_s_bP" style=""><code><span class="pln">curl </span><span class="pun">-</span><span class="pln">X GET </span><span class="pun">-</span><span class="pln">G \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">'fields="name"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
  https</span><span class="pun">:</span><span class="com">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id="fields">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class="_3hux"><p>{
    "<code>data</code>": [],
    "<code>paging</code>": {},
    "<code>summary</code>": {}
}</p>
</pre><div class="_3-8o"><h4><code>data</code></h4>A list of <a target="_blank" href="/docs/marketing-api/reference/ad-creative/">AdCreative</a> nodes.</div><div class="_3-8o"><h4><code>paging</code></h4>For more details about pagination, see the <a href="/docs/graph-api/using-graph-api/#paging">Graph API guide</a>.</div><div class="_3-8o"><h4><code>summary</code></h4><p>Aggregated information about the edge, such as counts. Specify the fields to fetch in the summary param (like <code>summary=total_count</code>).</p><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><code>total_count</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><p>Total number of creatives in the ad account.</p>
</div><p></p></td></tr></tbody></table></div></div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr></tbody></table></div></div></div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Creating">Creating</h2><div class="_844_"><div class="_3-98">You can make a POST request to <code>adcreatives</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-account/adcreatives/"><code>/act_{ad_account_id}/adcreatives</code></a></li></ul><div>When posting to this edge, no Graph object will be created.</div><div><h3 id="limitations">Limitations</h3><ul class="uiList _4of _4kg"><li><div class="fcb">When creating ad creatives, if the <code>object_story_id</code> being used is already in use by an existing creative, then the API will return the value of the existing creative_id instead of creating a new one.</div></li><li><div class="fcb">Using <code>radius</code> can cause an error, code: 100, subcode 1815946, when targeting multiple locations. We recommend creating an ad for each location or not using <code>radius</code> in your call.</div></li></ul></div><div><h3 id="example-2">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_t_+M"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_u_55">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_v_MH">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_w_+P">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_x_/3">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_y_WQ">iOS SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_z_Wq">cURL</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives%3Fname%3DSample%2BPromoted%2BPost%26object_story_id%3D%253CPAGE_ID%253E_%253CPOST_ID%253E&amp;version=v23.0" target="_blank">Graph API Explorer<i class="_3-99 img sp_c_epTrfICMy sx_7b2121"></i></a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_10_Wi" style=""><code><span class="pln">POST </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/</span><span class="pln">act_</span><span class="pun">&lt;</span><span class="pln">AD_ACCOUNT_ID</span><span class="pun">&gt;</span><span class="str">/adcreatives HTTP/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com

name</span><span class="pun">=</span><span class="typ">Sample</span><span class="pun">+</span><span class="typ">Promoted</span><span class="pun">+</span><span class="typ">Post</span><span class="pun">&amp;</span><span class="pln">object_story_id</span><span class="pun">=%</span><span class="lit">3CPAGE</span><span class="pln">_ID</span><span class="pun">%</span><span class="lit">3E</span><span class="pln">_</span><span class="pun">%</span><span class="lit">3CPOST</span><span class="pln">_ID</span><span class="pun">%</span><span class="lit">3E</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_11_81" style=""><code><span class="com">/* PHP SDK v5.0.0 */</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">try</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  </span><span class="com">// Returns a `Facebook\FacebookResponse` object</span><span class="pln">
  $response </span><span class="pun">=</span><span class="pln"> $fb</span><span class="pun">-&gt;</span><span class="pln">post</span><span class="pun">(</span><span class="pln">
    </span><span class="str">'/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives'</span><span class="pun">,</span><span class="pln">
    array </span><span class="pun">(</span><span class="pln">
      </span><span class="str">'name'</span><span class="pln"> </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'Sample Promoted Post'</span><span class="pun">,</span><span class="pln">
      </span><span class="str">'object_story_id'</span><span class="pln"> </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'&lt;PAGE_ID&gt;_&lt;POST_ID&gt;'</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">),</span><span class="pln">
    </span><span class="str">'{access-token}'</span><span class="pln">
  </span><span class="pun">);</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookResponseException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Graph returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookSDKException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Facebook SDK returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln">
$graphNode </span><span class="pun">=</span><span class="pln"> $response</span><span class="pun">-&gt;</span><span class="pln">getGraphNode</span><span class="pun">();</span><span class="pln">
</span><span class="com">/* handle the result */</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_12_Yp" style=""><code><span class="com">/* make the API call */</span><span class="pln">
FB</span><span class="pun">.</span><span class="pln">api</span><span class="pun">(</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives"</span><span class="pun">,</span><span class="pln">
    </span><span class="str">"POST"</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"name"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"Sample Promoted Post"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"object_story_id"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="kwd">function</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="kwd">if</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response </span><span class="pun">&amp;&amp;</span><span class="pln"> </span><span class="pun">!</span><span class="pln">response</span><span class="pun">.</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="com">/* handle the result */</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">);</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_13_9e" style=""><code><span class="typ">Bundle</span><span class="pln"> </span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">Bundle</span><span class="pun">();</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"name"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"Sample Promoted Post"</span><span class="pun">);</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"object_story_id"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;"</span><span class="pun">);</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">(</span><span class="pln">
    </span><span class="typ">AccessToken</span><span class="pun">.</span><span class="pln">getCurrentAccessToken</span><span class="pun">(),</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives"</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">params</span><span class="pun">,</span><span class="pln">
    </span><span class="typ">HttpMethod</span><span class="pun">.</span><span class="pln">POST</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">.</span><span class="typ">Callback</span><span class="pun">()</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="kwd">public</span><span class="pln"> </span><span class="kwd">void</span><span class="pln"> onCompleted</span><span class="pun">(</span><span class="typ">GraphResponse</span><span class="pln"> response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
            </span><span class="com">/* handle the result */</span><span class="pln">
        </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">).</span><span class="pln">executeAsync</span><span class="pun">();</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_14_N2" style=""><code><span class="typ">NSDictionary</span><span class="pln"> </span><span class="pun">*</span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="pun">@{</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"name"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"Sample Promoted Post"</span><span class="pun">,</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"object_story_id"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"&lt;PAGE_ID&gt;_&lt;POST_ID&gt;"</span><span class="pun">,</span><span class="pln">
</span><span class="pun">};</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> </span><span class="pun">*</span><span class="pln">request </span><span class="pun">=</span><span class="pln"> </span><span class="pun">[[</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> alloc</span><span class="pun">]</span><span class="pln">
                               initWithGraphPath</span><span class="pun">:@</span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives"</span><span class="pln">
                                      parameters</span><span class="pun">:</span><span class="kwd">params</span><span class="pln">
                                      </span><span class="typ">HTTPMethod</span><span class="pun">:@</span><span class="str">"POST"</span><span class="pun">];</span><span class="pln">
</span><span class="pun">[</span><span class="pln">request startWithCompletionHandler</span><span class="pun">:^(</span><span class="typ">FBSDKGraphRequestConnection</span><span class="pln"> </span><span class="pun">*</span><span class="pln">connection</span><span class="pun">,</span><span class="pln">
                                      id result</span><span class="pun">,</span><span class="pln">
                                      </span><span class="typ">NSError</span><span class="pln"> </span><span class="pun">*</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="com">// Handle the result</span><span class="pln">
</span><span class="pun">}];</span></code></pre><pre class="_5gt1 prettyprint hidden_elem prettyprinted" id="u_0_15_7u" style=""><code><span class="pln">curl </span><span class="pun">-</span><span class="pln">X POST \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'name="Sample Promoted Post"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'object_story_id="&lt;PAGE_ID&gt;_&lt;POST_ID&gt;"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
  https</span><span class="pun">:</span><span class="com">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/adcreatives</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters-2">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_16_QH"><tr class="row_0"><td><div class="_yc"><span><code>actor_id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>The actor ID (Page ID) of this creative.</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29 _5m27"><td><div class="_yc"><span><code>ad_disclaimer_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p><a href="/docs/graph-api/reference/ad-creative-ad-disclaimer/">Disclaimer information</a> to attach to your creative.</p>
</div></div><p></p></td></tr><tr class="row_1-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>title</code></span></div><div class="_yb">enum {HEALTH_DISCLAIMER, IMPORTANT_SAFETY_INFORMATION, MEDICATION_GUIDE, OFFER_DETAILS, PRESCRIBING_INFORMATION, TERMS_AND_CONDITIONS}</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_1-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_1-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2"><td><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p><a href="/docs/marketing-api/reference/ad-label">Ad Labels</a> associated with this creative. Used to group it with related ad objects.</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>applink_treatment</code></span></div><div class="_yb">enum{automatic, deeplink_with_web_fallback, deeplink_with_appstore_fallback, web_only}</div></td><td><p class="_yd"></p><div><div><p>Used for <a href="/docs/marketing-api/dynamic-product-ads/ads-management">Dynamic Ads</a>. Specify what action should occur if a person clicks a link in the ad, but the business' app is not installed on their device. For example, open a webpage displaying the product, or open the app in an app store on the person's mobile device.</p>
</div></div><p></p></td></tr><tr class="row_4 _5m27"><td><div class="_yc"><span><code>asset_feed_spec</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div><p>Used for <a href="/docs/marketing-api/dynamic-creative/dynamic-creative-optimization">Dynamic Creative</a> to automatically experiment and deliver different variations of an ad's creative. Specifies an asset feed with multiple images, text and other assets used to generate variations of an ad. Formatted as a JSON string.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>images</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>hash</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>image_crops</code></span></div><div class="_yb">dictionary { enum{191x100, 100x72, 400x150, 600x360, 100x100, 400x500, 90x160} : &lt;list&lt;list&lt;int64&gt;&gt;&gt; }</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-0-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-0-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-0-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>tag</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>videos</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>video_id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>thumbnail_id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>thumbnail_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>thumbnail_hash</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>caption_ids</code></span></div><div class="_yb">list&lt;numeric string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-7 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>do_not_clone_flag</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>tag</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-1-9 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>thumbnail_source</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>bodies</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-2-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>translation_confidence</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>asset_source</code></span></div><div class="_yb">enum {ACO_TEXT_LIBRARY, TEXT_GEN_INPUT_TEXT, TEXT_GEN_SUGGESTION_DIVERSITY_UNEDITED, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_PARAPHRASING_UNEDITED, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_EDITED, TEXT_GEN_SUGGESTION_LLM_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_UNEDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_UNEDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_MULTILINGUAL_UNEDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_MULTILINGUAL_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_LLM_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_LLM_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_2_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_2_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_SINGLE_STAGE_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_V_1_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_ORCHESTRATOR_V_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_ORCHESTRATOR_V_1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V3_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V3_EDITED, TEXT_GEN_SUGGESTION_LLM_V3_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V3_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V3_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_FOR_IG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_FOR_IG_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_UNEDITED, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V3_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V3_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_FOR_FB_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_FOR_FB_EDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ADS_MANAGER_LLM_HEADLINE_HEURISTICS_V1_UNEDITED, TEXT_GEN_SUGGESTION_ADS_MANAGER_LLM_HEADLINE_HEURISTICS_V1_EDITED, TEXT_GEN_SUGGESTION_PPO_ORCHESTRATOR_V1_UNEDITED, TEXT_GEN_SUGGESTION_PPO_ORCHESTRATOR_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_ALIGNER_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_ALIGNER_EDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_PRIMARY_TEXT_UNEDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_PRIMARY_TEXT_EDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_HISTORICAL_ADS_UNEDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_HISTORICAL_ADS_EDITED, TEXT_GEN_SUGGESTION_LLM_FREEFORM_BRAND_TONE_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FREEFORM_BRAND_TONE_EDITED, TEXT_GEN_SUGGESTION_LLM_COHORT_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_COHORT_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_LIGHTHOUSE_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LIGHTHOUSE_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_SP_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_SP_EDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_EA_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_EA_EDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_EDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_NON_EN_UNEDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_NON_EN_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_BASED_GEN_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_BASED_GEN_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_SEQUENTIAL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_SEQUENTIAL_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_MM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_MM_EDITED, TEXT_GEN_SUGGESTION_LLM_PPO_TEXT_IMAGE_RM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PPO_TEXT_IMAGE_RM_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_LLAMA4_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_LLAMA4_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_GPT4O_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_GPT4O_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_HISTORICAL_ADS_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_HISTORICAL_ADS_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_COMPOUND_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_COMPOUND_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_GPT4O_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_GPT4O_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_COMPOUND_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_COMPOUND_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_DEEPER_FUNNEL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_DEEPER_FUNNEL_EDITED, TEXT_GEN_SUGGESTION_LLM_LLAMA4_SEGMENT_PROMPT_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LLAMA4_SEGMENT_PROMPT_EDITED, TEXT_GEN_SUGGESTION_LLM_TRANSLATION_V1, TEXT_FIRST_MANUAL_OPTION, TEXT_AUTO_TRANSLATION_MT_V1, TEXT_AUTO_TRANSLATION_LLM_V1, TEXT_TRANSLATIONS_LLAMA4_V1_TITLE, TEXT_TRANSLATIONS_LLAMA4_V1_BODY, TEXT_AD_HIGHLIGHT, PROMINENT_HEADLINE_NO_SHOW, TEXT_GEN_AUTOMATION, TEXT_GEN_AUTOMATION_ALPHA_DEEPER_FUNNEL_USER_COHORT_BATCH_GENERATION, TEXT_GEN_AUTOMATION_ALPHA_BRAND_ALIGNER_USER_COHORT_SEQUENTIAL_GENERATION, TEXT_GEN_AUTOMATION_ALPHA_BRAND_ALIGNER_USER_COHORT_BATCH_GENERATION, TEXT_GEN_SUGGESTION_MOCK_UNEDITED, TEXT_GEN_SUGGESTION_MOCK_EDITED, TEXT_GEN_SUGGESTION_MOCK_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_MOCK_EDITED_BEFORE_AND_AFTER_ADD}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>uuid</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-2-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text_gen_original_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-2-7 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>language</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>target_audience</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-9 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>tracking_tag</code></span></div><div class="_yb">list&lt;enum {NONE, TEXT_GEN_LLM_PERSONA_EDITED_TAG}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-10 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text_gen_input_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-2-11 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {TEMPLATE}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-2-12 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>action_type</code></span></div><div class="_yb">enum {DEFAULT_ADD, DEFAULT_ADD_INTERACTION, MANUAL_ADD, STICKY_ADD, STICKY_ADD_INTERACTION}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-3 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>call_to_action_types</code></span></div><div class="_yb">list&lt;enum{OPEN_LINK, LIKE_PAGE, SHOP_NOW, PLAY_GAME, INSTALL_APP, USE_APP, CALL, CALL_ME, VIDEO_CALL, INSTALL_MOBILE_APP, USE_MOBILE_APP, MOBILE_DOWNLOAD, BOOK_TRAVEL, LISTEN_MUSIC, WATCH_VIDEO, LEARN_MORE, SIGN_UP, DOWNLOAD, WATCH_MORE, NO_BUTTON, VISIT_PAGES_FEED, CALL_NOW, APPLY_NOW, CONTACT, BUY_NOW, GET_OFFER, GET_OFFER_VIEW, BUY_TICKETS, UPDATE_APP, GET_DIRECTIONS, BUY, SEND_UPDATES, MESSAGE_PAGE, DONATE, SUBSCRIBE, SAY_THANKS, SELL_NOW, SHARE, DONATE_NOW, GET_QUOTE, CONTACT_US, ORDER_NOW, START_ORDER, ADD_TO_CART, VIEW_CART, VIEW_IN_CART, VIDEO_ANNOTATION, RECORD_NOW, INQUIRE_NOW, CONFIRM, REFER_FRIENDS, REQUEST_TIME, GET_SHOWTIMES, LISTEN_NOW, WOODHENGE_SUPPORT, SOTTO_SUBSCRIBE, FOLLOW_USER, RAISE_MONEY, EVENT_RSVP, WHATSAPP_MESSAGE, FOLLOW_NEWS_STORYLINE, SEE_MORE, BOOK_NOW, FIND_A_GROUP, FIND_YOUR_GROUPS, PAY_TO_ACCESS, PURCHASE_GIFT_CARDS, FOLLOW_PAGE, SEND_A_GIFT, SWIPE_UP_SHOP, SWIPE_UP_PRODUCT, SEND_GIFT_MONEY, PLAY_GAME_ON_FACEBOOK, GET_STARTED, OPEN_INSTANT_APP, AUDIO_CALL, GET_PROMOTIONS, JOIN_CHANNEL, MAKE_AN_APPOINTMENT, ASK_ABOUT_SERVICES, BOOK_A_CONSULTATION, GET_A_QUOTE, BUY_VIA_MESSAGE, ASK_FOR_MORE_INFO, CHAT_WITH_US, VIEW_PRODUCT, VIEW_CHANNEL, GET_IN_TOUCH, WATCH_LIVE_VIDEO}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>descriptions</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-4-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>translation_confidence</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>asset_source</code></span></div><div class="_yb">enum {ACO_TEXT_LIBRARY, TEXT_GEN_INPUT_TEXT, TEXT_GEN_SUGGESTION_DIVERSITY_UNEDITED, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_PARAPHRASING_UNEDITED, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_EDITED, TEXT_GEN_SUGGESTION_LLM_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_UNEDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_UNEDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_MULTILINGUAL_UNEDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_MULTILINGUAL_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_LLM_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_LLM_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_2_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_2_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_SINGLE_STAGE_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_V_1_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_ORCHESTRATOR_V_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_ORCHESTRATOR_V_1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V3_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V3_EDITED, TEXT_GEN_SUGGESTION_LLM_V3_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V3_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V3_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_FOR_IG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_FOR_IG_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_UNEDITED, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V3_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V3_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_FOR_FB_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_FOR_FB_EDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ADS_MANAGER_LLM_HEADLINE_HEURISTICS_V1_UNEDITED, TEXT_GEN_SUGGESTION_ADS_MANAGER_LLM_HEADLINE_HEURISTICS_V1_EDITED, TEXT_GEN_SUGGESTION_PPO_ORCHESTRATOR_V1_UNEDITED, TEXT_GEN_SUGGESTION_PPO_ORCHESTRATOR_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_ALIGNER_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_ALIGNER_EDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_PRIMARY_TEXT_UNEDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_PRIMARY_TEXT_EDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_HISTORICAL_ADS_UNEDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_HISTORICAL_ADS_EDITED, TEXT_GEN_SUGGESTION_LLM_FREEFORM_BRAND_TONE_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FREEFORM_BRAND_TONE_EDITED, TEXT_GEN_SUGGESTION_LLM_COHORT_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_COHORT_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_LIGHTHOUSE_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LIGHTHOUSE_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_SP_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_SP_EDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_EA_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_EA_EDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_EDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_NON_EN_UNEDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_NON_EN_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_BASED_GEN_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_BASED_GEN_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_SEQUENTIAL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_SEQUENTIAL_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_MM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_MM_EDITED, TEXT_GEN_SUGGESTION_LLM_PPO_TEXT_IMAGE_RM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PPO_TEXT_IMAGE_RM_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_LLAMA4_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_LLAMA4_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_GPT4O_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_GPT4O_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_HISTORICAL_ADS_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_HISTORICAL_ADS_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_COMPOUND_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_COMPOUND_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_GPT4O_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_GPT4O_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_COMPOUND_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_COMPOUND_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_DEEPER_FUNNEL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_DEEPER_FUNNEL_EDITED, TEXT_GEN_SUGGESTION_LLM_LLAMA4_SEGMENT_PROMPT_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LLAMA4_SEGMENT_PROMPT_EDITED, TEXT_GEN_SUGGESTION_LLM_TRANSLATION_V1, TEXT_FIRST_MANUAL_OPTION, TEXT_AUTO_TRANSLATION_MT_V1, TEXT_AUTO_TRANSLATION_LLM_V1, TEXT_TRANSLATIONS_LLAMA4_V1_TITLE, TEXT_TRANSLATIONS_LLAMA4_V1_BODY, TEXT_AD_HIGHLIGHT, PROMINENT_HEADLINE_NO_SHOW, TEXT_GEN_AUTOMATION, TEXT_GEN_AUTOMATION_ALPHA_DEEPER_FUNNEL_USER_COHORT_BATCH_GENERATION, TEXT_GEN_AUTOMATION_ALPHA_BRAND_ALIGNER_USER_COHORT_SEQUENTIAL_GENERATION, TEXT_GEN_AUTOMATION_ALPHA_BRAND_ALIGNER_USER_COHORT_BATCH_GENERATION, TEXT_GEN_SUGGESTION_MOCK_UNEDITED, TEXT_GEN_SUGGESTION_MOCK_EDITED, TEXT_GEN_SUGGESTION_MOCK_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_MOCK_EDITED_BEFORE_AND_AFTER_ADD}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>tracking_tag</code></span></div><div class="_yb">list&lt;enum {NONE, TEXT_GEN_LLM_PERSONA_EDITED_TAG}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>uuid</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-4-7 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text_gen_original_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-4-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>language</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-9 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>target_audience</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-10 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text_gen_input_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-4-11 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>action_type</code></span></div><div class="_yb">enum {DEFAULT_ADD, DEFAULT_ADD_INTERACTION, MANUAL_ADD, STICKY_ADD, STICKY_ADD_INTERACTION}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-4-12 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {TEMPLATE}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>link_urls</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>website_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>display_url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>deeplink_url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>carousel_see_more_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {TEMPLATE}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>omnichannel_link_spec</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>web</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>app</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>application_id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>platform_specs</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>android</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>package_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>ios</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>app_store_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-1-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-2 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>ipad</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-2-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-2-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>app_store_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-2-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-3 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>iphone</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-3-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-3-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>app_store_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-7-1-1-3-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-5-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>object_store_urls</code></span></div><div class="_yb">list&lt;string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>titles</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-6-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>translation_confidence</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>asset_source</code></span></div><div class="_yb">enum {ACO_TEXT_LIBRARY, TEXT_GEN_INPUT_TEXT, TEXT_GEN_SUGGESTION_DIVERSITY_UNEDITED, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_DIVERSITY_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_PARAPHRASING_UNEDITED, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_PARAPHRASING_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_EDITED, TEXT_GEN_SUGGESTION_LLM_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_V_1_2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_UNEDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_UNEDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_BRAND_IDENTITY_V_1_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_UNEDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_V_1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ORCHESTRATOR_MULTILINGUAL_UNEDITED, TEXT_GEN_SUGGESTION_ORCHESTRATOR_MULTILINGUAL_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_LLM_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_LLM_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_2_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_V_1_2_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_SINGLE_STAGE_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_SEMANTIC_VARIATIONS_SINGLE_STAGE_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_V_1_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_BRAND_IDENTITY_V_1_1_UNEDITED, TEXT_GEN_SUGGESTION_GUIDANCE_ORCHESTRATOR_V_1_EDITED, TEXT_GEN_SUGGESTION_GUIDANCE_ORCHESTRATOR_V_1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V2_DPO_PPO_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V3_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V3_EDITED, TEXT_GEN_SUGGESTION_LLM_V3_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V3_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_V3_LONG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_V3_LONG_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_FOR_IG_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_FOR_IG_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_V2_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_UNEDITED, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_APP_DESCRIPTION_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V3_UNEDITED, TEXT_GEN_SUGGESTION_LLM_ORCHESTRATOR_V3_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_LC_PPO_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_FOR_FB_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FC_PPO_LLAMA3_V1_FOR_FB_EDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_LLM_SFT_LLAMA3_V1_EDITED_BEFORE_AND_AFTER_ADD, TEXT_GEN_SUGGESTION_ADS_MANAGER_LLM_HEADLINE_HEURISTICS_V1_UNEDITED, TEXT_GEN_SUGGESTION_ADS_MANAGER_LLM_HEADLINE_HEURISTICS_V1_EDITED, TEXT_GEN_SUGGESTION_PPO_ORCHESTRATOR_V1_UNEDITED, TEXT_GEN_SUGGESTION_PPO_ORCHESTRATOR_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_ALIGNER_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_ALIGNER_EDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_PRIMARY_TEXT_UNEDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_PRIMARY_TEXT_EDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_HISTORICAL_ADS_UNEDITED, TEXT_GEN_SUGGESTION_LLM_IMPLICIT_BRAND_TONE_HISTORICAL_ADS_EDITED, TEXT_GEN_SUGGESTION_LLM_FREEFORM_BRAND_TONE_UNEDITED, TEXT_GEN_SUGGESTION_LLM_FREEFORM_BRAND_TONE_EDITED, TEXT_GEN_SUGGESTION_LLM_COHORT_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_COHORT_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_LIGHTHOUSE_LLAMA3_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LIGHTHOUSE_LLAMA3_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_SP_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_SP_EDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_EA_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PURPOSEFUL_GEN_EA_EDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_EDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_NON_EN_UNEDITED, TEXT_GEN_SUGGESTION_LLM_UNIFIED_MULTILINGUAL_NON_EN_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_BASED_GEN_V1_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_BASED_GEN_V1_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_SEQUENTIAL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_PPO_SEQUENTIAL_EDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_MM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_DEEPER_FUNNEL_MM_EDITED, TEXT_GEN_SUGGESTION_LLM_PPO_TEXT_IMAGE_RM_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PPO_TEXT_IMAGE_RM_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_LLAMA4_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_LLAMA4_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_GPT4O_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_GPT4O_EDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_HISTORICAL_ADS_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_BRAND_HISTORICAL_ADS_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_COMPOUND_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_V2_COMPOUND_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_GPT4O_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_GPT4O_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_COMPOUND_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_EDITABLE_COMPOUND_EDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_DEEPER_FUNNEL_UNEDITED, TEXT_GEN_SUGGESTION_LLM_PERSONA_DEEPER_FUNNEL_EDITED, TEXT_GEN_SUGGESTION_LLM_LLAMA4_SEGMENT_PROMPT_UNEDITED, TEXT_GEN_SUGGESTION_LLM_LLAMA4_SEGMENT_PROMPT_EDITED, TEXT_GEN_SUGGESTION_LLM_TRANSLATION_V1, TEXT_FIRST_MANUAL_OPTION, TEXT_AUTO_TRANSLATION_MT_V1, TEXT_AUTO_TRANSLATION_LLM_V1, TEXT_TRANSLATIONS_LLAMA4_V1_TITLE, TEXT_TRANSLATIONS_LLAMA4_V1_BODY, TEXT_AD_HIGHLIGHT, PROMINENT_HEADLINE_NO_SHOW, TEXT_GEN_AUTOMATION, TEXT_GEN_AUTOMATION_ALPHA_DEEPER_FUNNEL_USER_COHORT_BATCH_GENERATION, TEXT_GEN_AUTOMATION_ALPHA_BRAND_ALIGNER_USER_COHORT_SEQUENTIAL_GENERATION, TEXT_GEN_AUTOMATION_ALPHA_BRAND_ALIGNER_USER_COHORT_BATCH_GENERATION, TEXT_GEN_SUGGESTION_MOCK_UNEDITED, TEXT_GEN_SUGGESTION_MOCK_EDITED, TEXT_GEN_SUGGESTION_MOCK_EDITED_BEFORE_ADD, TEXT_GEN_SUGGESTION_MOCK_EDITED_BEFORE_AND_AFTER_ADD}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>uuid</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-6-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text_gen_original_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-6-7 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>language</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>target_audience</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-9 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text_gen_input_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-6-10 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>action_type</code></span></div><div class="_yb">enum {DEFAULT_ADD, DEFAULT_ADD_INTERACTION, MANUAL_ADD, STICKY_ADD, STICKY_ADD_INTERACTION}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-11 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {TEMPLATE}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-6-12 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>tracking_tag</code></span></div><div class="_yb">list&lt;enum {NONE, TEXT_GEN_LLM_PERSONA_EDITED_TAG}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-7 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>captions</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-7-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-7-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-7-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-7-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {TEMPLATE}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-8 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>ad_formats</code></span></div><div class="_yb">list&lt;enum {AUTOMATIC_FORMAT, CAROUSEL, CAROUSEL_IMAGE, CAROUSEL_VIDEO, COLLECTION, SINGLE_IMAGE, SINGLE_VIDEO, POST}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>groups</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>image_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>video_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>body_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>description_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_url_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>title_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-9-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>caption_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>target_rules</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>targeting</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_min</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_max</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>audience_network_positions</code></span></div><div class="_yb">list&lt;enum{classic, rewarded_video, instream_video}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-3 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>device_platforms</code></span></div><div class="_yb">list&lt;enum{desktop, mobile}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-4 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>facebook_positions</code></span></div><div class="_yb">list&lt;enum{feed, instant_article, instream_video, marketplace, right_hand_column, search, story, suggested_video, video_feeds, story_sticker, facebook_reels_overlay, biz_disco_feed, facebook_reels, profile_feed, profile_reels, notification}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-5 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>instagram_positions</code></span></div><div class="_yb">list&lt;enum{reels, reels_overlay, stream, story, explore, explore_home, ig_search, shop, profile_feed, profile_reels, effect_tray}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-6 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>messenger_positions</code></span></div><div class="_yb">list&lt;enum{messenger_home, story}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-7 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>threads_positions</code></span></div><div class="_yb">list&lt;enum{threads_stream}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-8 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>genders</code></span></div><div class="_yb">list&lt;int64&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-9 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>publisher_platforms</code></span></div><div class="_yb">list&lt;enum{facebook, instagram, audience_network, messenger, oculus, whatsapp, threads}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-10 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>interests</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-10-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-10-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-11 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>behaviors</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-11-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-0-11-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>customization_spec</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_min</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_max</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-2 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>audience_network_positions</code></span></div><div class="_yb">list&lt;enum{classic, rewarded_video, instream_video}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-3 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>device_platforms</code></span></div><div class="_yb">list&lt;enum{desktop, mobile}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-4 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>facebook_positions</code></span></div><div class="_yb">list&lt;enum{feed, instant_article, instream_video, marketplace, right_hand_column, search, story, suggested_video, video_feeds, story_sticker, facebook_reels_overlay, biz_disco_feed, facebook_reels, profile_feed, profile_reels, notification}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-5 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>instagram_positions</code></span></div><div class="_yb">list&lt;enum{reels, reels_overlay, stream, story, explore, explore_home, ig_search, shop, profile_feed, profile_reels, effect_tray}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-6 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>genders</code></span></div><div class="_yb">list&lt;int64&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-7 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>publisher_platforms</code></span></div><div class="_yb">list&lt;enum{facebook, instagram, audience_network, messenger, oculus, whatsapp, threads}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-8 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>interests</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-8-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-8-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-9 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>behaviors</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-9-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-9-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-10 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>geo_locations</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-1-11 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>marketing_message_channels</code></span></div><div class="_yb">list&lt;enum{whatsapp, messenger}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>image_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>video_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>body_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>description_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_url_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-7 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>title_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>caption_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-9 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>call_to_action_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-10 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>priority</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-10-11 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>is_default</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>asset_customization_rules</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>targeting</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_min</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_max</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>audience_network_positions</code></span></div><div class="_yb">list&lt;enum{classic, rewarded_video, instream_video}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-3 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>device_platforms</code></span></div><div class="_yb">list&lt;enum{desktop, mobile}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-4 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>facebook_positions</code></span></div><div class="_yb">list&lt;enum{feed, instant_article, instream_video, marketplace, right_hand_column, search, story, suggested_video, video_feeds, story_sticker, facebook_reels_overlay, biz_disco_feed, facebook_reels, profile_feed, profile_reels, notification}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-5 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>instagram_positions</code></span></div><div class="_yb">list&lt;enum{reels, reels_overlay, stream, story, explore, explore_home, ig_search, shop, profile_feed, profile_reels, effect_tray}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-6 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>messenger_positions</code></span></div><div class="_yb">list&lt;enum{messenger_home, story}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-7 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>threads_positions</code></span></div><div class="_yb">list&lt;enum{threads_stream}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-8 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>genders</code></span></div><div class="_yb">list&lt;int64&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-9 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>publisher_platforms</code></span></div><div class="_yb">list&lt;enum{facebook, instagram, audience_network, messenger, oculus, whatsapp, threads}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-10 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>interests</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-10-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-10-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-11 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>behaviors</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-11-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-0-11-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>customization_spec</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_min</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>age_max</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-2 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>audience_network_positions</code></span></div><div class="_yb">list&lt;enum{classic, rewarded_video, instream_video}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-3 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>device_platforms</code></span></div><div class="_yb">list&lt;enum{desktop, mobile}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-4 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>facebook_positions</code></span></div><div class="_yb">list&lt;enum{feed, instant_article, instream_video, marketplace, right_hand_column, search, story, suggested_video, video_feeds, story_sticker, facebook_reels_overlay, biz_disco_feed, facebook_reels, profile_feed, profile_reels, notification}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-5 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>instagram_positions</code></span></div><div class="_yb">list&lt;enum{reels, reels_overlay, stream, story, explore, explore_home, ig_search, shop, profile_feed, profile_reels, effect_tray}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-6 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>genders</code></span></div><div class="_yb">list&lt;int64&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-7 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>publisher_platforms</code></span></div><div class="_yb">list&lt;enum{facebook, instagram, audience_network, messenger, oculus, whatsapp, threads}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-8 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>interests</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-8-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-8-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-9 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>behaviors</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-9-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-9-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-10 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>geo_locations</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-1-11 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>marketing_message_channels</code></span></div><div class="_yb">list&lt;enum{whatsapp, messenger}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>image_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>video_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>body_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>description_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_url_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-7 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>title_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>caption_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-9 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>call_to_action_label</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-10 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>priority</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-11-11 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>is_default</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-12 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>optimization_type</code></span></div><div class="_yb">enum{REGULAR, LANGUAGE, PLACEMENT, BRAND, LOCALIZED_PLACEMENTS, FORMAT_AUTOMATION, DOF_MESSAGING_DESTINATION, ACO_AUTOFLOW, MULTI_CREATOR, UNIFIED_PROFILE_VISIT_DESTINATION}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>call_to_actions</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum{BOOK_TRAVEL, CONTACT_US, DONATE, DONATE_NOW, DOWNLOAD, GET_DIRECTIONS, GO_LIVE, INTERESTED, LEARN_MORE, LIKE_PAGE, MESSAGE_PAGE, RAISE_MONEY, SAVE, SEND_TIP, SHOP_NOW, SIGN_UP, VIEW_INSTAGRAM_PROFILE, INSTAGRAM_MESSAGE, LOYALTY_LEARN_MORE, PURCHASE_GIFT_CARDS, PAY_TO_ACCESS, SEE_MORE, TRY_IN_CAMERA, WHATSAPP_LINK, GET_IN_TOUCH, BOOK_NOW, CHECK_AVAILABILITY, ORDER_NOW, WHATSAPP_MESSAGE, GET_MOBILE_APP, INSTALL_MOBILE_APP, USE_MOBILE_APP, INSTALL_APP, USE_APP, PLAY_GAME, WATCH_VIDEO, WATCH_MORE, OPEN_LINK, NO_BUTTON, LISTEN_MUSIC, MOBILE_DOWNLOAD, GET_OFFER, GET_OFFER_VIEW, BUY_NOW, BUY_TICKETS, UPDATE_APP, BET_NOW, ADD_TO_CART, SELL_NOW, GET_SHOWTIMES, LISTEN_NOW, GET_EVENT_TICKETS, REMIND_ME, SEARCH_MORE, PRE_REGISTER, SWIPE_UP_PRODUCT, SWIPE_UP_SHOP, PLAY_GAME_ON_FACEBOOK, VISIT_WORLD, OPEN_INSTANT_APP, JOIN_GROUP, GET_PROMOTIONS, SEND_UPDATES, INQUIRE_NOW, VISIT_PROFILE, CHAT_ON_WHATSAPP, EXPLORE_MORE, CONFIRM, JOIN_CHANNEL, MAKE_AN_APPOINTMENT, ASK_ABOUT_SERVICES, BOOK_A_CONSULTATION, GET_A_QUOTE, BUY_VIA_MESSAGE, ASK_FOR_MORE_INFO, CHAT_WITH_US, VIEW_PRODUCT, VIEW_CHANNEL, WATCH_LIVE_VIDEO, IMAGINE, CALL, MISSED_CALL, CALL_NOW, CALL_ME, APPLY_NOW, BUY, GET_QUOTE, SUBSCRIBE, RECORD_NOW, VOTE_NOW, GIVE_FREE_RIDES, REGISTER_NOW, OPEN_MESSENGER_EXT, EVENT_RSVP, CIVIC_ACTION, SEND_INVITES, REFER_FRIENDS, REQUEST_TIME, SEE_MENU, SEARCH, TRY_IT, TRY_ON, LINK_CARD, DIAL_CODE, FIND_YOUR_GROUPS, START_ORDER}</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_4-13-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>value</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-13-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>link</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>app_link</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-2 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>page</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-3 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>link_format</code></span></div><div class="_yb">enum {VIDEO_LEAD, VIDEO_LPP, VIDEO_NEKO, VIDEO_NON_LINK, VIDEO_SHOP, WHATSAPP_CATALOG_ATTACHMENT}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-4 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>application</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-5 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>link_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-13-1-6 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>link_description</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_4-13-1-7 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>link_caption</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-8 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>product_link</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-9 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>get_movie_showtimes</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-10 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>sponsorship</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-10-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-10-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>image</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>video_annotation</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>annotations</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>start_time_in_sec</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>end_time_in_sec</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0-3 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0-4 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link_description</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0-5 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link_caption</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-0-6 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>image_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>header_color</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>logo_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-3 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>post_click_cta_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-11-4 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>post_click_description_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-12 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>offer_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-13 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>offer_view_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-14 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>advanced_data</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-14-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>offer_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-15 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>lead_gen_form_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-16 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>referral_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-17 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>fundraiser_campaign_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-18 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>event_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-19 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>event_tour_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-20 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>app_destination</code></span></div><div class="_yb">enum {MESSENGER, MESSENGER_EXTENSIONS, MESSENGER_GAMES, LINK_CARD, MARKETPLACE, WHATSAPP, INSTAGRAM_DIRECT, INSTAGRAM_LIVE_VIDEO, FACEBOOK_LIVE_VIDEO}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-21 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>app_destination_page_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-22 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>is_canvas_video_transition_enabled</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-23 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>whatsapp_number</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-24 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>preinput_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-25 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>customized_message_page_cta_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-26 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>external_offer_provider_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-27 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>origins</code></span></div><div class="_yb">enum {COMPOSER, CAMERA}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-28 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>object_store_urls</code></span></div><div class="_yb">array&lt;string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>facebook_login_spec</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-29-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>facebook_login_app_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-29-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>offer_type</code></span></div><div class="_yb">enum {NO_OFFER, PERCENTAGE_BASED, AMOUNT_BASED}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-29-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>offer_pct_call_to_action</code></span></div><div class="_yb">enum {TEN}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-29-3 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>offer_amt_call_to_action</code></span></div><div class="_yb">enum {TEN}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-30 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>product_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-31 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>group_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-32 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>channel_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-1-33 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>land_on_whatsapp_catalog</code></span></div><div class="_yb">enum{1, 2}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-13-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-14 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>autotranslate</code></span></div><div class="_yb">array&lt;string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-15 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>additional_data</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-16 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>audios</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-16-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {OPTED_OUT, RANDOM, SELECTED}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-16-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>selected_audios</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-16-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>is_audio_swap</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-17 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>app_product_page_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>promotional_metadata</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>allowed_coupon_code_sources</code></span></div><div class="_yb">array&lt;enum {PROVIDED_BY_MERCHANT, PROVIDED_BY_MERCHANT_OFFER_MANAGEMENT, DETECTED_FROM_MERCHANT_ADS, DETECTED_FROM_MERCHANT_WEBSITE, DETECTED_FROM_MERCHANT_WEBSITE_URL, AD_CREATIVE_PRIMARY_TEXT, AD_CREATIVE_HEADLINE, AD_CREATIVE_DESCRIPTION, AD_CREATIVE_MANUAL_COUPON_CODES, EMAIL_CAPTURE_SHOPIFY, EMAIL_CAPTURE_NON_SHOPIFY, EMAIL_CAPTURE_GENERIC_CODE, WA_CAPTURE_GENERIC_CODE}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>allowed_promo_offer_ids</code></span></div><div class="_yb">array&lt;numeric string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>manual_coupon_codes</code></span></div><div class="_yb">array&lt;string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>is_auto_update_allowed</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>coupon_codes</code></span></div><div class="_yb">JSON object {enum {PROVIDED_BY_MERCHANT, PROVIDED_BY_MERCHANT_OFFER_MANAGEMENT, DETECTED_FROM_MERCHANT_ADS, DETECTED_FROM_MERCHANT_WEBSITE, DETECTED_FROM_MERCHANT_WEBSITE_URL, AD_CREATIVE_PRIMARY_TEXT, AD_CREATIVE_HEADLINE, AD_CREATIVE_DESCRIPTION, AD_CREATIVE_MANUAL_COUPON_CODES, EMAIL_CAPTURE_SHOPIFY, EMAIL_CAPTURE_NON_SHOPIFY, EMAIL_CAPTURE_GENERIC_CODE, WA_CAPTURE_GENERIC_CODE} : array&lt;string&gt;}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>offer_details</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-18-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>excluded_offers</code></span></div><div class="_yb">array&lt;string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-19 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>web_destination_spec</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-19-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-20 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>call_ads_configuration</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-20-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>auto_response</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-20-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>call_destination_type</code></span></div><div class="_yb">enum{PHONE, MESSENGER, MESSENGER_AND_PHONE, WEBSITE_AND_CALL}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-20-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>callback_type</code></span></div><div class="_yb">enum{FORM, NOT_ENABLED, MESSENGER}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-20-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>phone_number</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-20-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>recording_consent</code></span></div><div class="_yb">enum{TRANSCRIPTION_ENABLED, DISABLED}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-21 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>lead_gen_configuration</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-21-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>is_form_qa_enabled</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-21-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>is_work_email_enforcement_enabled</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_4-21-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>verification_type</code></span></div><div class="_yb">enum{NOT_ENABLED, SMS}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>authorization_category</code></span></div><div class="_yb">enum{NONE, POLITICAL, POLITICAL_WITH_DIGITALLY_CREATED_MEDIA}</div></td><td><p class="_yd"></p><div><div><p>Specifies whether ad is political or not. If your ad has political content, set this to <code>POLITICAL</code>, otherwise it defaults to <code>null</code>. Your ad will be disapproved if it contains political content but not labeled <code>POLITICAL</code>. See <a href="https://www.facebook.com/policies/ads">Facebook Advertising Policies</a>. This field cannot be used for <a href="https://developers.facebook.com/docs/marketing-api/dynamic-ad">Dynamic Ads</a>.</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>body</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>The body of the ad.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_7 _5m29 _5m27"><td><div class="_yc"><span><code>branded_content</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>branded_content</p>
</div></div><p></p></td></tr><tr class="row_7-0 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>partners</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div><p>partners</p>
</div></div><p></p></td></tr><tr class="row_7-0-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>fb_page_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>fb_page_id</p>
</div></div><p></p></td></tr><tr class="row_7-0-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>ig_user_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>ig_user_id</p>
</div></div><p></p></td></tr><tr class="row_7-0-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>ig_asset_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>ig_asset_id</p>
</div></div><p></p></td></tr><tr class="row_7-0-3 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>identity_type</code></span></div><div class="_yb">enum {ADVERTISER, PARTNER_BUSINESS, PARTNER_CREATOR}</div></td><td><p class="_yd"></p><div><div><p>identity_type</p>
</div></div><p></p></td></tr><tr class="row_7-0-4 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>has_create_ads_access</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>has_create_ads_access</p>
</div></div><p></p></td></tr><tr class="row_7-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>ui_version</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>ui_version</p>
</div></div><p></p></td></tr><tr class="row_7-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>instagram_boost_post_access_token</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>instagram_boost_post_access_token</p>
</div></div><p></p></td></tr><tr class="row_7-3 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>ad_format</code></span></div><div class="_yb">enum {0, 1, 2, 3}</div></td><td><p class="_yd"></p><div><div><p>ad_format</p>
</div></div><p></p></td></tr><tr class="row_7-4 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>creator_ad_permission_type</code></span></div><div class="_yb">enum {FB_ADS_PERMISSION, FB_ORGANIC_PERMISSION, FB_POST_PERMISSION, IG_ADS_PERMISSION, IG_ADS_PERMISSION_DIRECTION_AGNOSTIC, IG_ORGANIC_PERMISSION, IG_POST_PERMISSION, SAME_BUSINESS_PERMISSION}</div></td><td><p class="_yd"></p><div><div><p>creator_ad_permission_type</p>
</div></div><p></p></td></tr><tr class="row_7-5 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>is_mca_internal</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>is_mca_internal</p>
</div></div><p></p></td></tr><tr class="row_7-6 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>promoted_page_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>promoted_page_id</p>
</div></div><p></p></td></tr><tr class="row_7-7 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>facebook_boost_post_access_token</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>facebook_boost_post_access_token</p>
</div></div><p></p></td></tr><tr class="row_7-8 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>content_search_input</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>content_search_input</p>
</div></div><p></p></td></tr><tr class="row_7-9 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>testimonial</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>testimonial</p>
</div></div><p></p></td></tr><tr class="row_7-10 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>product_set_partner_selection_status</code></span></div><div class="_yb">enum {OPT_IN, OPT_OUT}</div></td><td><p class="_yd"></p><div><div><p>product_set_partner_selection_status</p>
</div></div><p></p></td></tr><tr class="row_7-11 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>parent_source_instagram_media_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>parent_source_instagram_media_id</p>
</div></div><p></p></td></tr><tr class="row_7-12 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>parent_source_facebook_post_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>parent_source_facebook_post_id</p>
</div></div><p></p></td></tr><tr class="row_7-13 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>testimonial_locale</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>testimonial_locale</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>branded_content_sponsor_page_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>ID for page representing business which runs Branded Content ads. See <a href="/docs/marketing-api/guides/branded-content">Creating Branded Content Ads</a>.</p>
</div></div><p></p></td></tr><tr class="row_9 _5m29"><td><div class="_yc"><span><code>bundle_folder_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The <a href="/docs/marketing-api/dynamic-product-ads">Dynamic Ad's</a> bundle folder ID</p>
</div></div><p></p></td></tr><tr class="row_10 _5m27"><td><div class="_yc"><span><code>call_to_action</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div><p>This field promotes an onsite or offsite action.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_10-0 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum{BOOK_TRAVEL, CONTACT_US, DONATE, DONATE_NOW, DOWNLOAD, GET_DIRECTIONS, GO_LIVE, INTERESTED, LEARN_MORE, LIKE_PAGE, MESSAGE_PAGE, RAISE_MONEY, SAVE, SEND_TIP, SHOP_NOW, SIGN_UP, VIEW_INSTAGRAM_PROFILE, INSTAGRAM_MESSAGE, LOYALTY_LEARN_MORE, PURCHASE_GIFT_CARDS, PAY_TO_ACCESS, SEE_MORE, TRY_IN_CAMERA, WHATSAPP_LINK, GET_IN_TOUCH, BOOK_NOW, CHECK_AVAILABILITY, ORDER_NOW, WHATSAPP_MESSAGE, GET_MOBILE_APP, INSTALL_MOBILE_APP, USE_MOBILE_APP, INSTALL_APP, USE_APP, PLAY_GAME, WATCH_VIDEO, WATCH_MORE, OPEN_LINK, NO_BUTTON, LISTEN_MUSIC, MOBILE_DOWNLOAD, GET_OFFER, GET_OFFER_VIEW, BUY_NOW, BUY_TICKETS, UPDATE_APP, BET_NOW, ADD_TO_CART, SELL_NOW, GET_SHOWTIMES, LISTEN_NOW, GET_EVENT_TICKETS, REMIND_ME, SEARCH_MORE, PRE_REGISTER, SWIPE_UP_PRODUCT, SWIPE_UP_SHOP, PLAY_GAME_ON_FACEBOOK, VISIT_WORLD, OPEN_INSTANT_APP, JOIN_GROUP, GET_PROMOTIONS, SEND_UPDATES, INQUIRE_NOW, VISIT_PROFILE, CHAT_ON_WHATSAPP, EXPLORE_MORE, CONFIRM, JOIN_CHANNEL, MAKE_AN_APPOINTMENT, ASK_ABOUT_SERVICES, BOOK_A_CONSULTATION, GET_A_QUOTE, BUY_VIA_MESSAGE, ASK_FOR_MORE_INFO, CHAT_WITH_US, VIEW_PRODUCT, VIEW_CHANNEL, WATCH_LIVE_VIDEO, IMAGINE, CALL, MISSED_CALL, CALL_NOW, CALL_ME, APPLY_NOW, BUY, GET_QUOTE, SUBSCRIBE, RECORD_NOW, VOTE_NOW, GIVE_FREE_RIDES, REGISTER_NOW, OPEN_MESSENGER_EXT, EVENT_RSVP, CIVIC_ACTION, SEND_INVITES, REFER_FRIENDS, REQUEST_TIME, SEE_MENU, SEARCH, TRY_IT, TRY_ON, LINK_CARD, DIAL_CODE, FIND_YOUR_GROUPS, START_ORDER}</div></td><td><p class="_yd"></p><div><div><p>The type of the action. Not all types can be used for all
      ads. Check
      <a href="https://www.facebook.com/business/ads-guide">Ads Product Guide</a>
      to see which type can be used for based on the <code>objective</code> of your
      campaign.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_10-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>value</code></span></div><div class="_yb">Object</div></td><td><div>Default value: <code>Vec</code></div><p class="_yd"></p><div><div><p>JSON containing the call to action data.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_10-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_link</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>page</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_format</code></span></div><div class="_yb">enum {VIDEO_LEAD, VIDEO_LPP, VIDEO_NEKO, VIDEO_NON_LINK, VIDEO_SHOP, WHATSAPP_CATALOG_ATTACHMENT}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-4 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>application</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-5 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_10-1-6 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_description</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_10-1-7 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_caption</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-8 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>product_link</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-9 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>get_movie_showtimes</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-10 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>sponsorship</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-10-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>link</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-10-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>image</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>video_annotation</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>annotations</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>start_time_in_sec</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>end_time_in_sec</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0-3 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0-4 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link_description</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0-5 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>link_caption</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-0-6 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>image_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>header_color</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-2 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>logo_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-3 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>post_click_cta_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-11-4 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>post_click_description_title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-12 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>offer_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-13 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>offer_view_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-14 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>advanced_data</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-14-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>offer_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-15 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>lead_gen_form_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-16 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>referral_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-17 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>fundraiser_campaign_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-18 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>event_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-19 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>event_tour_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-20 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_destination</code></span></div><div class="_yb">enum {MESSENGER, MESSENGER_EXTENSIONS, MESSENGER_GAMES, LINK_CARD, MARKETPLACE, WHATSAPP, INSTAGRAM_DIRECT, INSTAGRAM_LIVE_VIDEO, FACEBOOK_LIVE_VIDEO}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-21 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_destination_page_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-22 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>is_canvas_video_transition_enabled</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-23 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>whatsapp_number</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-24 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>preinput_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-25 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>customized_message_page_cta_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-26 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>external_offer_provider_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-27 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>origins</code></span></div><div class="_yb">enum {COMPOSER, CAMERA}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-28 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>object_store_urls</code></span></div><div class="_yb">array&lt;string&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>facebook_login_spec</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-29-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>facebook_login_app_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-29-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>offer_type</code></span></div><div class="_yb">enum {NO_OFFER, PERCENTAGE_BASED, AMOUNT_BASED}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-29-2 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>offer_pct_call_to_action</code></span></div><div class="_yb">enum {TEN}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-29-3 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>offer_amt_call_to_action</code></span></div><div class="_yb">enum {TEN}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-30 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>product_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-31 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>group_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-32 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>channel_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_10-1-33 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>land_on_whatsapp_catalog</code></span></div><div class="_yb">enum{1, 2}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_11 _5m29"><td><div class="_yc"><span><code>categorization_criteria</code></span></div><div class="_yb">enum{brand, category, product_type}</div></td><td><p class="_yd"></p><div><div><p>The <a href="/docs/marketing-api/dynamic-product-ads">Dynamic Category Ad's</a> categorization criteria</p>
</div></div><p></p></td></tr><tr class="row_12"><td><div class="_yc"><span><code>category_media_source</code></span></div><div class="_yb">enum{CATEGORY, MIXED, PRODUCTS_COLLAGE, PRODUCTS_SLIDESHOW}</div></td><td><p class="_yd"></p><div><div><p>The <a href="/docs/marketing-api/dynamic-product-ads">Dynamic Ad's</a> rendering mode for category ads</p>
</div></div><p></p></td></tr><tr class="row_13 _5m29 _5m27"><td><div class="_yc"><span><code>contextual_multi_ads</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>contextual_multi_ads</p>
</div></div><p></p></td></tr><tr class="row_13-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>eligibility</code></span></div><div class="_yb">array&lt;enum {}&gt;</div></td><td><p class="_yd"></p><div><div><p>eligibility</p>
</div></div><p></p></td></tr><tr class="row_13-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>enroll_status</code></span></div><div class="_yb">enum {OPT_IN, OPT_OUT}</div></td><td><p class="_yd"></p><div><div><p>enroll_status</p>
</div></div><p></p></td></tr><tr class="row_13-2 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>action_metadata</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>action_metadata</p>
</div></div><p></p></td></tr><tr class="row_13-2-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {DEFAULT, DEFAULT_OPT_IN, DEFAULT_OPT_OUT, DUPLICATION_UPGRADE, DUPLICATION_UPSELL, MANUAL, STICKY_OPT_IN, STICKY_OPT_OUT}</div></td><td><p class="_yd"></p><div><div><p>type</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_14 _5m27"><td><div class="_yc"><span><code>degrees_of_freedom_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>Specifies the type of transformation which is enabled for the given creative</p>
</div></div><p></p></td></tr><tr class="row_14-0 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>ad_handle_type</code></span></div><div class="_yb">enum{ORIGINAL, DYNAMIC}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14-1 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>degrees_of_freedom_type</code></span></div><div class="_yb">enum{DISABLED, USER_ENROLLED_AUTOFLOW, USER_ENROLLED_LWI_ACO, USER_ENROLLED_NON_DCO, USER_ENROLLED_IMAGE_CROPPING_NON_DCO, USER_ENROLLED, VIDEO_TEMPLATES, FAM_TOGGLE_ON, FAM_TOGGLE_OFF, SMART_CROP_ELIGIBLE_ON, SMART_CROP_ELIGIBLE_OFF, SMART_CROP_INELIGIBLE_ON, SMART_CROP_INELIGIBLE_OFF, VCK_MIXED_FORMAT}</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_14-2 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>multi_media_transformation_type</code></span></div><div class="_yb">enum{ADSET_IMAGES_TO_VIDEO, CAROUSEL_IMAGES_TO_VIDEO}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14-3 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>mobile_app_star_rating_enabled</code></span></div><div class="_yb">boolean</div></td><td><div>Default value: <code>false</code></div><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14-4 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>image_transformation_types</code></span></div><div class="_yb">list&lt;enum{CROPPING, ENHANCEMENT, COLOR_FILTERING, AUTOGEN_MIXED_FORMAT, FB_REELS_IMAGE_DEFAULT, IG_REELS_IMAGE_PORTAL}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14-5 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>text_transformation_types</code></span></div><div class="_yb">list&lt;enum{TEXT_LIQUIDITY}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14-6 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>video_transformation_types</code></span></div><div class="_yb">list&lt;enum{CROPPING, CROPPING_ACF, FB_REELS_VIDEO_DEFAULT, IG_REELS_VIDEO_PORTAL}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14-7 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>stories_transformation_types</code></span></div><div class="_yb">list&lt;enum{SMART_CROP, INSTAGRAM_PROFILE_CARD, INSTAGRAM_END_CARD, INSTAGRAM_MOBILE_APP_INSTALL_CARD, PORTAL}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14-8 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>creative_features_spec</code></span></div><div class="_yb">JSON object {enum {PRODUCT_METADATA_AUTOMATION, PROFILE_CARD, STANDARD_ENHANCEMENTS_CATALOG, VIDEO_TO_IMAGE} : JSON object}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_15 _5m29"><td><div class="_yc"><span><code>destination_set_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>The ID of the Product Set for a Destination Catalog that will be used to link with Travel Catalogs.</p>
</div></div><p></p></td></tr><tr class="row_16"><td><div class="_yc"><span><code>dynamic_ad_voice</code></span></div><div class="_yb">enum{DYNAMIC, STORY_OWNER}</div></td><td><p class="_yd"></p><div><div><p>Determines the Page voice to be used in <a href="/docs/marketing-api/guides/local-awareness">Dynamic Local Ads</a></p>
</div></div><p></p></td></tr><tr class="row_17 _5m29"><td><div class="_yc"><span><code>enable_direct_install</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>Whether Direct Install should be enabled on supported devices.</p>
</div></div><p></p></td></tr><tr class="row_18"><td><div class="_yc"><span><code>enable_launch_instant_app</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>Whether Instant App should be enabled on supported devices.</p>
</div></div><p></p></td></tr><tr class="row_19 _5m29 _5m27"><td><div class="_yc"><span><code>facebook_branded_content</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>Params required for facebook branded content.</p>
</div></div><p></p></td></tr><tr class="row_19-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>sponsor_page_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>sponsor_page_id</p>
</div></div><p></p></td></tr><tr class="row_19-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>shared_to_sponsor_status</code></span></div><div class="_yb">enum {NOT_SHARED, ALL_SHARED}</div></td><td><p class="_yd"></p><div><div><p>shared_to_sponsor_status</p>
</div></div><p></p></td></tr><tr class="row_20"><td><div class="_yc"><span><code>image_crops</code></span></div><div class="_yb">dictionary { enum{191x100, 100x72, 400x150, 600x360, 100x100, 400x500, 90x160} : &lt;list&lt;list&lt;int64&gt;&gt;&gt; }</div></td><td><p class="_yd"></p><div><div><p>Crop dimensions for the image specified. See <a href="/docs/reference/ads-api/image-crops/">image crop reference</a> for more details.</p>
</div></div><p></p></td></tr><tr class="row_21 _5m29"><td><div class="_yc"><span><code>image_file</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Reference to a local image file to upload for use in a creative. Not to exceed 8MB in size. If <code>object_story_spec</code> or <code>object_story_id</code> is specified, this field will be ignored</p>
</div></div><p></p></td></tr><tr class="row_22"><td><div class="_yc"><span><code>image_hash</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Image hash for an image you have <a href="/docs/marketing-api/adimage">uploaded</a> and can be used in a creative. If <code>object_story_spec</code> or <code>object_story_id</code> is specified, this field will be ignored</p>
</div></div><p></p></td></tr><tr class="row_23 _5m29"><td><div class="_yc"><span><code>image_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>URL for the image in this ad creative. Do not use image URLs returned by Facebook. Instead you should host the image on your own servers. Facebook saves the image from your URL to your <a href="/docs/marketing-api/adimage">ad account's image library</a>. Images cannot exceed 8 MB. You must also provide one of these three fields: <code>image_file</code>, <code>image_hash</code>, or <code>image_url</code>.</p>
</div></div><p></p></td></tr><tr class="row_24 _5m27"><td><div class="_yc"><span><code>instagram_branded_content</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>Params required for instagram branded content.</p>
</div></div><p></p></td></tr><tr class="row_24-0 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>sponsor_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_24-1 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>sponsor_asset_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_25 _5m29"><td><div class="_yc"><span><code>instagram_permalink_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>URL for a post on Instagram you want to run as an ad. Also known as Instagram media.</p>
</div></div><p></p></td></tr><tr class="row_26"><td><div class="_yc"><span><code>instagram_user_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Instagram user ID</p>
</div></div><p></p></td></tr><tr class="row_27 _5m29 _5m27"><td><div class="_yc"><span><code>interactive_components_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>The specification for interactive component overlay on the media.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_27-0 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>child_attachments</code></span></div><div class="_yb">list&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>components</code></span></div><div class="_yb">list&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-0 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>poll_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_27-0-0-0-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>option_a_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_27-0-0-0-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>option_b_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_27-0-0-0-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel4"><div class="_yc"><span><code>question_text</code></span></div><div class="_yb">string</div></td><td><div>Default value: <code>""</code></div><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>product_tag_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>position_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-3 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>link_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-4 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>feed_media_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-5 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>product_sticker_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-6 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>cta_sticker_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-0-0-7 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {7, 14, 9, 4, 8, 5, 3, 12, 1, 11, 10, 2}</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_27-0-0-8 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>enroll_status</code></span></div><div class="_yb">enum {OPT_IN, OPT_OUT}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>components</code></span></div><div class="_yb">list&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-0 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>poll_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_27-1-0-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>option_a_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_27-1-0-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>option_b_text</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_27-1-0-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>question_text</code></span></div><div class="_yb">string</div></td><td><div>Default value: <code>""</code></div><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>product_tag_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>position_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-3 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>link_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-4 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>feed_media_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-5 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>product_sticker_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-6 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>cta_sticker_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_27-1-7 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>type</code></span></div><div class="_yb">enum {7, 14, 9, 4, 8, 5, 3, 12, 1, 11, 10, 2}</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_27-1-8 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>enroll_status</code></span></div><div class="_yb">enum {OPT_IN, OPT_OUT}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_28"><td><div class="_yc"><span><code>link_og_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>The Open Graph (OG) ID for the link in this creative if the landing page has OG tags.</p>
</div></div><p></p></td></tr><tr class="row_29 _5m29"><td><div class="_yc"><span><code>link_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>Identify a specific landing tab on your Facebook page by the Page tab's URL. See <a href="/docs/reference/ads-api/connectionobjects/">connection objects</a> for retrieving Page tab URLs. You can add <a href="/docs/facebook-login/manually-build-a-login-flow">app_data</a> parameters to the URL to pass data to a Page's tab.</p>
</div></div><p></p></td></tr><tr class="row_30"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Name of this ad creative as seen in the ad account's library.</p>
</div></div><p></p></td></tr><tr class="row_31 _5m29"><td><div class="_yc"><span><code>object_id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>The Facebook object ID that is relevant to the ad. See <a href="/docs/reference/ads-api/connectionobjects/">connection objects</a></p>
</div></div><p></p></td></tr><tr class="row_32"><td><div class="_yc"><span><code>object_story_id</code></span></div><div class="_yb">post_id</div></td><td><p class="_yd"></p><div><div><p>ID of a Facebook Page post to use in an ad. You can get this ID by <a href="/docs/graph-api/reference/page/feed/">querying the posts of the page</a>. If this post includes an image, it should not exceed 8 MB. Facebook will upload the image from the post to your ad account's <a href="/docs/marketing-api/adimage">image library</a>.</p>
</div></div><p></p></td></tr><tr class="row_33 _5m29"><td><div class="_yc"><span><code>object_story_spec</code></span></div><div class="_yb">string (ObjectStorySpec)</div></td><td><p class="_yd"></p><div><div><p>JSON string of <a href="/docs/marketing-api/reference/ad-creative-object-story-spec/">AdCreativeObjectStorySpec</a> type. Use if you want to create a new unpublished page post and turn the post into an ad. The Page ID and the content to create a new unpublished page post. Specify <code>link_data</code>, <code>photo_data</code>, <code>video_data</code>, <code>text_data</code> or <code>template_data</code> with the content.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_34"><td><div class="_yc"><span><code>object_type</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>The type of Facebook object you want to advertise. Allowed values are:<br><code>PAGE</code><br><code>DOMAIN</code><br><code>EVENT</code><br><code>STORE_ITEM</code>: refers to an iTunes or Google Play store destination<br><code>OFFER</code><br><code>SHARE</code>: from a page<br><code>PHOTO</code><br><code>STATUS</code>: of a page<br><code>VIDEO</code><br><code>APPLICATION</code>: app on Facebook</p>
</div></div><p></p></td></tr><tr class="row_35 _5m29"><td><div class="_yc"><span><code>object_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>URL that opens if someone clicks your link on a link ad. This URL is not connected to a Facebook page.</p>
</div></div><p></p></td></tr><tr class="row_36"><td><div class="_yc"><span><code>page_welcome_message</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>You can create more tailored user experiences for your ads that click to Messenger or to WhatsApp by customizing your ads' greeting message. For ads that clicks to Whatsapp, you can set the the page_welcome_message field under object_story_spec.</p>

<p>Note: If you are using the message received in Whatsapp to trigger any bot flows, please make sure to work with your BSP and agencies to update it so as to ensure flows aren't disrupted.</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_37 _5m29"><td><div class="_yc"><span><code>place_page_set_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>The Place Page Set when objective is LOCAL_AWARENESS. Used with Dynamic Local Ads</p>
</div></div><p></p></td></tr><tr class="row_38 _5m27"><td><div class="_yc"><span><code>platform_customizations</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div><p>Use this field to specify the exact media to use on different Facebook <a href="/docs/marketing-api/targeting-specs/#placement">placements</a>. You can currently use this setting for images and videos. Facebook replaces the media originally defined in ad creative with this media when the ad displays in a specific placements. For example, if you define a media here for <code>instagram</code>, Facebook uses that media instead of the media defined in the ad creative when the ad appears on Instagram.</p>
</div></div><p></p></td></tr><tr class="row_38-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>instagram</code></span></div><div class="_yb">JSON or object-like arrays</div></td><td><p class="_yd"></p><div><div><p>Specify the media to display in an Instagram ad. This displays instead
       of the media defined in the ad creative.</p>
</div></div><p></p></td></tr><tr class="row_38-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>image_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>The URL of the image used for the
        platform specific media. Either this field or <code>image_hash</code> is
        required.</p>
</div></div><p></p></td></tr><tr class="row_38-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>image_hash</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>The
        <a href="/docs/marketing-api/reference/ad-image">ad image</a> used for the
        platform specific media. Either this field or <code>image_url</code> is required.</p>
</div></div><p></p></td></tr><tr class="row_38-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>image_crops</code></span></div><div class="_yb">dictionary { enum{191x100, 100x72, 400x150, 600x360, 100x100, 400x500, 90x160} : &lt;list&lt;list&lt;int64&gt;&gt;&gt; }</div></td><td><p class="_yd"></p><div><div><p>A JSON object defining crop
        dimensions for the image specified. See
        <a href="/docs/marketing-api/image-crops">Image Crops</a> for more details.</p>
</div></div><p></p></td></tr><tr class="row_39 _5m29"><td><div class="_yc"><span><code>playable_asset_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>The ID of the playable asset in this creative.</p>
</div></div><p></p></td></tr><tr class="row_40 _5m27"><td><div class="_yc"><span><code>portrait_customizations</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>Use this field to customizations how ads look in portrait mode format example for IG Stories, Facebook Stories, IGTV, etc</p>
</div></div><p></p></td></tr><tr class="row_40-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>specifications</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div><p>specifications</p>
</div></div><p></p></td></tr><tr class="row_40-0-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>background_color</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>background_color</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_40-0-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>bottom_color</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>bottom_color</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_40-0-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel3"><div class="_yc"><span><code>top_color</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>top_color</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_40-1 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>carousel_delivery_mode</code></span></div><div class="_yb">enum {fixed_num_cards, optimal_num_cards}</div></td><td><p class="_yd"></p><div><div><p>carousel_delivery_mode</p>
</div></div><p></p></td></tr><tr class="row_41 _5m29"><td><div class="_yc"><span><code>product_set_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Used for <a href="/docs/marketing-api/dynamic-product-ads">Dynamic Ad</a>. An ID for a product set, which groups related products or other items being advertised.</p>
</div></div><p></p></td></tr><tr class="row_42 _5m27"><td><div class="_yc"><span><code>recommender_settings</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>The recommender settings that can be used to control recommendations for Dynamic Ads.</p>
</div></div><p></p></td></tr><tr class="row_42-0 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>preferred_events</code></span></div><div class="_yb">list&lt;enum{ViewContent, Search, AddToCart, AddToWishlist, InitiateCheckout, AddPaymentInfo, Purchase, Subscribe, RecurringSubscriptionPayment, Lead, CompleteRegistration, CustomConversion, AggregateCustomConversion, Other}&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_42-1 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>product_sales_channel</code></span></div><div class="_yb">enum {ONLINE, IN_STORE, OMNI}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_43 _5m29"><td><div class="_yc"><span><code>referral_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of Referral Ad Configuration in this creative.</p>
</div></div><p></p></td></tr><tr class="row_44 _5m27"><td><div class="_yc"><span><code>regional_regulation_disclaimer_spec</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>regional_regulation_disclaimer_spec</p>
</div></div><p></p></td></tr><tr class="row_44-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>taiwan_finserv</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>taiwan_finserv</p>
</div></div><p></p></td></tr><tr class="row_44-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>taiwan_finserv_funder_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>taiwan_finserv_funder_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>australia_finserv</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>australia_finserv</p>
</div></div><p></p></td></tr><tr class="row_44-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>australia_finserv_beneficiary_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>australia_finserv_beneficiary_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>australia_finserv_payer_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>australia_finserv_payer_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-2 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>taiwan_universal</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>taiwan_universal</p>
</div></div><p></p></td></tr><tr class="row_44-2-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>taiwan_universal_beneficiary_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>taiwan_universal_beneficiary_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-2-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>taiwan_universal_payer_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>taiwan_universal_payer_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-3 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>india_finserv</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>india_finserv</p>
</div></div><p></p></td></tr><tr class="row_44-3-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>india_finserv_beneficiary_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>india_finserv_beneficiary_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-3-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>india_finserv_payer_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>india_finserv_payer_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-4 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>singapore_universal</code></span></div><div class="_yb">JSON object</div></td><td><p class="_yd"></p><div><div><p>singapore_universal</p>
</div></div><p></p></td></tr><tr class="row_44-4-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>singapore_universal_beneficiary_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>singapore_universal_beneficiary_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_44-4-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>singapore_universal_payer_id</code></span></div><div class="_yb">numeric string</div></td><td><p class="_yd"></p><div><div><p>singapore_universal_payer_id</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_45 _5m29"><td><div class="_yc"><span><code>template_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>The product link url, which overrides the one set in <a href="/docs/marketing-api/dynamic-product-ads">Dynamic Product Ad's</a> product feeds.</p>
</div></div><p></p></td></tr><tr class="row_46 _5m27"><td><div class="_yc"><span><code>template_url_spec</code></span></div><div class="_yb">string (TemplateURLSpec)</div></td><td><p class="_yd"></p><div><div><p>An optional structured collection of templated web and app-link descriptors that override the fallbacks that would otherwise be pulled from a Dynamic Ad`s catalog</p>
</div></div><p></p></td></tr><tr class="row_46-0 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>android</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-0-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-0-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>package</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-0-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-1 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>config</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-1-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-1-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>third_party_app_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-2 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>ios</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-2-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-2-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_store_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-2-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-3 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>ipad</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-3-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-3-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_store_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-3-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-4 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>iphone</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-4-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-4-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_store_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-4-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-5 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>windows_phone</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-5-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-5-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app_id</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-5-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-6 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>web</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-6-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_46-6-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>should_fallback</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_47 _5m29"><td><div class="_yc"><span><code>threads_user_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>threads_user_id</p>
</div></div><p></p></td></tr><tr class="row_48"><td><div class="_yc"><span><code>thumbnail_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>URL for a thumbnail image for this ad creative. You can provide dimensions for this with <code>thumbnail_width</code> and <code>thumbnail_height</code>. <a href="/docs/marketing-api/reference/ad-creative#thumbnail-example">See example</a>.</p>
</div></div><p></p></td></tr><tr class="row_49 _5m29"><td><div class="_yc"><span><code>title</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Title for a Page Likes ad, which appears in the right-hand column on Facebook.</p>
</div></div><p></p></td></tr><tr class="row_50"><td><div class="_yc"><span><code>url_tags</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>A set of query string parameters which will replace or be appended to urls clicked from page post ads, message of the post, and canvas app install creatives only.</p>
</div></div><p></p></td></tr><tr class="row_51 _5m29"><td><div class="_yc"><span><code>use_page_actor_override</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>If <code>true</code>, we show the page actor for mobile app ads.</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div><div class="_uoj"><code>success</code>: bool, </div>}</div><h3 id="error-codes-2">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>500</td><td>Message contains banned content</td></tr><tr><td>1500</td><td>The url you supplied is invalid</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>105</td><td>The number of parameters exceeded the maximum for this operation</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>194</td><td>Missing at least one required parameter</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr></tbody></table></div></div></div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Deleting">Deleting</h2><div class="_844_">You can't perform this operation on this endpoint.</div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div></div><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '675141479195042');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '574561515946252');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '1754628768090156');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '217404712025032');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1" /></noscript></div></div></div>