{"title": "Facebook Marketing API - Ad Account Activities Reference", "summary": "Reference documentation for the Ad Account Activities endpoint in Facebook's Marketing API. This endpoint allows reading activities related to an ad account but does not support create, update, or delete operations.", "content": "# Ad Account Activities\n\n## Overview\n\nThe Ad Account Activities endpoint provides access to activities related to an Ad Account in the Facebook Marketing API.\n\n## Reading\n\nActivities related to an Ad Account can be retrieved through this endpoint.\n\n### Parameters\n\nThis endpoint doesn't have any parameters.\n\n### Response Format\n\nReading from this edge will return a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### `data`\n\nA list of [AdActivity](/docs/marketing-api/reference/ad-activity/) nodes.\n\n#### `paging`\n\nFor more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).\n\n### Error Codes\n\n| Error | Description |\n|-------|-------------|\n| 200 | Permissions error |\n| 80004 | There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management. |\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 368 | The action attempted has been deemed abusive or is otherwise disallowed |\n\n## Creating\n\nYou can't perform this operation on this endpoint.\n\n## Updating\n\nYou can't perform this operation on this endpoint.\n\n## Deleting\n\nYou can't perform this operation on this endpoint.\n\n## API Version\n\nGraph API Version: v23.0", "keyPoints": ["This endpoint only supports READ operations - no create, update, or delete functionality", "Returns a list of AdActivity nodes with standard pagination", "No parameters are required for this endpoint", "Rate limiting applies with specific error code 80004 for too many calls", "Requires proper OAuth 2.0 access token for authentication"], "apiEndpoints": ["/ad-account/activities"], "parameters": [], "examples": ["{\n    \"data\": [],\n    \"paging\": {}\n}"], "tags": ["Facebook Marketing API", "Ad Account", "Activities", "Graph API", "Read-only endpoint"], "relatedTopics": ["AdActivity reference", "Graph API pagination", "Rate limiting", "OAuth 2.0 Access Tokens", "Ad Account management"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/activities/", "processedAt": "2025-06-25T16:10:54.243Z", "processor": "openrouter-claude-sonnet-4"}