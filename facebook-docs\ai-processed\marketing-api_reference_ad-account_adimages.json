{"title": "Facebook Marketing API - Ad Account Ad Images Reference", "summary": "Complete reference documentation for managing ad images within Facebook Ad Accounts through the Marketing API. Covers reading, creating, and deleting ad images with detailed parameters, examples, and error handling.", "content": "# Ad Account Ad Images\n\nManage ad images that belong to a Facebook Ad Account through the Marketing API.\n\n## Reading Ad Images\n\nRetrieve ad images associated with an ad account.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/adimages\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `biz_tag_id` | int64 | Business tag ID to filter images |\n| `business_id` | numeric string or integer | Optional. Assists with filters such as recently used |\n| `hashes` | list<string> | Hash of the image |\n| `minheight` | int64 | Minimum height of the image |\n| `minwidth` | int64 | Minimum width of the image |\n| `name` | string | Image name used in image names filter |\n\n### Response Format\n\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Data Field\nA list of AdImage nodes.\n\n#### Summary Field\n- `total_count` (int32): Total number of images in the Ad Account\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/adimages HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/adimages',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/adimages\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n## Creating Ad Images\n\nUpload new ad images to an ad account.\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/adimages\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `bytes` | Base64 UTF-8 string | Image file content in bytes format |\n| `copy_from` | JSON object | Copy Ad Image from source to destination account |\n| `source_account_id` | numeric string | Source account ID for copying |\n| `hash` | string | Hash of the image to copy |\n\n### Copy From Object Structure\n```json\n{\n  \"source_account_id\": \"<SOURCE_ACCOUNT_ID>\",\n  \"hash\": \"02bee5277ec507b6fd0f9b9ff2f22d9c\"\n}\n```\n\n### Return Type\n\nReturns a map structure with image details:\n\n```json\n{\n  \"images\": {\n    \"<filename>\": {\n      \"hash\": \"string\",\n      \"url\": \"string\",\n      \"url_128\": \"string\",\n      \"url_256\": \"string\",\n      \"url_256_height\": \"string\",\n      \"url_256_width\": \"string\",\n      \"height\": \"int32\",\n      \"width\": \"int32\",\n      \"name\": \"string\"\n    }\n  }\n}\n```\n\n## Deleting Ad Images\n\nRemove ad images from an ad account.\n\n### Endpoint\n```\nDELETE /act_{ad_account_id}/adimages\n```\n\n### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|----------|\n| `hash` | string | Hash of the image to delete | Yes |\n\n### Return Type\n\n```json\n{\n  \"success\": true\n}\n```\n\n## Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | The action attempted has been deemed abusive or is otherwise disallowed |\n| 2500 | Error parsing graph query |\n| 2635 | You are calling a deprecated version of the Ads API |\n| 80004 | Too many calls to this ad-account. Rate limiting applied |\n\n## Notes\n\n- Updating ad images is not supported through this endpoint\n- The API supports read-after-write for creation operations\n- All operations require proper OAuth 2.0 access tokens\n- Rate limiting applies to prevent abuse", "keyPoints": ["Supports reading, creating, and deleting ad images within Facebook Ad Accounts", "Provides filtering options by business tag, dimensions, name, and hash", "Supports copying images between ad accounts using source account ID and hash", "Returns detailed image metadata including URLs, dimensions, and hash values", "Implements rate limiting and comprehensive error handling"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/adimages", "POST /act_{ad_account_id}/adimages", "DELETE /act_{ad_account_id}/adimages"], "parameters": ["biz_tag_id", "business_id", "hashes", "minheight", "minwidth", "name", "bytes", "copy_from", "source_account_id", "hash"], "examples": ["HTTP GET request for retrieving ad images", "PHP SDK implementation for reading images", "JavaScript SDK API call", "Android SDK GraphRequest example", "iOS SDK FBSDKGraphRequest implementation", "JSON structure for copying images between accounts"], "tags": ["Facebook Marketing API", "Ad Images", "Ad Account", "Image Management", "Graph API", "REST API"], "relatedTopics": ["AdImage reference", "AdAccount reference", "Graph API pagination", "OAuth 2.0 authentication", "Rate limiting", "Error handling", "Image uploading", "Base64 encoding"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adimages/", "processedAt": "2025-06-25T16:12:06.928Z", "processor": "openrouter-claude-sonnet-4"}