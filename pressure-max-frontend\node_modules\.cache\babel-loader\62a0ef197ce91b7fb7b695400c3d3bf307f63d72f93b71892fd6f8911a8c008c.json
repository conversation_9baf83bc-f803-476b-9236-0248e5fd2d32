{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '../lib/supabase';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [session, setSession] = useState(null);\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const {\n        data: {\n          session\n        },\n        error\n      } = await supabase.auth.getSession();\n      if (error) {\n        console.error('Error getting session:', error);\n      } else {\n        var _session$user;\n        setSession(session);\n        setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n      }\n      setLoading(false);\n    };\n    getInitialSession();\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      var _session$user2;\n      console.log('Auth state changed:', event, session);\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      setLoading(false);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const login = async credentials => {\n    try {\n      setLoading(true);\n      const response = await authAPI.login(credentials);\n      const {\n        user: userData,\n        tokens\n      } = response.data;\n\n      // Store tokens and user data\n      localStorage.setItem('accessToken', tokens.accessToken);\n      localStorage.setItem('refreshToken', tokens.refreshToken);\n      localStorage.setItem('user', JSON.stringify(userData));\n      setUser(userData);\n      setIsAuthenticated(true);\n      toast.success('Login successful!');\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const register = async userData => {\n    try {\n      setLoading(true);\n      const response = await authAPI.register(userData);\n      const {\n        user: newUser,\n        tokens\n      } = response.data;\n\n      // Store tokens and user data\n      localStorage.setItem('accessToken', tokens.accessToken);\n      localStorage.setItem('refreshToken', tokens.refreshToken);\n      localStorage.setItem('user', JSON.stringify(newUser));\n      setUser(newUser);\n      setIsAuthenticated(true);\n      toast.success('Registration successful!');\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear local storage\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n      setUser(null);\n      setIsAuthenticated(false);\n      toast.success('Logged out successfully');\n    }\n  };\n  const getToken = () => {\n    return localStorage.getItem('accessToken');\n  };\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"k8IDfYqdQDRIjOgFgI8DROyNge4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "supabase", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "session", "setSession", "getInitialSession", "data", "error", "auth", "getSession", "console", "_session$user", "subscription", "onAuthStateChange", "event", "_session$user2", "log", "unsubscribe", "login", "credentials", "response", "authAPI", "userData", "tokens", "localStorage", "setItem", "accessToken", "refreshToken", "JSON", "stringify", "setIsAuthenticated", "success", "_error$response", "_error$response$data", "message", "register", "newUser", "_error$response2", "_error$response2$data", "logout", "removeItem", "getToken", "getItem", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '../lib/supabase';\nimport toast from 'react-hot-toast';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [session, setSession] = useState(null);\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session }, error } = await supabase.auth.getSession();\n      if (error) {\n        console.error('Error getting session:', error);\n      } else {\n        setSession(session);\n        setUser(session?.user ?? null);\n      }\n      setLoading(false);\n    };\n\n    getInitialSession();\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session);\n        setSession(session);\n        setUser(session?.user ?? null);\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const login = async (credentials) => {\n    try {\n      setLoading(true);\n      const response = await authAPI.login(credentials);\n      const { user: userData, tokens } = response.data;\n\n      // Store tokens and user data\n      localStorage.setItem('accessToken', tokens.accessToken);\n      localStorage.setItem('refreshToken', tokens.refreshToken);\n      localStorage.setItem('user', JSON.stringify(userData));\n\n      setUser(userData);\n      setIsAuthenticated(true);\n      \n      toast.success('Login successful!');\n      return { success: true, data: response.data };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Login failed';\n      toast.error(message);\n      return { success: false, error: message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      setLoading(true);\n      const response = await authAPI.register(userData);\n      const { user: newUser, tokens } = response.data;\n\n      // Store tokens and user data\n      localStorage.setItem('accessToken', tokens.accessToken);\n      localStorage.setItem('refreshToken', tokens.refreshToken);\n      localStorage.setItem('user', JSON.stringify(newUser));\n\n      setUser(newUser);\n      setIsAuthenticated(true);\n      \n      toast.success('Registration successful!');\n      return { success: true, data: response.data };\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed';\n      toast.error(message);\n      return { success: false, error: message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // Clear local storage\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('user');\n      \n      setUser(null);\n      setIsAuthenticated(false);\n      \n      toast.success('Logged out successfully');\n    }\n  };\n\n  const getToken = () => {\n    return localStorage.getItem('accessToken');\n  };\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    register,\n    logout,\n    getToken,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,MAAM;QAAEC,IAAI,EAAE;UAAEH;QAAQ,CAAC;QAAEI;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAACC,UAAU,CAAC,CAAC;MACrE,IAAIF,KAAK,EAAE;QACTG,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD,CAAC,MAAM;QAAA,IAAAI,aAAA;QACLP,UAAU,CAACD,OAAO,CAAC;QACnBH,OAAO,EAAAW,aAAA,GAACR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAY,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;MAChC;MACAT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDG,iBAAiB,CAAC,CAAC;;IAEnB;IACA,MAAM;MAAEC,IAAI,EAAE;QAAEM;MAAa;IAAE,CAAC,GAAGzB,QAAQ,CAACqB,IAAI,CAACK,iBAAiB,CAChE,OAAOC,KAAK,EAAEX,OAAO,KAAK;MAAA,IAAAY,cAAA;MACxBL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAEF,KAAK,EAAEX,OAAO,CAAC;MAClDC,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAAe,cAAA,GAACZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAgB,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAC9Bb,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAMU,YAAY,CAACK,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,QAAQ,GAAG,MAAMC,OAAO,CAACH,KAAK,CAACC,WAAW,CAAC;MACjD,MAAM;QAAEpB,IAAI,EAAEuB,QAAQ;QAAEC;MAAO,CAAC,GAAGH,QAAQ,CAACd,IAAI;;MAEhD;MACAkB,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEF,MAAM,CAACG,WAAW,CAAC;MACvDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEF,MAAM,CAACI,YAAY,CAAC;MACzDH,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEG,IAAI,CAACC,SAAS,CAACP,QAAQ,CAAC,CAAC;MAEtDtB,OAAO,CAACsB,QAAQ,CAAC;MACjBQ,kBAAkB,CAAC,IAAI,CAAC;MAExB1C,KAAK,CAAC2C,OAAO,CAAC,mBAAmB,CAAC;MAClC,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEzB,IAAI,EAAEc,QAAQ,CAACd;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAyB,eAAA,EAAAC,oBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAzB,KAAK,CAACa,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB1B,IAAI,cAAA2B,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,cAAc;MAC/D9C,KAAK,CAACmB,KAAK,CAAC2B,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAExB,KAAK,EAAE2B;MAAQ,CAAC;IAC3C,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,QAAQ,GAAG,MAAOb,QAAQ,IAAK;IACnC,IAAI;MACFpB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,QAAQ,GAAG,MAAMC,OAAO,CAACc,QAAQ,CAACb,QAAQ,CAAC;MACjD,MAAM;QAAEvB,IAAI,EAAEqC,OAAO;QAAEb;MAAO,CAAC,GAAGH,QAAQ,CAACd,IAAI;;MAE/C;MACAkB,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEF,MAAM,CAACG,WAAW,CAAC;MACvDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEF,MAAM,CAACI,YAAY,CAAC;MACzDH,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEG,IAAI,CAACC,SAAS,CAACO,OAAO,CAAC,CAAC;MAErDpC,OAAO,CAACoC,OAAO,CAAC;MAChBN,kBAAkB,CAAC,IAAI,CAAC;MAExB1C,KAAK,CAAC2C,OAAO,CAAC,0BAA0B,CAAC;MACzC,OAAO;QAAEA,OAAO,EAAE,IAAI;QAAEzB,IAAI,EAAEc,QAAQ,CAACd;MAAK,CAAC;IAC/C,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACd,MAAMJ,OAAO,GAAG,EAAAG,gBAAA,GAAA9B,KAAK,CAACa,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/B,IAAI,cAAAgC,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MACtE9C,KAAK,CAACmB,KAAK,CAAC2B,OAAO,CAAC;MACpB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAExB,KAAK,EAAE2B;MAAQ,CAAC;IAC3C,CAAC,SAAS;MACRhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMlB,OAAO,CAACkB,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACAiB,YAAY,CAACgB,UAAU,CAAC,aAAa,CAAC;MACtChB,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;MACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;MAE/BxC,OAAO,CAAC,IAAI,CAAC;MACb8B,kBAAkB,CAAC,KAAK,CAAC;MAEzB1C,KAAK,CAAC2C,OAAO,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC;EAED,MAAMU,QAAQ,GAAGA,CAAA,KAAM;IACrB,OAAOjB,YAAY,CAACkB,OAAO,CAAC,aAAa,CAAC;EAC5C,CAAC;EAED,MAAMC,KAAK,GAAG;IACZ5C,IAAI;IACJE,OAAO;IACP2C,eAAe;IACf1B,KAAK;IACLiB,QAAQ;IACRI,MAAM;IACNE;EACF,CAAC;EAED,oBACEnD,OAAA,CAACC,WAAW,CAACsD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA9C,QAAA,EAChCA;EAAQ;IAAAiD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACnD,GAAA,CAxHWF,YAAY;AAAAsD,EAAA,GAAZtD,YAAY;AAAA,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}