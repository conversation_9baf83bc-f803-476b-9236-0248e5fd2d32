{"title": "Overview", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_6_ik\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Overview</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#how-it-works\">How it Works</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad-campaigns\">Ad campaigns</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad-sets\">Ad sets</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad-creatives\">Ad creatives</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#ads\">Ads</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#ad-components\">Ad Components</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_0_IN\"><div class=\"_4cel\"><span data-click-area=\"main\"><div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\">Overview</h1>\n\n<p>The Marketing API is a Meta business tool designed to empower developers and marketers with the ability to automate advertising efforts across Meta technologies. It offers a comprehensive suite of functionalities that streamline the processes of ad creation, management, and performance analysis.</p>\n\n<p>One of the primary features of the Marketing API is its ability to facilitate the automated creation of ads. You can programmatically generate ad campaigns, ad sets, and individual ads, allowing for rapid deployment and iteration based on real-time performance data. This automation also enables businesses to reach larger audiences with greater efficiency.</p>\n\n<p>In addition to ad creation, you can:</p>\n\n<ul>\n<li>Update, pause, or delete ads seamlessly</li>\n<li>Ensure that campaigns remain aligned with business objectives</li>\n<li>Access detailed insights and analytics to track  ad performance and make data-driven decisions to improve outcomes</li>\n</ul>\n</div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"how-it-works\">How it Works</h2>\n<div style=\"text-align:center;\"><img class=\"img\" src=\"https://scontent-phx1-1.xx.fbcdn.net/v/t39.8562-6/465922728_1274126690433578_4616558889838468033_n.png?_nc_cat=103&amp;ccb=1-7&amp;_nc_sid=f537c7&amp;_nc_ohc=N9X76CpW_hoQ7kNvwHRgo0d&amp;_nc_oc=AdnmTPUAPRM07qkO-7kjW8uak9DuW9JkQsiQ732GX0cjSY4I5SM_H9f1LgiQn6oSHxM&amp;_nc_zt=14&amp;_nc_ht=scontent-phx1-1.xx&amp;_nc_gid=cdOLrL3kR-2sXcvKziz4bQ&amp;oh=00_AfNGsD0N3Hr38-ru2z61UbSs7vKINAJ7BYaZ0GKuk4Hyfg&amp;oe=6861DBE5\" width=\"600px\" alt=\"\"></div><h3 id=\"ad-campaigns\">Ad campaigns</h3>\n\n<p>A campaign is the highest level organizational structure within an ad account and should represent a single objective, for example, to drive Page post engagement. Setting the objective of the campaign enforces validation on any ads added to that campaign to ensure they also have the correct objective.</p>\n\n<h3 id=\"ad-sets\">Ad sets</h3>\n\n<p>Ad sets are groups of ads and are used to configure the budget and period the ads should run for. All ads contained within an ad set should have the same targeting, budget, billing, optimization goal, and duration.</p>\n\n<p>Create an ad set for each target audience with your bid; ads in the set target the same audience with the same bid. This helps control the amount you spend on each audience, determine when the audience will see your ads, and provides metrics for each audience.</p>\n\n<h3 id=\"ad-creatives\">Ad creatives</h3>\n\n<p>Ad creatives contain just the visual elements of the ad and you can't change them once they're created. Each ad account has a creative library to store creatives for reuse in ads.</p>\n\n<h3 id=\"ads\">Ads</h3>\n\n<p>An ad object contains all of the information necessary to display an ad on Facebook, Instagram, Messenger, and WhatsApp, including the ad creative. Create multiple ads in each ad set to optimize ad delivery based on different images, links, video, text, or placements.</p>\n\n<h3 id=\"ad-components\">Ad Components</h3>\n\n<p>This table shows how the various ad components align to the different levels of ad creation.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th></th><th>Ad Campaign</th><th>Ad Set</th><th>Ad</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_1_DJ\"><tr class=\"row_0\"><td><p><strong>Objective</strong></p>\n</td><td><div style=\"color:green;text-align:center;font-size:22pt;\">✓</div></td><td></td><td></td></tr><tr class=\"row_1 _5m29\"><td><p><strong>Schedule</strong></p>\n</td><td></td><td><div style=\"color:green;text-align:center;font-size:22pt;\">✓</div></td><td></td></tr><tr class=\"row_2\"><td><p><strong>Budget</strong></p>\n</td><td></td><td><div style=\"color:green;text-align:center;font-size:22pt;\">✓</div></td><td></td></tr><tr class=\"row_3 _5m29\"><td><p><strong>Bidding</strong></p>\n</td><td></td><td><div style=\"color:green;text-align:center;font-size:22pt;\">✓</div></td><td></td></tr><tr class=\"row_4\"><td><p><strong>Audience</strong></p>\n</td><td></td><td><div style=\"color:green;text-align:center;font-size:22pt;\">✓</div></td><td></td></tr><tr class=\"row_5 _5m29\"><td><p><strong>Ad Creative</strong></p>\n</td><td></td><td></td><td><div style=\"color:green;text-align:center;font-size:22pt;\">✓</div></td></tr></tbody></table></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div></span><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p _4_k\"><fb:like href=\"https://developers.facebook.com/docs/marketing-apis/overview/\" layout=\"button_count\" share=\"1\"></fb:like><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div id=\"developer_documentation_toolbar\" data-referrer=\"developer_documentation_toolbar\" data-click-area=\"toolbar\"></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-apis/overview", "/docs/marketing-api/overview/versioning", "/docs/marketing-api/overview/rate-limiting", "/docs/marketing-api/overview/data-processing-options", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/marketing-api-changelog"], "url": "https://developers.facebook.com/docs/marketing-apis/overview", "timestamp": "2025-06-25T15:04:40.101Z"}