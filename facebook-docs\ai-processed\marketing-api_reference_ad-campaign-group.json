{"title": "Facebook Marketing API Campaign Reference", "summary": "Complete reference documentation for the Facebook Marketing API Campaign object, which represents the highest level organizational structure within an ad account. Covers reading, creating, updating, and deleting campaigns, along with field definitions, parameters, and objective validation rules.", "content": "# Campaign\n\nA campaign is the highest level organizational structure within an ad account and should represent a single objective for an advertiser, for example, to drive page post engagement. Setting objective of the campaign will enforce validation on any ads added to the campaign to ensure they also have the correct objective.\n\n## Important Notes\n\n- Facebook will no longer be able to aggregate non-inline conversion metric values across iOS 14.5 and non-iOS 14.5 campaigns due to differences in attribution logic\n- Ad campaigns that target iOS 14.5 must set the new `is_skadnetwork_attribution` field to `true`\n- The `date_preset = lifetime` parameter is disabled in Graph API v10.0 and replaced with `date_preset = maximum`, which returns a maximum of 37 months of data\n\n## Limits\n\n- You can only create 200 ad sets per ad campaign\n- If your campaign has more than 70 ad sets and uses Campaign Budget Optimization, you are not able to edit your current bid strategy or turn off CBO\n\n## Special Ad Categories\n\nAll businesses using the Marketing API must identify whether or not new and edited campaigns belong to a Special Ad Category. Current available categories are: housing, employment, credit, or issues, elections, and politics. Businesses whose ads do not belong to a Special Ad Category must indicate NONE or send an empty array in the `special_ad_categories` field.\n\n## Reading\n\nA campaign is a grouping of ad sets which are organized by the same business objective. Each campaign has an objective that must be valid across the ad sets within that campaign.\n\n### Parameters\n\n| Parameter | Description |\n|-----------|-------------|\n| `date_preset` | enum{today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year} |\n| `time_range` | {'since':YYYY-MM-DD,'until':YYYY-MM-DD} |\n\n### Fields\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `id` | numeric string | Campaign's ID (Default) |\n| `account_id` | numeric string | ID of the ad account that owns this campaign |\n| `adlabels` | list<AdLabel> | Ad Labels associated with this campaign |\n| `bid_strategy` | enum | Bid strategy for this campaign when you enable campaign budget optimization |\n| `boosted_object_id` | numeric string | The Boosted Object this campaign has associated, if any |\n| `brand_lift_studies` | list<AdStudy> | Automated Brand Lift V2 studies for this ad set |\n| `budget_remaining` | numeric string | Remaining budget |\n| `buying_type` | string | Buying type (AUCTION or RESERVED) |\n| `configured_status` | enum | Campaign status (ACTIVE, PAUSED, DELETED, ARCHIVED) |\n| `created_time` | datetime | Created Time |\n| `daily_budget` | numeric string | The daily budget of the campaign |\n| `effective_status` | enum | Effective status including IN_PROCESS and WITH_ISSUES |\n| `is_skadnetwork_attribution` | bool | Indicates campaign will include SKAdNetwork, iOS 14+ |\n| `lifetime_budget` | numeric string | The lifetime budget of the campaign |\n| `name` | string | Campaign's name |\n| `objective` | string | Campaign's objective |\n| `promoted_object` | AdPromotedObject | The object this campaign is promoting across all its ads |\n| `special_ad_categories` | list<enum> | Special ad categories |\n| `spend_cap` | numeric string | A spend cap for the campaign |\n| `start_time` | datetime | Campaign start time |\n| `status` | enum | Campaign status |\n| `stop_time` | datetime | Campaign stop time |\n| `updated_time` | datetime | Updated Time |\n\n## Creating\n\nYou can create a campaign by making a POST request to `/act_{ad_account_id}/campaigns`.\n\n### Required Parameters\n\n| Parameter | Description |\n|-----------|-------------|\n| `name` | Campaign name |\n| `objective` | Campaign objective |\n| `status` | Campaign status (ACTIVE or PAUSED) |\n| `special_ad_categories` | Array of special ad categories (required) |\n\n### Optional Parameters\n\n| Parameter | Description |\n|-----------|-------------|\n| `bid_strategy` | Bid strategy for campaign budget optimization |\n| `daily_budget` | Daily budget (int64) |\n| `lifetime_budget` | Lifetime budget (int64) |\n| `buying_type` | AUCTION (default) or RESERVED |\n| `is_skadnetwork_attribution` | Boolean for iOS 14+ campaigns |\n| `promoted_object` | Object being promoted |\n| `spend_cap` | Spend cap for the campaign |\n\n## Updating\n\nYou can update a campaign by making a POST request to `/{campaign_id}`.\n\n## Deleting\n\nYou can delete a campaign by making a DELETE request to `/{campaign_id}`.\n\n## Objective Validation\n\n### Outcome-Driven Ads Experiences (ODAX)\n\nNewer objectives that replace legacy objectives:\n- `OUTCOME_APP_PROMOTION`\n- `OUTCOME_AWARENESS`\n- `OUTCOME_ENGAGEMENT`\n- `OUTCOME_LEADS`\n- `OUTCOME_SALES`\n- `OUTCOME_TRAFFIC`\n\n### Legacy Objectives\n\nDeprecated objectives include:\n- `APP_INSTALLS`\n- `BRAND_AWARENESS`\n- `CONVERSIONS`\n- `EVENT_RESPONSES`\n- `LEAD_GENERATION`\n- `LINK_CLICKS`\n- `MESSAGES`\n- `PAGE_LIKES`\n- `POST_ENGAGEMENT`\n- `PRODUCT_CATALOG_SALES`\n- `REACH`\n- `STORE_VISITS`\n- `VIDEO_VIEWS`\n\n### Objective Requirements\n\nCertain objectives require specific `promoted_object` fields:\n\n- **APP_INSTALLS**: `application_id` and `object_store_url`\n- **CONVERSIONS**: `pixel_id` and optionally `custom_event_type`\n- **PRODUCT_CATALOG_SALES**: `product_set_id`\n- **PAGE_LIKES**: `page_id`\n\n## Error Codes\n\nCommon error codes:\n- 100: Invalid parameter\n- 190: Invalid OAuth 2.0 Access Token\n- 200: Permissions error\n- 368: Action deemed abusive or disallowed\n- 80004: Too many calls to ad account", "keyPoints": ["Campaigns are the highest level organizational structure in Facebook ad accounts", "All campaigns must specify special_ad_categories field (required)", "iOS 14.5+ campaigns must set is_skadnetwork_attribution to true", "Campaign objectives determine valid ad types, optimization goals, and promoted objects", "New ODAX objectives are replacing legacy objectives starting from v17.0"], "apiEndpoints": ["GET /v23.0/{campaign_id}", "POST /act_{ad_account_id}/campaigns", "POST /{campaign_id}", "DELETE /{campaign_id}", "POST /{campaign_id}/copies"], "parameters": ["special_ad_categories", "objective", "name", "status", "bid_strategy", "daily_budget", "lifetime_budget", "is_skadnetwork_attribution", "promoted_object", "spend_cap", "buying_type"], "examples": ["POST /v23.0/act_<AD_ACCOUNT_ID>/campaigns HTTP/1.1\nname=My+campaign&objective=OUTCOME_TRAFFIC&status=PAUSED&special_ad_categories=%5B%5D", "curl -X POST \\\n  -F 'name=\"New ODAX Campaign\"' \\\n  -F 'objective=\"OUTCOME_ENGAGEMENT\"' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'special_ad_categories=[]' \\\n  -F 'access_token=ACCESS_TOKEN \\\n  https://graph.facebook.com/v11.0/act_AD_ACCOUNT_ID/campaigns"], "tags": ["Facebook Marketing API", "Campaign", "Advertising", "Graph API", "CRUD Operations", "Objectives", "Special Ad Categories", "iOS 14.5", "SKAdNetwork"], "relatedTopics": ["Ad Sets", "Ad Accounts", "Ad Labels", "Promoted Objects", "Special Ad Categories", "Campaign Budget Optimization", "Bid Strategies", "Attribution Specs", "Tracking Specs", "ODAX Objectives"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group", "processedAt": "2025-06-25T15:53:24.288Z", "processor": "openrouter-claude-sonnet-4"}