{"title": "Page Not Found - Connected Instagram Accounts API Reference", "summary": "The requested Facebook Marketing API documentation page for connected Instagram accounts under ad accounts was not found. The page may have been moved, removed, or the URL may be incorrect.", "content": "# Page Not Found - Connected Instagram Accounts API Reference\n\nThe requested documentation page for the Facebook Marketing API reference on ad account connected Instagram accounts could not be found.\n\n## Error Details\n- **Status**: Page Not Found (404)\n- **Original URL**: `/docs/marketing-api/reference/ad-account/connected_instagram_accounts/`\n- **Issue**: The link may be broken or the page may have been removed\n\n## Suggested Actions\n- Search for \"marketing api reference ad account connected instagram accounts\" on the Meta for Developers site\n- Check the current Facebook Marketing API documentation for updated endpoints\n- Verify the correct API reference path for Instagram account connections", "keyPoints": ["The specific API documentation page for connected Instagram accounts is currently unavailable", "The page may have been moved or removed from the Meta for Developers documentation", "Users should search the developers site for current Instagram account connection documentation", "This appears to be related to Facebook Marketing API ad account functionality"], "apiEndpoints": [], "parameters": [], "examples": [], "tags": ["facebook-marketing-api", "instagram-accounts", "ad-account", "page-not-found", "documentation-error"], "relatedTopics": ["Facebook Marketing API", "Instagram Business Accounts", "Ad Account Management", "Social Media API Integration"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/connected_instagram_accounts/", "processedAt": "2025-06-25T16:10:37.329Z", "processor": "openrouter-claude-sonnet-4"}