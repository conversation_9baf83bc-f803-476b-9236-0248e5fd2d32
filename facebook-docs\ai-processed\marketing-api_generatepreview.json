{"title": "Facebook Marketing API - Ad Previews Generation", "summary": "This documentation covers how to generate previews of Facebook ads using the Marketing API v23.0. It explains multiple methods for creating ad previews including using existing ad IDs, ad creative IDs, or creative specifications, with support for various ad formats and placements.", "content": "# Ad Previews\n\nPreview existing ads and generate previews of ads you want to create. Generated previews are based on your ad creative. For ad preview **provide a user access token**, not a Page access token.\n\n## Preview Existing Ad Creative\n\n```bash\ncurl -X GET \\\n  -d 'ad_format=\"DESKTOP_FEED_STANDARD\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>/previews\n```\n\n## Preview Using Creative Spec\n\n```bash\ncurl -X GET \\\n  -d 'creative=\"<CREATIVE_SPEC>\"' \\\n  -d 'ad_format=\"<AD_FORMAT>\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```\n\n## Generating Previews\n\nThere are several ways to generate a preview:\n\n- **Ad ID**: Use existing ad's `previews` endpoint\n- **Ad Creative ID**: Use existing ad creative's `previews` endpoint  \n- **Creative Spec**: Supply a creative specification\n\n### Using Ad ID\n```\nhttps://graph.facebook.com/<API_VERSION>/<AD_ID>/previews\n```\n\n### Using Ad Creative ID\n```\nhttps://graph.facebook.com/<API_VERSION>/<AD_CREATIVE_ID>/previews\n```\n\n### Using Creative Spec\nTwo endpoint options:\n- `/act_<AD_ACCOUNT_ID>/generatepreviews` - Account-specific previews\n- `/generatepreviews` - General previews (not account-specific)\n\n**Note**: Previews from an ad account are only visible to people with a role on the ad account. Previews generated using the general `generatepreviews` endpoint are visible to anyone.\n\n### Advantage+ Catalog Ads\nFor Advantage+ catalog ads, pass the entire `object_story_spec` into the `/generatepreviews` endpoint and use `product_item_ids`.\n\n## Examples\n\n### Object Story Spec Preview (PHP)\n\n```php\nuse FacebookAds\\Object\\AdAccount;\nuse FacebookAds\\Object\\AdCreative;\nuse FacebookAds\\Object\\Fields\\AdCreativeFields;\nuse FacebookAds\\Object\\Fields\\AdPreviewFields;\nuse FacebookAds\\Object\\Fields\\AdCreativeLinkDataFields;\nuse FacebookAds\\Object\\Fields\\AdCreativeObjectStorySpecFields;\nuse FacebookAds\\Object\\AdCreativeLinkData;\nuse FacebookAds\\Object\\AdCreativeObjectStorySpec;\nuse FacebookAds\\Object\\Values\\AdPreviewAdFormatValues;\nuse FacebookAds\\Object\\Values\\AdCreativeCallToActionTypeValues;\n\n$link_data = new AdCreativeLinkData();\n$link_data->setData(array(\n  AdCreativeLinkDataFields::LINK => '<URL>',\n  AdCreativeLinkDataFields::MESSAGE => 'Message',\n  AdCreativeLinkDataFields::NAME => 'Name',\n  AdCreativeLinkDataFields::DESCRIPTION => 'Description',\n  AdCreativeLinkDataFields::CALL_TO_ACTION => array(\n    'type' => AdCreativeCallToActionTypeValues::SIGN_UP,\n    'value' => array(\n      'link' => '<URL>',\n    ),\n  ),\n));\n\n$story = new AdCreativeObjectStorySpec();\n$story->setData(array(\n  AdCreativeObjectStorySpecFields::PAGE_ID => <PAGE_ID>,\n  AdCreativeObjectStorySpecFields::LINK_DATA => $link_data,\n));\n\n$creative = new AdCreative();\n$creative->setData(array(\n  AdCreativeFields::OBJECT_STORY_SPEC => $story,\n));\n\n$account = new AdAccount('act_<AD_ACCOUNT_ID>');\n$account->getGeneratePreviews(array(), array(\n  AdPreviewFields::CREATIVE => $creative,\n  AdPreviewFields::AD_FORMAT => AdPreviewAdFormatValues::DESKTOP_FEED_STANDARD,\n));\n```\n\n### Multi-Product Ad Preview\n\n```bash\ncurl -X GET \\\n  -d 'creative={\n        \"object_story_id\": \"<PAGE_ID>_<POST_ID>\"\n      }' \\\n  -d 'ad_format=\"DESKTOP_FEED_STANDARD\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```\n\n### App Ad Preview\n\n```bash\ncurl -X GET \\\n  -d 'creative={\n        \"object_story_spec\": {\n          \"link_data\": {\n            \"call_to_action\": {\n              \"type\": \"USE_APP\",\n              \"value\": {\n                \"link\": \"<URL>\"\n              }\n            },\n            \"description\": \"Description\",\n            \"link\": \"<URL>\",\n            \"message\": \"Message\",\n            \"name\": \"Name\",\n            \"picture\": \"<IMAGE_URL>\"\n          },\n          \"page_id\": \"<PAGE_ID>\"\n        }\n      }' \\\n  -d 'ad_format=\"MOBILE_FEED_STANDARD\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews\n```\n\n### Instagram Explore Home Ad Preview\n\n```bash\ncurl -X GET \\\n  -d 'ad_format=\"INSTAGRAM_EXPLORE_GRID_HOME\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_ID>/previews\n```\n\n### Instagram Search Results Ad Preview\n\n```bash\ncurl -X GET \\\n  -d 'ad_format=\"INSTAGRAM_SEARCH_CHAIN\"' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_ID>/previews\n```\n\n## Response Format\n\nAll endpoints return an ad preview object containing an iframe with the rendered ad preview:\n\n```json\n{\n  \"data\": [\n    {\n      \"body\": \"<iframe src=\\\"https://www.facebook.com/ads/api/preview_iframe.php?d=...\\\" width=\\\"274\\\" height=\\\"213\\\" scrolling=\\\"yes\\\" style=\\\"border: none;\\\"></iframe>\"\n    }\n  ]\n}\n```", "keyPoints": ["Use user access tokens, not Page access tokens for ad previews", "Multiple methods available: Ad ID, Ad Creative ID, or Creative Spec", "Account-specific previews are only visible to account role holders", "General generatepreviews endpoint creates publicly visible previews", "Supports various ad formats including Instagram placements"], "apiEndpoints": ["https://graph.facebook.com/v23.0/<CREATIVE_ID>/previews", "https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/generatepreviews", "https://graph.facebook.com/v23.0/generatepreviews", "https://graph.facebook.com/v23.0/<AD_ID>/previews", "https://graph.facebook.com/v23.0/<AD_CREATIVE_ID>/previews"], "parameters": ["ad_format", "access_token", "creative", "object_story_spec", "object_story_id", "link_data", "page_id", "product_item_ids", "call_to_action"], "examples": ["Basic creative preview with DESKTOP_FEED_STANDARD format", "Multi-product ad preview using object_story_id", "App ad preview with USE_APP call-to-action", "Instagram Explore Grid Home ad format", "Instagram Search Chain ad format", "PHP SDK implementation with object_story_spec"], "tags": ["Facebook Marketing API", "Ad Previews", "Ad Creative", "Instagram Ads", "API v23.0", "generatepreviews"], "relatedTopics": ["Ad Creative", "Ad Account", "Multi-Product Ads", "Advantage+ Catalog Ads", "Instagram Placements", "Object Story Spec", "Access Tokens"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/generatepreview/v23.0", "processedAt": "2025-06-25T15:43:15.898Z", "processor": "openrouter-claude-sonnet-4"}