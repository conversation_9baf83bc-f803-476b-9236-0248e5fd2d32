# Facebook Marketing API - Ad Account Adsets Reference

## Summary
Complete reference documentation for managing ad sets within Facebook ad accounts, including reading, creating, updating, and deleting ad sets through the Marketing API. Covers all parameters, fields, examples, and error codes for ad set operations.

## Key Points
- iOS 14.5 changes affect mobile app custom audiences and app connections targeting
- Either daily_budget or lifetime_budget must be specified when creating ad sets
- Bid strategy and optimization goal work together to determine ad delivery
- Targeting object is required and must include at least country-level geographic targeting
- Mobile App Install CPA billing is no longer supported

## API Endpoints
- `GET /v23.0/act_<AD_ACCOUNT_ID>/adsets`
- `POST /v23.0/act_<AD_ACCOUNT_ID>/adsets`

## Parameters
- name
- campaign_id
- daily_budget
- lifetime_budget
- billing_event
- optimization_goal
- targeting
- bid_amount
- bid_strategy
- promoted_object
- start_time
- end_time
- status

## Content
# Ad Account Adsets

## Overview

Due to the iOS 14.5 launch, changes have been made to this endpoint:
- Mobile App Custom Audiences for inclusion targeting is no longer supported for the `POST /{ad-account-id}/adsets` endpoint for iOS 14.5 SKAdNetwork campaigns
- New iOS 14.5 app install campaigns will no longer be able to use app connections targeting

## Reading Ad Sets

Retrieve the ad sets of an ad account.

### Endpoint
```
GET /v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Example Request
```bash
curl -X GET -G \
  -d 'fields="name,id,status"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `date_preset` | enum | Predefined date range for aggregating insights metrics |
| `effective_status` | list<enum> | Filter by effective status (ACTIVE, PAUSED, DELETED, etc.) |
| `is_completed` | boolean | Filter ad sets by completed status |
| `time_range` | object | Custom date range with 'since' and 'until' fields |
| `updated_since` | integer | Time since the ad set has been updated |

### Response Fields

The response returns a JSON object with:
- `data`: Array of AdSet nodes
- `paging`: Pagination information
- `summary`: Aggregated information (insights, total_count)

## Creating Ad Sets

Create a new ad set within an ad account.

### Important Notes
- Mobile App Install CPA Billing is no longer supported
- The billing event cannot be App Install if the optimization goal is App Install

### Endpoint
```
POST /v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Example Request
```bash
curl -X POST \
  -F 'name="My First AdSet"' \
  -F 'daily_budget=10000' \
  -F 'bid_amount=300' \
  -F 'billing_event="IMPRESSIONS"' \
  -F 'optimization_goal="REACH"' \
  -F 'campaign_id="<AD_CAMPAIGN_ID>"' \
  -F 'promoted_object={"page_id":"<PAGE_ID>"}' \
  -F 'targeting={"facebook_positions":["feed"],"geo_locations":{"countries":["US"]}}' \
  -F 'status="PAUSED"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets
```

### Required Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `name` | string | Ad set name (max 400 characters) |
| `campaign_id` | numeric string | The ad campaign ID to add this ad set to |
| `daily_budget` or `lifetime_budget` | int64 | Budget in account currency |
| `billing_event` | enum | How you pay for ads (IMPRESSIONS, CLICKS, etc.) |
| `optimization_goal` | enum | What the ad set optimizes for |
| `targeting` | object | Targeting specifications |

### Key Optional Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `bid_amount` | integer | Bid cap or target cost |
| `bid_strategy` | enum | Bidding strategy (LOWEST_COST_WITHOUT_CAP, etc.) |
| `start_time` | datetime | When the ad set starts |
| `end_time` | datetime | When the ad set ends |
| `promoted_object` | object | What the ad set promotes |
| `attribution_spec` | list | Conversion attribution specifications |
| `frequency_control_specs` | list | Frequency control settings |

### Bid Strategies

- `LOWEST_COST_WITHOUT_CAP`: Get most results for budget without bid limits
- `LOWEST_COST_WITH_BID_CAP`: Get most results while limiting bid amount
- `COST_CAP`: Maintain stable costs while optimizing

### Billing Events

- `APP_INSTALLS`: Pay when people install your app
- `CLICKS`: Deprecated
- `IMPRESSIONS`: Pay when ads are shown
- `LINK_CLICKS`: Pay when people click ad links
- `THRUPLAY`: Pay for 15+ second video views or completions

### Optimization Goals

- `APP_INSTALLS`: Optimize for app installations
- `CONVERSIONS`: Optimize for website conversions
- `LINK_CLICKS`: Optimize for link clicks
- `REACH`: Optimize for unique reach
- `IMPRESSIONS`: Maximize impressions
- `THRUPLAY`: Optimize for video completion

### Targeting Object

The targeting object supports:
- Geographic targeting (countries, regions, cities)
- Demographic targeting (age, gender)
- Interest targeting
- Behavioral targeting
- Custom audiences
- Lookalike audiences

### Response

Returns an object with:
```json
{
  "id": "<ADSET_ID>",
  "success": true
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 613 | Rate limit exceeded |
| 80004 | Too many calls to ad account |
| 2641 | Restricted locations in targeting |
| 368 | Abusive or disallowed action |
| 2695 | Campaign group limit reached |

## Updating and Deleting

- **Updating**: Not supported on this endpoint
- **Deleting**: Deprecated as of Marketing API V8

## Examples
GET request to retrieve ad sets with name, id, and status fields

POST request to create ad set with targeting, budget, and optimization settings

Targeting object with geographic, demographic, and interest specifications

Promoted object configuration for different campaign objectives

---
**Tags:** Facebook Marketing API, Ad Sets, Advertising, Campaign Management, Targeting, Bidding, iOS 14.5  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adsets/  
**Processed:** 2025-06-25T16:21:42.946Z