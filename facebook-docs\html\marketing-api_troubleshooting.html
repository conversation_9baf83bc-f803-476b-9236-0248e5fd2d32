<div class="_1dyy" id="u_0_5_Uf"><div class="_33zv _3e1u"><div class="_33zw" tabindex="0" role="button">On This Page<div><i class="img sp_zD_MvjcHflF sx_a3c10c" alt="" data-visualcompletion="css-img"></i><i class="hidden_elem img sp_zD_MvjcHflF sx_6874ff" alt="" data-visualcompletion="css-img"></i></div></div><div class="_5-24 hidden_elem"><a href="#troubleshooting">Troubleshooting</a></div><div class="_5-24 hidden_elem"><a href="#error-handling">Error Handling</a></div><div class="_5-24 hidden_elem"><a href="#authorization-errors">Authorization Errors</a></div><div class="_5-24 hidden_elem"><a href="#invalid-parameters">Invalid Parameters</a></div><div class="_5-24 hidden_elem"><a href="#resource-not-found">Resource Not Found</a></div><div class="_5-24 hidden_elem"><a href="#rate-limiting">Rate Limiting</a></div><div class="_5-24 hidden_elem"><a href="#caching-strategies">Caching Strategies</a></div><div class="_5-24 hidden_elem"><a href="#managing-api-versioning">Managing API Versioning</a></div><div class="_5-24 hidden_elem"><a href="#error-logging-and-monitoring">Error Logging and Monitoring</a></div></div></div><div id="documentation_body_pagelet" data-referrer="documentation_body_pagelet"><div class="_34yh" id="u_0_0_3A"><div class="_4cel"><span data-click-area="main"><div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8"><div class="_4-u3 _588p"><h1 id="troubleshooting">Troubleshooting</h1>

<p>Working with the Marketing API can occasionally present challenges. Below are issues users may encounter, along with practical solutions to help streamline your experience.</p>
</div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="error-handling">Error Handling</h2>

<p>Use the error handling techniques and best practices below to enhance the reliability and efficiency of your applications.</p>

<h3 id="authorization-errors">Authorization Errors</h3>

<p>These errors often occur due to <a href="/docs/facebook-login/guides/access-tokens">access tokens</a> that are expired, invalid, or lacking the necessary permissions. To mitigate these issues, ensure that tokens are refreshed regularly and that the correct scopes are requested during authorization.</p>

<h3 id="invalid-parameters">Invalid Parameters</h3>

<p>Sending requests with incorrect or missing parameters can lead to <a href="/docs/marketing-api/error-reference">errors</a>. Always validate the input data before making API calls. Utilizing validation tools can significantly reduce such errors.</p>

<h3 id="resource-not-found">Resource Not Found</h3>

<p>This error occurs when attempting to access a resource that does not exist or has been deleted. To resolve this, check that resources (like campaigns or ad sets) exist before performing operations on them.</p>

<h3 id="rate-limiting">Rate Limiting</h3>

<p>The Marketing API enforces <a href="/docs/marketing-apis/rate-limiting">rate limits</a> to prevent abuse. Exceeding these limits results in error messages indicating that too many requests have been made in a short time. Employing exponential backoff strategies can help slow down request rates after hitting the limit.</p>

<p>To optimize performance and avoid hitting rate limits, create a queue system for API requests. This allows for controlled pacing of requests, ensuring compliance with the API's limits without sacrificing performance.</p>

<h3 id="caching-strategies">Caching Strategies</h3>

<p>Implement caching for frequently accessed data, such as audience insights or ad performance metrics. This reduces the number of API calls and speeds up data retrieval, leading to a more efficient application.</p>

<h3 id="managing-api-versioning">Managing API Versioning</h3>

<p>Stay informed about <a href="/docs/marketing-api/marketing-api-changelog">updates and changes</a> in the Marketing API by regularly checking the documentation. Placing API calls within version-specific functions can prepare your application for version changes, allowing for independent updates.</p>

<h3 id="error-logging-and-monitoring">Error Logging and Monitoring</h3>

<p>Implement robust error logging to track API interactions. This will help identify patterns in errors and facilitate quicker resolutions. Utilizing monitoring tools can alert developers to critical failures or unusual patterns in API usage.</p>
<div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div></div></span><div class="_4-u2 _57mb _1u44 _4-u8 _3la3"><div class="_4-u3 _588p _4_k"><fb:like href="https://developers.facebook.com/docs/marketing-api/troubleshooting/" layout="button_count" share="1"></fb:like><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div id="developer_documentation_toolbar" data-referrer="developer_documentation_toolbar" data-click-area="toolbar"></div><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '675141479195042');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '574561515946252');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '1754628768090156');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '217404712025032');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1" /></noscript></div></div></div>