{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport function stripTrailingSlash(url) {\n  return url.replace(/\\/$/, '');\n}\nexport const isBrowser = () => typeof window !== 'undefined';\nexport function applySettingDefaults(options, defaults) {\n  const {\n    db: dbOptions,\n    auth: authOptions,\n    realtime: realtimeOptions,\n    global: globalOptions\n  } = options;\n  const {\n    db: DEFAULT_DB_OPTIONS,\n    auth: DEFAULT_AUTH_OPTIONS,\n    realtime: DEFAULT_REALTIME_OPTIONS,\n    global: DEFAULT_GLOBAL_OPTIONS\n  } = defaults;\n  const result = {\n    db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n    auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n    realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n    global: Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions),\n    accessToken: () => __awaiter(this, void 0, void 0, function* () {\n      return '';\n    })\n  };\n  if (options.accessToken) {\n    result.accessToken = options.accessToken;\n  } else {\n    // hack around Required<>\n    delete result.accessToken;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["uuid", "replace", "c", "r", "Math", "random", "v", "toString", "stripTrailingSlash", "url", "<PERSON><PERSON><PERSON><PERSON>", "window", "applySettingDefaults", "options", "defaults", "db", "dbOptions", "auth", "authOptions", "realtime", "realtimeOptions", "global", "globalOptions", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "DEFAULT_GLOBAL_OPTIONS", "result", "Object", "assign", "accessToken", "__awaiter"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\supabase-js\\src\\lib\\helpers.ts"], "sourcesContent": ["// helpers.ts\nimport { SupabaseClientOptions } from './types'\n\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = (Math.random() * 16) | 0,\n      v = c == 'x' ? r : (r & 0x3) | 0x8\n    return v.toString(16)\n  })\n}\n\nexport function stripTrailingSlash(url: string): string {\n  return url.replace(/\\/$/, '')\n}\n\nexport const isBrowser = () => typeof window !== 'undefined'\n\nexport function applySettingDefaults<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database\n>(\n  options: SupabaseClientOptions<SchemaName>,\n  defaults: SupabaseClientOptions<any>\n): Required<SupabaseClientOptions<SchemaName>> {\n  const {\n    db: dbOptions,\n    auth: authOptions,\n    realtime: realtimeOptions,\n    global: globalOptions,\n  } = options\n  const {\n    db: DEFAULT_DB_OPTIONS,\n    auth: DEFAULT_AUTH_OPTIONS,\n    realtime: DEFAULT_REALTIME_OPTIONS,\n    global: DEFAULT_GLOBAL_OPTIONS,\n  } = defaults\n\n  const result: Required<SupabaseClientOptions<SchemaName>> = {\n    db: {\n      ...DEFAULT_DB_OPTIONS,\n      ...dbOptions,\n    },\n    auth: {\n      ...DEFAULT_AUTH_OPTIONS,\n      ...authOptions,\n    },\n    realtime: {\n      ...DEFAULT_REALTIME_OPTIONS,\n      ...realtimeOptions,\n    },\n    global: {\n      ...DEFAULT_GLOBAL_OPTIONS,\n      ...globalOptions,\n    },\n    accessToken: async () => '',\n  }\n\n  if (options.accessToken) {\n    result.accessToken = options.accessToken\n  } else {\n    // hack around Required<>\n    delete (result as any).accessToken\n  }\n\n  return result\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,OAAM,SAAUA,IAAIA,CAAA;EAClB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC;IACxE,IAAIC,CAAC,GAAIC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAI,CAAC;MAC9BC,CAAC,GAAGJ,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;IACpC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACvB,CAAC,CAAC;AACJ;AAEA,OAAM,SAAUC,kBAAkBA,CAACC,GAAW;EAC5C,OAAOA,GAAG,CAACR,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC/B;AAEA,OAAO,MAAMS,SAAS,GAAGA,CAAA,KAAM,OAAOC,MAAM,KAAK,WAAW;AAE5D,OAAM,SAAUC,oBAAoBA,CAMlCC,OAA0C,EAC1CC,QAAoC;EAEpC,MAAM;IACJC,EAAE,EAAEC,SAAS;IACbC,IAAI,EAAEC,WAAW;IACjBC,QAAQ,EAAEC,eAAe;IACzBC,MAAM,EAAEC;EAAa,CACtB,GAAGT,OAAO;EACX,MAAM;IACJE,EAAE,EAAEQ,kBAAkB;IACtBN,IAAI,EAAEO,oBAAoB;IAC1BL,QAAQ,EAAEM,wBAAwB;IAClCJ,MAAM,EAAEK;EAAsB,CAC/B,GAAGZ,QAAQ;EAEZ,MAAMa,MAAM,GAAgD;IAC1DZ,EAAE,EAAAa,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACGN,kBAAkB,GAClBP,SAAS,CACb;IACDC,IAAI,EAAAW,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACCL,oBAAoB,GACpBN,WAAW,CACf;IACDC,QAAQ,EAAAS,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACHJ,wBAAwB,GACxBL,eAAe,CACnB;IACDC,MAAM,EAAAO,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACDH,sBAAsB,GACtBJ,aAAa,CACjB;IACDQ,WAAW,EAAEA,CAAA,KAAWC,SAAA;MAAC,SAAE;IAAA;GAC5B;EAED,IAAIlB,OAAO,CAACiB,WAAW,EAAE;IACvBH,MAAM,CAACG,WAAW,GAAGjB,OAAO,CAACiB,WAAW;GACzC,MAAM;IACL;IACA,OAAQH,MAAc,CAACG,WAAW;;EAGpC,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}