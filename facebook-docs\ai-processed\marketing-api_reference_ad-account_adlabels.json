{"title": "Facebook Marketing API - Ad Account Ad Labels Reference", "summary": "Complete reference documentation for the Facebook Marketing API's Ad Account Ad Labels endpoint, covering how to read, create, and manage ad labels within an ad account. This endpoint allows developers to organize and categorize ads using custom labels for better campaign management.", "content": "# Ad Account Ad Labels\n\nThe Ad Account Ad Labels endpoint allows you to manage labels for organizing ads within a Facebook ad account. This endpoint supports reading existing labels and creating new ones.\n\n## Reading Ad Labels\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/adlabels\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Response Format\nThe response returns a JSON object with the following structure:\n```json\n{\n  \"data\": [],\n  \"paging\": {},\n  \"summary\": {}\n}\n```\n\n#### Response Fields\n- **data**: Array of AdLabel nodes\n- **paging**: Pagination information for navigating through results\n- **summary**: Aggregated information about the edge\n  - `insights`: Analytics summary for all objects\n  - `total_count`: Total number of objects\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/adlabels HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/adlabels',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/adlabels\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Error Codes\n- **200**: Permissions error\n- **190**: Invalid OAuth 2.0 Access Token\n- **80004**: Too many calls to this ad-account (rate limiting)\n- **100**: Invalid parameter\n\n## Creating Ad Labels\n\n### Endpoint\n```\nPOST /v23.0/act_{ad_account_id}/adlabels\n```\n\n### Required Parameters\n- **name** (string): The name for the new ad label\n\n### Code Examples\n\n#### HTTP Request\n```http\nPOST /v23.0/act_<AD_ACCOUNT_ID>/adlabels HTTP/1.1\nHost: graph.facebook.com\n\nname=My+Label+1\n```\n\n#### PHP SDK\n```php\ntry {\n  $response = $fb->post(\n    '/act_<AD_ACCOUNT_ID>/adlabels',\n    array (\n      'name' => 'My Label 1',\n    ),\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n}\n```\n\n#### cURL\n```bash\ncurl -X POST \\\n  -F 'name=\"My Label 1\"' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adlabels\n```\n\n### Return Type\nThis endpoint supports read-after-write and returns:\n```json\n{\n  \"id\": \"numeric_string\"\n}\n```\n\n### Error Codes\n- **200**: Permissions error\n- **100**: Invalid parameter\n\n## Limitations\n- **Updating**: Not supported on this endpoint\n- **Deleting**: Not supported on this endpoint\n\n## API Version\nCurrent version: v23.0", "keyPoints": ["Ad Labels help organize and categorize ads within Facebook ad accounts", "The endpoint supports reading existing labels and creating new ones", "Reading requires no parameters and returns paginated results with summary data", "Creating labels only requires a name parameter", "Updating and deleting operations are not supported on this endpoint"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/adlabels", "POST /v23.0/act_{ad_account_id}/adlabels"], "parameters": ["name (string, required for creation)", "summary (optional for reading - specify fields like 'insights')", "access_token (required for authentication)"], "examples": ["GET request to read ad labels", "POST request to create new ad label with name 'My Label 1'", "PHP SDK implementation for both reading and creating", "JavaScript SDK example for reading labels", "cURL command for creating labels"], "tags": ["Facebook Marketing API", "Ad Labels", "Ad Account", "Campaign Management", "API Reference", "Graph API"], "relatedTopics": ["AdLabel object reference", "Graph API pagination", "OAuth 2.0 Access Tokens", "Rate limiting for ads management", "Read-after-write operations", "AdsInsights analytics"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adlabels/", "processedAt": "2025-06-25T16:12:32.217Z", "processor": "openrouter-claude-sonnet-4"}