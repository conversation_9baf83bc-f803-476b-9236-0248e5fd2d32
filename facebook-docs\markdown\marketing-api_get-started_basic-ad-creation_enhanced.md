# Facebook Marketing API: Automating Ad Creation

## Summary
This guide covers the systematic approach to creating ads using the Facebook Marketing API, including the three primary endpoints for campaigns, ad sets, and ads. It provides detailed guidance and code samples for programmatically creating advertising components.

## Key Points
- Ad creation follows a hierarchical structure: campaigns → ad sets → ads
- The campaigns endpoint sets overall marketing objectives like brand awareness or conversions
- Ad sets provide granular control over targeting criteria and budget allocation
- The ads endpoint creates actual advertisements with creative elements
- All endpoints require proper authentication via access tokens

## API Endpoints
- `POST /v23.0/act_{ad-account-id}/campaigns`
- `POST /v23.0/act_{ad-account-id}/adsets`
- `POST /v23.0/act_{ad-account-id}/ads`

## Parameters
- name
- objective
- status
- campaign_id
- daily_budget
- targeting
- adset_id
- creative
- access_token

## Content
# Automating Ad Creation

Creating ads using the Marketing API involves a systematic approach that includes setting up campaigns, ad sets, and ad creatives. This document provides detailed guidance on programmatically creating these components, along with code samples to illustrate the implementation process.

## Ad Creation Endpoints

The Marketing API offers a variety of key endpoints that serve as essential tools for developers to create, manage, and analyze advertising campaigns. The primary creation endpoints include `campaigns`, `adsets`, and `ads`. Understanding these endpoints and their functionalities is crucial for both new and experienced developers looking to optimize their advertising strategies.

### The `campaigns` endpoint

The [`campaigns` endpoint](/docs/marketing-api/reference/ad-account/campaigns) is used to create and manage advertising campaigns. This endpoint allows users to set the overall objectives for their marketing efforts, such as brand awareness or conversions.

**Example API Request:**

```bash
curl -X POST \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/campaigns \
  -F 'name=My Campaign' \
  -F 'objective=LINK_CLICKS' \
  -F 'status=PAUSED' \
  -F 'access_token=<ACCESS_TOKEN>'
```

### The `adsets` endpoint

The [`adsets` endpoint](/docs/marketing-api/reference/ad-account/adsets) organizes ads within campaigns based on specific targeting criteria and budget allocation. This allows for more granular control over audience targeting and spending.

**Example API Request:**

```bash
curl -X POST \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets \
  -F 'name=My Ad Set' \
  -F 'campaign_id=<CAMPAIGN_ID>' \
  -F 'daily_budget=1000' \
  -F 'targeting={"geo_locations":{"countries":["US"]}}' \
  -F 'access_token=<ACCESS_TOKEN>'
```

### The `ads` endpoint

The [`ads` endpoint](/docs/marketing-api/reference/ad-account/ads) is where the actual advertisements are created, allowing you to define creative elements and link them to the appropriate ad set.

**Example API Request:**

```bash
curl -X POST \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/ads \
  -F 'name=My Ad' \
  -F 'adset_id=<AD_SET_ID>' \
  -F 'creative={"creative_id": "<CREATIVE_ID>"}' \
  -F 'status=ACTIVE' \
  -F 'access_token=<ACCESS_TOKEN>'
```

## Examples
Campaign creation with LINK_CLICKS objective

Ad set creation with US geo-targeting and daily budget

Ad creation linking to creative and ad set

---
**Tags:** facebook-marketing-api, ad-creation, campaigns, adsets, automation, api-endpoints
**Difficulty:** beginner
**Content Type:** guide
**Source:** https://developers.facebook.com/docs/marketing-api/get-started/basic-ad-creation
**Processed:** 2025-06-25T15:49:09.563Z