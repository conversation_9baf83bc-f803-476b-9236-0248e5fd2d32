{"title": "Facebook Marketing API - Ad Creative Reference", "summary": "Complete reference for the Ad Creative object in Facebook Marketing API, which provides layout and content formatting for ads. Covers creation, reading, updating, and deletion of ad creatives with detailed field specifications and examples.", "content": "# Facebook Marketing API - Ad Creative Reference\n\n## Overview\n\nThe Ad Creative object provides layout and contains content for Facebook ads. It defines the creative field of one or more ads and is stored in your ad account's creative library for reuse.\n\n### Important Deprecation Notice\n\nThe `instagram_actor_id` field for `act_<AD_ACCOUNT_ID>/adcreatives` has been deprecated for v22.0 and will be deprecated for all versions January 20, 2026. Migrate to use the `instagram_user_id` field instead.\n\n## Special Requirements\n\n### Political Ads\n\nAdvertisers running ads about social issues, elections, and politics must:\n- Specify `special_ad_categories` while creating an ad campaign\n- Set `authorization_category` to flag at the ad creative level\n- Use `POLITICAL_WITH_DIGITALLY_CREATED_MEDIA` for digitally created/altered media (effective January 9, 2024)\n\n## Limits and Restrictions\n\n### General Limits\n- Maximum 50,000 ad creatives returned (pagination unavailable beyond this)\n- Ad title: 1-25 characters (recommended)\n- Ad body: 1-90 characters (recommended)\n- URL length: 1000 characters maximum\n- Individual word length: 30 characters (recommended)\n\n### Content Rules\n- Cannot start with punctuation: `\\ / ! . ? - * ( ) , ; :`\n- No consecutive punctuation except three dots `...`\n- Maximum three 1-character words allowed\n- Prohibited characters: IPA symbols (with exceptions), standalone diacritical marks, superscript/subscript (except ™ and ℠), and `^~_={}[]|<>`\n\n### Placement Restrictions\n- Link Ads: Cannot use special characters\n- Page Post Ads: Allow special characters like `★`\n\n## API Operations\n\n### Reading Ad Creatives\n\n```bash\ncurl -G \\\n  -d 'fields=name,object_story_id' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>\n```\n\n#### Reading Thumbnails\n\n```bash\ncurl -G \\\n  -d 'thumbnail_width=150' \\\n  -d 'thumbnail_height=120' \\\n  -d 'fields=thumbnail_url' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>\n```\n\n### Creating Ad Creatives\n\n#### Basic Link Ad\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"link_data\": { \n      \"image_hash\": \"<IMAGE_HASH>\", \n      \"link\": \"<URL>\", \n      \"message\": \"try it out\" \n    }, \n    \"page_id\": \"<PAGE_ID>\" \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Link Ad with Call to Action\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"link_data\": { \n      \"call_to_action\": {\"type\":\"SIGN_UP\",\"value\":{\"link\":\"<URL>\"}}, \n      \"link\": \"<URL>\", \n      \"message\": \"try it out\" \n    }, \n    \"page_id\": \"<PAGE_ID>\" \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Carousel Ad\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"link_data\": { \n      \"child_attachments\": [ \n        { \n          \"description\": \"$8.99\", \n          \"image_hash\": \"<IMAGE_HASH>\", \n          \"link\": \"https://www.link.com/product1\", \n          \"name\": \"Product 1\", \n          \"video_id\": \"<VIDEO_ID>\" \n        }, \n        { \n          \"description\": \"$9.99\", \n          \"image_hash\": \"<IMAGE_HASH>\", \n          \"link\": \"https://www.link.com/product2\", \n          \"name\": \"Product 2\", \n          \"video_id\": \"<VIDEO_ID>\" \n        } \n      ], \n      \"link\": \"<URL>\" \n    }, \n    \"page_id\": \"<PAGE_ID>\" \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Political Ad Creative\n\n```bash\ncurl \\\n  -F 'authorization_category=POLITICAL' \\\n  -F 'object_story_spec={...}' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n#### Video Page Like Ad\n\n```bash\ncurl \\\n  -F 'name=Sample Creative' \\\n  -F 'object_story_spec={ \n    \"page_id\": \"<PAGE_ID>\", \n    \"video_data\": { \n      \"call_to_action\": {\"type\":\"LIKE_PAGE\",\"value\":{\"page\":\"<PAGE_ID>\"}}, \n      \"image_url\": \"<THUMBNAIL_URL>\", \n      \"video_id\": \"<VIDEO_ID>\" \n    } \n  }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives\n```\n\n### Updating Ad Creatives\n\n```bash\ncurl \\\n  -F 'name=New creative name' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>\n```\n\n### Deleting Ad Creatives\n\n```bash\ncurl -X DELETE \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<CREATIVE_ID>/\n```\n\n## Key Fields\n\n### Core Fields\n- `id`: Unique numeric string identifier\n- `account_id`: Ad account ID\n- `name`: Creative name (100 character limit)\n- `status`: ACTIVE, IN_PROCESS, WITH_ISSUES, DELETED\n\n### Content Fields\n- `object_story_spec`: Specification for creating unpublished page posts\n- `object_story_id`: ID of existing page post to use\n- `body`: Ad body text (not supported for video posts)\n- `title`: Title for link ads\n- `call_to_action_type`: Type of CTA button\n\n### Media Fields\n- `image_hash`: Image from ad account's library\n- `image_url`: URL for new image to save to library\n- `video_id`: Facebook video object ID\n- `image_crops`: JSON object defining crop dimensions\n\n### Targeting and Placement\n- `authorization_category`: Political ad labeling\n- `platform_customizations`: Media for specific placements\n- `asset_feed_spec`: Dynamic Creative asset feed\n\n### Special Features\n- `branded_content_sponsor_page_id`: For branded content ads\n- `dynamic_ad_voice`: For store traffic in dynamic ads\n- `product_set_id`: For dynamic product ads\n- `url_tags`: Query parameters for clicked URLs\n\n## Partnership Ads\n\nFor partnership ads, set sponsor information:\n- `facebook_branded_content.sponsor_page_id`: Facebook sponsor page\n- `instagram_branded_content.sponsor_id`: Instagram sponsor user ID\n\n## Error Codes\n\n- `100`: Invalid parameter\n- `190`: Invalid OAuth 2.0 Access Token\n- `200`: Permissions error\n- `270`: Development access level restriction\n- `2500`: Error parsing graph query\n- `80004`: Too many calls to ad account (rate limiting)", "keyPoints": ["Ad Creative objects define the layout and content for Facebook ads and are stored in the ad account's creative library", "Political ads require special authorization_category settings and compliance with Facebook's advertising policies", "Content has strict character limits and formatting rules, with different restrictions for link ads vs page post ads", "Inline page post creation allows creating unpublished posts directly within the ad creative using object_story_spec", "The API supports various ad formats including link ads, carousel ads, video ads, and dynamic ads with specific field requirements"], "apiEndpoints": ["GET /v23.0/<CREATIVE_ID>", "POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives", "POST /v23.0/<CREATIVE_ID>", "DELETE /v23.0/<CREATIVE_ID>", "GET /v23.0/<CREATIVE_ID>/previews"], "parameters": ["object_story_spec", "object_story_id", "authorization_category", "name", "image_hash", "image_url", "video_id", "call_to_action_type", "asset_feed_spec", "platform_customizations", "thumbnail_width", "thumbnail_height", "product_set_id", "url_tags", "status"], "examples": ["Basic link ad creation with image and message", "Carousel ad with multiple products and descriptions", "Political ad creative with authorization_category", "Video page like ad with call to action", "Reading ad creative with thumbnail specifications", "Partnership ads with branded content sponsor settings", "Adding URL tags to track clicked URLs"], "tags": ["facebook-marketing-api", "ad-creative", "advertising", "creative-management", "api-reference", "political-ads", "carousel-ads", "video-ads", "branded-content"], "relatedTopics": ["Ad Campaign", "Ad Set", "Ad Account", "Image Library", "Page Posts", "Dynamic Ads", "Instagram Ads", "Branded Content", "Political Advertising", "Ad Placements"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-creative", "processedAt": "2025-06-25T15:51:28.699Z", "processor": "openrouter-claude-sonnet-4"}