# Facebook Marketing API - Ad Account Saved Audiences Reference

## Summary
Reference documentation for the Facebook Marketing API endpoint to retrieve saved audiences from an ad account. This endpoint supports reading operations only and returns a list of SavedAudience objects with optional filtering and field selection.

## Key Points
- This endpoint only supports reading operations for saved audiences
- Responses include pagination support for handling large datasets
- Optional filtering and field selection parameters are available
- Rate limiting applies with specific error code 80004 for too many requests
- Returns SavedAudience objects with configurable field selection

## API Endpoints
- `GET /v23.0/{ad-account-id}/saved_audiences`

## Parameters
- business_id
- fields
- filtering
- ad-account-id

## Content
# Ad Account Saved Audiences

## Overview

This endpoint allows you to retrieve saved audiences associated with a specific ad account in the Facebook Marketing API.

## Reading Saved Audiences

### Endpoint
```
GET /v23.0/{ad-account-id}/saved_audiences
```

### Example Request
```http
GET /v23.0/{ad-account-id}/saved_audiences HTTP/1.1
Host: graph.facebook.com
```

### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `business_id` | numeric string or integer | Optional parameter to assist with filters such as recently used |
| `fields` | list<string> | Fields to be retrieved. Default behavior is to return only the IDs |
| `filtering` | list<Filter Object> | Filters on the report data. This parameter is an array of filter objects |

### Response Format

The response returns a JSON formatted result:

```json
{
  "data": [],
  "paging": {}
}
```

#### Response Fields

- **`data`**: A list of [SavedAudience](/docs/marketing-api/reference/saved-audience/) nodes
- **`paging`**: Pagination information (see [Graph API guide](/docs/graph-api/using-graph-api/#paging) for details)

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 80004 | Too many calls to this ad-account. Wait and try again. See [rate limiting documentation](https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management) |

## Supported Operations

- **Reading**: ✅ Supported
- **Creating**: ❌ Not supported on this endpoint
- **Updating**: ❌ Not supported on this endpoint
- **Deleting**: ❌ Not supported on this endpoint

## Additional Resources

- [Using Graph API guide](/docs/graph-api/using-graph-api/)
- [Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fsaved_audiences&version=v23.0)

## Examples
GET /v23.0/{ad-account-id}/saved_audiences HTTP/1.1

---
**Tags:** Facebook Marketing API, Saved Audiences, Ad Account, Graph API, API Reference, Marketing
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/saved_audiences/
**Processed:** 2025-06-25T15:39:11.870Z