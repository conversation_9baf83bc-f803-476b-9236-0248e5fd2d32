{"title": "Image Crops", "breadcrumbs": [], "content": "<h1 id=\"image-crops\">Image Crops</h1>\n\n<p>Provide aspect ratios for images in different ad placements. Facebook crops your image according to your specifications given or if you provide no cropping we display it using defaults. See <a href=\"/docs/reference/ads-api/adimage/\">Ad Image</a>. For example upload an image to use in ad creative:</p>\n<pre class=\"_5s-8 prettyprint lang-curl prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'filename=@&lt;IMAGE_PATH&gt;'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/</span><code><span class=\"com\">v23.0</span></code><span class=\"com\">/act_&lt;AD_ACCOUNT_ID&gt;/adimages</span><span class=\"pln\">\n    </span></pre><p>Then, provide ad creative by referencing the image hash returned in the previous call along with cropping.</p>\n<p>Crops contains key-value pairs, where the key is a <code>crop key</code> and value is the pixel dimensions of the crop. For all supported keys, see <a href=\"/docs/marketing-api/reference/ads-image-crops\">Ads Image Crops Reference</a>.</p>\n\n<p>Provide value as <code>(x, y)</code> coordinates for the upper-left and bottom-right corners of the cropping rectangle. <code>crop key</code> describes an aspect ratio. The aspect ratio of the box specified by width and height must be as close as possible to the aspect ratio in <code>crop key</code>.</p>\n\n<p>An image's origin <code>(0, 0)</code> is at the upper-left corner. The point, <code>(width - 1, height - 1)</code> is at the bottom-right corner.</p>\n\n<h2 id=\"requirements\">Specification</h2>\n\n<p>When you use this feature, <strong>you should use it for all placements where an ad may appear</strong>. For example, if you provide it for the Right Hand Column, and you also want to use the ad in Newsfeed, you'll need to provide cropping for the Newsfeed placement.</p>\n\n<h2 id=\"limits\">Limitations</h2>\n\n<p>Image crops are only supported for ad creatives with <code>image_file</code> or <code>image_hash</code>. <code>Page posts</code> are not supported. Values must adhere to these constraints:</p>\n\n<ul>\n<li>Points specified by <code>(x, y)</code> must lie within the image. A rectangle that extends beyond the bounds of the image is invalid.</li>\n<li>The rectangle must be the same aspect ratio as specified by the crop key.</li>\n<li>Coordinates cannot contain negative values.</li>\n<li>Facebook Stories do not support image crops. </li>\n</ul>\n\n<p>For example:</p>\n<pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"typ\">Example</span><span class=\"pun\">:{</span><span class=\"str\">\"100x100\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">[</span><span class=\"pln\"> </span><span class=\"pun\">[</span><span class=\"lit\">330</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"lit\">67</span><span class=\"pun\">],</span><span class=\"pln\"> </span><span class=\"pun\">[</span><span class=\"lit\">1080</span><span class=\"pun\">,</span><span class=\"pln\"> </span><span class=\"lit\">817</span><span class=\"pun\">]</span><span class=\"pln\"> </span><span class=\"pun\">]}</span></pre><div style=\"text-align:center;\"></div>\n\n", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference", "/docs/marketing-api/image-crops", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/reference/ads-image-crops"], "url": "https://developers.facebook.com/docs/marketing-api/image-crops", "timestamp": "2025-06-25T15:46:29.300Z"}