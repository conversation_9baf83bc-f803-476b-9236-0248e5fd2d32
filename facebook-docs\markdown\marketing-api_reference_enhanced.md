# Facebook Marketing API v23.0 Reference - Root Nodes and Core Objects

## Summary
Comprehensive reference documentation for Facebook Marketing API v23.0, covering root nodes and core advertising objects including Ad Accounts, Campaigns, Ad Sets, Ads, and Ad Creatives. Provides detailed information about available edges and endpoints for each object type.

## Key Points
- Facebook Marketing API v23.0 follows Graph API architecture with root nodes and edges
- Ad Account is the primary container for all advertising objects and activities
- Campaign hierarchy flows from Campaign → Ad Set → Ad, with Ad Creatives defining appearance
- Insights endpoints are available at multiple levels for performance analytics
- All reference documentation requires Facebook login for full access

## API Endpoints
- `/{AD_ACCOUNT_USER_ID}`
- `/act_{AD_ACCOUNT_ID}`
- `/{AD_ID}`
- `/{AD_CREATIVE_ID}`
- `/{AD_SET_ID}`
- `/{AD_CAMPAIGN_ID}`
- `/adaccounts`
- `/accounts`
- `/promotable_events`
- `/adcreatives`
- `/adimages`
- `/ads`
- `/adsets`
- `/advideos`
- `/campaigns`
- `/customaudiences`
- `/insights`
- `/users`
- `/leads`
- `/previews`
- `/activities`

## Parameters
- AD_ACCOUNT_USER_ID
- AD_ACCOUNT_ID
- AD_ID
- AD_CREATIVE_ID
- AD_SET_ID
- AD_CAMPAIGN_ID
- budget
- schedule
- bid
- targeting

## Content
# Marketing API Reference v23.0

## Overview

This is a complete reference for the Facebook Marketing API v23.0 root nodes and core objects. The API follows Graph API architecture patterns for accessing advertising data and managing campaigns.

**Note:** Access to all reference information requires Facebook login.

## Root Nodes

| Node Pattern | Description |
|--------------|-------------|
| `/{AD_ACCOUNT_USER_ID}` | Someone on Facebook who creates ads. Each ad user can have a role on several ad accounts. |
| `/act_{AD_ACCOUNT_ID}` | Represents the business entity managing ads. |
| `/{AD_ID}` | Contains information for an ad, such as creative elements and measurement information. |
| `/{AD_CREATIVE_ID}` | Format for your image, carousel, collection, or video ad. |
| `/{AD_SET_ID}` | Contains all ads that share the same budget, schedule, bid, and targeting. |
| `/{AD_CAMPAIGN_ID}` | Defines your ad campaigns' objective. Contains one or more ad set. |

## Core Objects

### User

Represents a Facebook user who can create and manage ads.

#### Key Edges
- `/adaccounts` - All ad accounts associated with this person
- `/accounts` - All pages and places that someone is an admin of
- `/promotable_events` - All promotable events you created or promotable page events

### Ad Account

All collections of ad objects in Marketing APIs belong to an ad account. This is the primary container for advertising activities.

#### Popular Edges
- `/adcreatives` - Defines your ad's appearance and content
- `/adimages` - Library of images to use in ad creatives
- `/ads` - Data for an ad, such as creative elements and measurement information
- `/adsets` - Contain all ads that share the same budget, schedule, bid, and targeting
- `/advideos` - Library of videos for use in ad creatives
- `/campaigns` - Define your campaigns' objective and contain one or more ad sets
- `/customaudiences` - The custom audiences owned by/shared with this ad account
- `/insights` - Interface for insights with de-duplication, sorting, and async reports
- `/users` - List of people associated with an ad account

### Ad

An individual ad associated with an ad set.

#### Key Edges
- `/adcreatives` - Defines your ad's appearance and content
- `/insights` - Insights on your advertising performance
- `/leads` - Any leads associated with a Lead Ad
- `/previews` - Generate ad previews from an existing ad

### Ad Set

A group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data.

#### Key Edges
- `/activities` - Log of actions taken on the ad set
- `/adcreatives` - Defines your ad's content and appearance
- `/ads` - Data necessary for an ad, such as creative elements and measurement information
- `/insights` - Insights on your advertising performance

### Ad Campaign

The highest level organizational structure within an ad account, representing a single objective for an advertiser.

#### Key Edges
- `/ads` - Data necessary for an ad, such as creative elements and measurement information
- `/adsets` - Contain all ads that share the same budget, schedule, bid, and targeting
- `/insights` - Insights on your advertising performance

### Ad Creative

The format which provides layout and contains content for the ad.

#### Key Edges
- `/previews` - Generate ad previews from the existing ad creative object

---
**Tags:** Facebook Marketing API, Graph API, Advertising, API Reference, v23.0, Ad Management, Campaign Management  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/v23.0  
**Processed:** 2025-06-25T16:18:07.450Z