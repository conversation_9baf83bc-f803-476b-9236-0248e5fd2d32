# Facebook Marketing API - Ad Account MCM E-commerce Conversions Reference

## Summary
This is a reference page for the Ad Account MCM E-commerce Conversions endpoint in the Facebook Marketing API v23.0. The endpoint currently does not support any CRUD operations (reading, creating, updating, or deleting).

## Key Points
- The MCM E-commerce Conversions endpoint does not support any CRUD operations
- This is part of the Facebook Marketing API v23.0
- The endpoint is related to Multi-Company Manager e-commerce conversion tracking
- All standard operations (read, create, update, delete) are currently disabled

## API Endpoints
- `/ad-account/mcmeconversions`

## Content
# Ad Account MCM E-commerce Conversions

**API Version:** v23.0

## Overview

This endpoint is part of the Facebook Marketing API for managing MCM (Multi-Company Manager) e-commerce conversions at the ad account level.

## Supported Operations

### Reading
This operation is not supported on this endpoint.

### Creating
This operation is not supported on this endpoint.

### Updating
This operation is not supported on this endpoint.

### Deleting
This operation is not supported on this endpoint.

## Current Status

All CRUD operations are currently unavailable for this endpoint. This may indicate that the endpoint is either deprecated, under development, or requires special permissions not covered in the standard documentation.

---
**Tags:** Facebook Marketing API, MCM, E-commerce, Conversions, Ad Account, API Reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/mcmeconversions/  
**Processed:** 2025-06-25T16:15:08.821Z