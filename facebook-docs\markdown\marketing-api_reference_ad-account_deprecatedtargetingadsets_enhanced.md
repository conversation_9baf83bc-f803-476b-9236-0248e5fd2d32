# Facebook Marketing API - Ad Account Deprecated Targeting Ad Sets

## Summary
This endpoint allows you to retrieve ad sets from an ad account that use deprecated targeting options. It provides read-only access to identify ad sets that may need updating due to targeting deprecations or delivery pauses.

## Key Points
- Read-only endpoint for retrieving ad sets with deprecated targeting options
- Supports filtering by deprecation type (deprecating or delivery_paused)
- Returns paginated results with AdSet nodes
- No create, update, or delete operations are supported
- Useful for identifying ad sets that need targeting updates

## API Endpoints
- `GET /v23.0/{ad-account-id}/deprecatedtargetingadsets`

## Parameters
- type (string): deprecating or delivery_paused
- ad-account-id (path parameter): Target ad account ID

## Content
# Ad Account Deprecated Targeting Ad Sets

This endpoint provides access to ad sets within an ad account that use deprecated targeting options or have delivery paused due to targeting issues.

## Reading

Retrieve ad sets with deprecated targeting from a specific ad account.

### Endpoint
```
GET /v23.0/{ad-account-id}/deprecatedtargetingadsets
```

### Example Request

**HTTP**
```http
GET /v23.0/{ad-account-id}/deprecatedtargetingadsets HTTP/1.1
Host: graph.facebook.com
```

**PHP SDK**
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/deprecatedtargetingadsets',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

**JavaScript SDK**
```javascript
FB.api(
    "/{ad-account-id}/deprecatedtargetingadsets",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Parameters

| Parameter | Type | Description | Default |
|-----------|------|-------------|----------|
| `type` | string | Query ad sets according to deprecation type. Valid options: `deprecating`, `delivery_paused` | `deprecating` |

### Response Format

The response returns a JSON object with the following structure:

```json
{
    "data": [],
    "paging": {}
}
```

#### Fields

- **`data`**: A list of AdSet nodes containing the deprecated targeting ad sets
- **`paging`**: Pagination information for navigating through results

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |

## Limitations

This endpoint is **read-only**. The following operations are not supported:
- Creating new entries
- Updating existing entries  
- Deleting entries

## Usage Notes

- Use this endpoint to identify ad sets that may need attention due to deprecated targeting options
- The `type` parameter allows filtering between ad sets that are deprecating vs. those with delivery paused
- Results are paginated - use the paging object to navigate through large result sets

## Examples
HTTP GET request to retrieve deprecated targeting ad sets

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest example

iOS SDK FBSDKGraphRequest example

---
**Tags:** Facebook Marketing API, Ad Account, Ad Sets, Deprecated Targeting, Graph API, Read-only endpoint  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/  
**Processed:** 2025-06-25T16:27:48.079Z