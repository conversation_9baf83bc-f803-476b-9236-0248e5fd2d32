# Facebook Marketing API - Ad Account Ad Labels Reference

## Summary
Complete reference documentation for the Facebook Marketing API's Ad Account Ad Labels endpoint, covering how to read, create, and manage ad labels within an ad account. This endpoint allows developers to organize and categorize ads using custom labels for better campaign management.

## Key Points
- Ad Labels help organize and categorize ads within Facebook ad accounts
- The endpoint supports reading existing labels and creating new ones
- Reading requires no parameters and returns paginated results with summary data
- Creating labels only requires a name parameter
- Updating and deleting operations are not supported on this endpoint

## API Endpoints
- `GET /v23.0/{ad-account-id}/adlabels`
- `POST /v23.0/act_{ad_account_id}/adlabels`

## Parameters
- name (string, required for creation)
- summary (optional for reading - specify fields like 'insights')
- access_token (required for authentication)

## Content
# Ad Account Ad Labels

The Ad Account Ad Labels endpoint allows you to manage labels for organizing ads within a Facebook ad account. This endpoint supports reading existing labels and creating new ones.

## Reading Ad Labels

### Endpoint
```
GET /v23.0/{ad-account-id}/adlabels
```

### Parameters
This endpoint doesn't require any parameters.

### Response Format
The response returns a JSON object with the following structure:
```json
{
  "data": [],
  "paging": {},
  "summary": {}
}
```

#### Response Fields
- **data**: Array of AdLabel nodes
- **paging**: Pagination information for navigating through results
- **summary**: Aggregated information about the edge
  - `insights`: Analytics summary for all objects
  - `total_count`: Total number of objects

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/adlabels HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
try {
  $response = $fb->get(
    '/{ad-account-id}/adlabels',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/adlabels",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Error Codes
- **200**: Permissions error
- **190**: Invalid OAuth 2.0 Access Token
- **80004**: Too many calls to this ad-account (rate limiting)
- **100**: Invalid parameter

## Creating Ad Labels

### Endpoint
```
POST /v23.0/act_{ad_account_id}/adlabels
```

### Required Parameters
- **name** (string): The name for the new ad label

### Code Examples

#### HTTP Request
```http
POST /v23.0/act_<AD_ACCOUNT_ID>/adlabels HTTP/1.1
Host: graph.facebook.com

name=My+Label+1
```

#### PHP SDK
```php
try {
  $response = $fb->post(
    '/act_<AD_ACCOUNT_ID>/adlabels',
    array (
      'name' => 'My Label 1',
    ),
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
}
```

#### cURL
```bash
curl -X POST \
  -F 'name="My Label 1"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adlabels
```

### Return Type
This endpoint supports read-after-write and returns:
```json
{
  "id": "numeric_string"
}
```

### Error Codes
- **200**: Permissions error
- **100**: Invalid parameter

## Limitations
- **Updating**: Not supported on this endpoint
- **Deleting**: Not supported on this endpoint

## API Version
Current version: v23.0

## Examples
GET request to read ad labels

POST request to create new ad label with name 'My Label 1'

PHP SDK implementation for both reading and creating

JavaScript SDK example for reading labels

cURL command for creating labels

---
**Tags:** Facebook Marketing API, Ad Labels, Ad Account, Campaign Management, API Reference, Graph API  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/adlabels/  
**Processed:** 2025-06-25T16:12:32.217Z