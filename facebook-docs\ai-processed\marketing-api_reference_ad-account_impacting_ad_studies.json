{"title": "Facebook Marketing API - Ad Account Impacting Ad Studies", "summary": "This endpoint allows you to read ad studies that impact a specific ad account or any descendant ad objects. It's a read-only endpoint that returns a list of AdStudy nodes with pagination support.", "content": "# Ad Account Impacting Ad Studies\n\n## Overview\n\nThe impacting ad studies endpoint provides access to ad studies that impact a specific ad account or any descendant ad objects. This is a read-only endpoint that does not support creating, updating, or deleting operations.\n\n## Reading\n\nRetrieve the ad studies that impact this ad account or any descendant ad objects.\n\n### HTTP Request\n\n```http\nGET /v23.0/{ad-account-id}/impacting_ad_studies HTTP/1.1\nHost: graph.facebook.com\n```\n\n### Code Examples\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/impacting_ad_studies',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/impacting_ad_studies\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n#### Android SDK\n```java\nnew GraphRequest(\n    AccessToken.getCurrentAccessToken(),\n    \"/{ad-account-id}/impacting_ad_studies\",\n    null,\n    HttpMethod.GET,\n    new GraphRequest.Callback() {\n        public void onCompleted(GraphResponse response) {\n            /* handle the result */\n        }\n    }\n).executeAsync();\n```\n\n#### iOS SDK\n```objc\nFBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]\n                               initWithGraphPath:@\"/{ad-account-id}/impacting_ad_studies\"\n                                      parameters:params\n                                      HTTPMethod:@\"GET\"];\n[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,\n                                      id result,\n                                      NSError *error) {\n    // Handle the result\n}];\n```\n\n### Parameters\n\nThis endpoint doesn't have any parameters.\n\n### Response Format\n\nThe response returns a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Fields\n\n- **data**: A list of AdStudy nodes\n- **paging**: Pagination information for navigating through results\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n\n## Unsupported Operations\n\n- **Creating**: You can't perform this operation on this endpoint\n- **Updating**: You can't perform this operation on this endpoint\n- **Deleting**: You can't perform this operation on this endpoint", "keyPoints": ["Read-only endpoint that returns ad studies impacting an ad account", "Returns a list of AdStudy nodes with pagination support", "No parameters required for the request", "Does not support create, update, or delete operations", "Requires proper OAuth 2.0 access token and permissions"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/impacting_ad_studies"], "parameters": [], "examples": ["PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest implementation", "iOS SDK FBSDKGraphRequest implementation"], "tags": ["Facebook Marketing API", "Ad Studies", "Ad Account", "Graph API", "Read-only endpoint"], "relatedTopics": ["AdStudy nodes", "Graph API pagination", "OAuth 2.0 Access Tokens", "Facebook SDK implementations", "Ad account management"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "processedAt": "2025-06-25T16:14:20.440Z", "processor": "openrouter-claude-sonnet-4"}