# Facebook Marketing API - Ad Account Custom Audiences Terms of Service

## Summary
This endpoint manages Terms of Service acceptance for Custom Audiences within Facebook Ad Accounts. It allows reading existing TOS status and creating new TOS acceptances for custom audience targeting features.

## Key Points
- Manages Terms of Service acceptance for Custom Audiences functionality
- Supports reading existing TOS status and creating new TOS acceptances
- Requires tos_id parameter for creating new TOS records
- Does not support updating or deleting operations
- Returns CustomAudiencesTOS nodes with standard pagination

## API Endpoints
- `GET /v23.0/{ad-account-id}/customaudiencestos`
- `POST /act_{ad_account_id}/customaudiencestos`

## Parameters
- business_id (string, optional)
- tos_id (string, required for POST)
- ad-account-id (path parameter)

## Content
# Ad Account Custom Audiences Terms of Service

The Custom Audiences TOS endpoint manages Terms of Service acceptance for Custom Audiences functionality within Facebook Ad Accounts.

## Reading

### Endpoint
```
GET /v23.0/{ad-account-id}/customaudiencestos
```

### Parameters
This endpoint doesn't have any parameters.

### Response Format
Returns a JSON formatted result:
```json
{
  "data": [],
  "paging": {}
}
```

#### Fields
- **data**: A list of CustomAudiencesTOS nodes
- **paging**: Standard pagination object for navigating results

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/customaudiencestos HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/customaudiencestos',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/customaudiencestos",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

## Creating

You can make a POST request to create new TOS acceptance records.

### Endpoint
```
POST /act_{ad_account_id}/customaudiencestos
```

### Parameters
| Parameter | Type | Description | Required |
|-----------|------|-------------|-----------|
| business_id | string | Business identifier | No |
| tos_id | string | Terms of Service identifier | Yes |

### Return Type
Supports read-after-write and returns:
```json
{
  "success": bool
}
```

## Error Codes

| Error Code | Description |
|------------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |

## Limitations
- **Updating**: Not supported on this endpoint
- **Deleting**: Not supported on this endpoint

## Examples
HTTP GET request example

PHP SDK implementation

JavaScript SDK usage

Android SDK example

iOS SDK example

---
**Tags:** Facebook Marketing API, Custom Audiences, Terms of Service, Ad Account, Graph API  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiencestos/  
**Processed:** 2025-06-25T16:26:26.373Z