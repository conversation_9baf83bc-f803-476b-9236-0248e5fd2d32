{"title": "Facebook Marketing API - Ad Account Adplayables Reference", "summary": "This reference documentation covers the Ad Account Adplayables endpoint in the Facebook Marketing API, which manages playable assets associated with an ad account. It provides methods for reading existing playable assets and creating new ones through file uploads.", "content": "# Ad Account Adplayables\n\nThe Ad Account Adplayables endpoint manages playable assets associated with a Facebook ad account. This endpoint allows you to read existing playable content and create new playable assets.\n\n## Reading Adplayables\n\nRetrieves the playable assets associated with an ad account.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/adplayables\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Response Format\nReturns a JSON formatted result:\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Response Fields\n- **data**: A list of PlayableContent nodes\n- **paging**: Pagination information (see Graph API guide for details)\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/adplayables HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/adplayables',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/adplayables\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n#### Android SDK\n```java\nnew GraphRequest(\n    AccessToken.getCurrentAccessToken(),\n    \"/{ad-account-id}/adplayables\",\n    null,\n    HttpMethod.GET,\n    new GraphRequest.Callback() {\n        public void onCompleted(GraphResponse response) {\n            /* handle the result */\n        }\n    }\n).executeAsync();\n```\n\n#### iOS SDK\n```objc\nFBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]\n                               initWithGraphPath:@\"/{ad-account-id}/adplayables\"\n                                      parameters:params\n                                      HTTPMethod:@\"GET\"];\n[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,\n                                      id result,\n                                      NSError *error) {\n    // Handle the result\n}];\n```\n\n### Error Codes\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 100 | Invalid parameter |\n\n## Creating Adplayables\n\nCreates a new playable asset by uploading an HTML file.\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/adplayables\n```\n\n### Parameters\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|----------|\n| name | string | The name of the playable asset | Yes |\n| source | file | The local file path of the HTML playable asset | Yes |\n\n### Return Type\nReturns a struct containing:\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n### Error Codes\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n\n## Updating and Deleting\n\nBoth updating and deleting operations are not supported for this endpoint.\n\n## Related Resources\n- [PlayableContent Reference](/docs/graph-api/reference/playable-content/)\n- [Graph API Usage Guide](/docs/graph-api/using-graph-api/)\n- [Graph API Pagination](/docs/graph-api/using-graph-api/#paging)", "keyPoints": ["Manages playable assets associated with Facebook ad accounts", "Supports reading existing playable content and creating new assets", "Requires HTML file upload when creating new playable assets", "Returns PlayableContent nodes with pagination support", "Does not support updating or deleting operations"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/adplayables", "POST /act_{ad_account_id}/adplayables"], "parameters": ["name (string, required for creation)", "source (file, required for creation)", "ad-account-id (path parameter)"], "examples": ["HTTP GET request example", "PHP SDK implementation", "JavaScript SDK usage", "Android SDK integration", "iOS SDK implementation"], "tags": ["Facebook Marketing API", "Ad Account", "Playable Assets", "Graph API", "HTML Playables"], "relatedTopics": ["Playable<PERSON>ontent", "Graph API pagination", "OAuth 2.0 Access Tokens", "Facebook SDK implementations", "Ad account management"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/adplayables/", "processedAt": "2025-06-25T16:19:58.260Z", "processor": "openrouter-claude-sonnet-4"}