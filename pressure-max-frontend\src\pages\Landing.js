import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { facebookAPI } from '../services/api';
import { User, LogOut, Eye, EyeOff, Facebook, ArrowRight, Shield, Zap, Users } from 'lucide-react';
import toast from 'react-hot-toast';

const Landing = () => {
  const { user, isAuthenticated, login, register, logout, getToken } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('facebook');
  const [showToken, setShowToken] = useState(false);
  const [loading, setLoading] = useState(false);

  const loginForm = useForm();
  const registerForm = useForm();

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  // Check for Facebook OAuth callback
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    
    if (code && state && !isAuthenticated) {
      handleFacebookCallback(code, state);
    }
  }, [isAuthenticated]);

  const onLogin = async (data) => {
    setLoading(true);
    const result = await login(data);
    if (result.success) {
      navigate('/dashboard');
    }
    setLoading(false);
  };

  const onRegister = async (data) => {
    setLoading(true);
    const result = await register(data);
    if (result.success) {
      navigate('/dashboard');
    }
    setLoading(false);
  };

  const handleFacebookLogin = async () => {
    try {
      setLoading(true);
      const redirectUri = `${window.location.origin}/landing`;
      const response = await facebookAPI.getOAuthUrl(redirectUri);
      
      if (response.data.oauthUrl) {
        localStorage.setItem('facebook_oauth_state', response.data.state);
        window.location.href = response.data.oauthUrl;
      } else {
        throw new Error('No OAuth URL received');
      }
    } catch (error) {
      setLoading(false);
      toast.error('Failed to initiate Facebook login: ' + error.message);
    }
  };

  const handleFacebookCallback = async (code, state) => {
    try {
      setLoading(true);
      const storedState = localStorage.getItem('facebook_oauth_state');
      
      if (state !== storedState) {
        throw new Error('Invalid state parameter');
      }

      const redirectUri = `${window.location.origin}/landing`;
      const response = await facebookAPI.handleOAuthCallback(code, state, redirectUri);
      
      if (response.data.user && response.data.tokens) {
        const { user: userData, tokens } = response.data;
        
        localStorage.setItem('accessToken', tokens.accessToken);
        localStorage.setItem('refreshToken', tokens.refreshToken);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.removeItem('facebook_oauth_state');
        
        toast.success('Facebook login successful!');
        navigate('/dashboard');
      }
    } catch (error) {
      setLoading(false);
      toast.error('Facebook login failed: ' + error.message);
      window.history.replaceState({}, document.title, '/landing');
    }
  };

  return (
    <div className="landing-page">
      {/* Hero Section */}
      <div className="hero-section">
        <div className="hero-content">
          <div className="hero-text">
            <h1>🚀 Pressure Max</h1>
            <p className="hero-subtitle">
              Powerful Facebook Marketing API Integration & Campaign Management
            </p>
            <p className="hero-description">
              Streamline your Facebook advertising with our comprehensive API testing interface, 
              campaign management tools, and real-time analytics dashboard.
            </p>
            
            <div className="features-grid">
              <div className="feature-item">
                <Shield size={24} />
                <span>Secure Authentication</span>
              </div>
              <div className="feature-item">
                <Zap size={24} />
                <span>Real-time API Testing</span>
              </div>
              <div className="feature-item">
                <Users size={24} />
                <span>Campaign Management</span>
              </div>
            </div>
          </div>
          
          <div className="auth-container">
            <div className="auth-card">
              <h2>Get Started</h2>
              
              <div className="auth-tabs">
                <button 
                  className={activeTab === 'facebook' ? 'active' : ''}
                  onClick={() => setActiveTab('facebook')}
                >
                  <Facebook size={16} />
                  Facebook Login
                </button>
                <button 
                  className={activeTab === 'login' ? 'active' : ''}
                  onClick={() => setActiveTab('login')}
                >
                  Email Login
                </button>
                <button 
                  className={activeTab === 'register' ? 'active' : ''}
                  onClick={() => setActiveTab('register')}
                >
                  Register
                </button>
              </div>

              {activeTab === 'facebook' ? (
                <div className="facebook-login-section">
                  <div className="facebook-login-info">
                    <h3>
                      <Facebook size={20} />
                      Quick Facebook Login
                    </h3>
                    <p>Connect with your Facebook account to access:</p>
                    <ul>
                      <li>✅ Automatic Marketing API permissions</li>
                      <li>✅ Campaign management tools</li>
                      <li>✅ Real-time analytics</li>
                      <li>✅ Lead form integration</li>
                    </ul>
                  </div>
                  
                  <button
                    onClick={handleFacebookLogin}
                    disabled={loading}
                    className="facebook-login-btn"
                  >
                    <Facebook size={20} />
                    {loading ? 'Connecting...' : 'Continue with Facebook'}
                    <ArrowRight size={16} />
                  </button>
                  
                  <p className="facebook-note">
                    Using App ID: <code>***************</code><br/>
                    This will automatically grant marketing API permissions.
                  </p>
                </div>
              ) : activeTab === 'login' ? (
                <form onSubmit={loginForm.handleSubmit(onLogin)} className="auth-form">
                  <div className="form-group">
                    <label>Email:</label>
                    <input
                      type="email"
                      {...loginForm.register('email', { required: 'Email is required' })}
                      placeholder="Enter your email"
                    />
                    {loginForm.formState.errors.email && (
                      <span className="error">{loginForm.formState.errors.email.message}</span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Password:</label>
                    <input
                      type="password"
                      {...loginForm.register('password', { required: 'Password is required' })}
                      placeholder="Enter your password"
                    />
                    {loginForm.formState.errors.password && (
                      <span className="error">{loginForm.formState.errors.password.message}</span>
                    )}
                  </div>

                  <button type="submit" disabled={loading} className="submit-btn">
                    {loading ? 'Logging in...' : 'Login'}
                    <ArrowRight size={16} />
                  </button>
                </form>
              ) : (
                <form onSubmit={registerForm.handleSubmit(onRegister)} className="auth-form">
                  <div className="form-group">
                    <label>First Name:</label>
                    <input
                      type="text"
                      {...registerForm.register('firstName', { required: 'First name is required' })}
                      placeholder="Enter your first name"
                    />
                    {registerForm.formState.errors.firstName && (
                      <span className="error">{registerForm.formState.errors.firstName.message}</span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Last Name:</label>
                    <input
                      type="text"
                      {...registerForm.register('lastName', { required: 'Last name is required' })}
                      placeholder="Enter your last name"
                    />
                    {registerForm.formState.errors.lastName && (
                      <span className="error">{registerForm.formState.errors.lastName.message}</span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Email:</label>
                    <input
                      type="email"
                      {...registerForm.register('email', { required: 'Email is required' })}
                      placeholder="Enter your email"
                    />
                    {registerForm.formState.errors.email && (
                      <span className="error">{registerForm.formState.errors.email.message}</span>
                    )}
                  </div>

                  <div className="form-group">
                    <label>Password:</label>
                    <input
                      type="password"
                      {...registerForm.register('password', { 
                        required: 'Password is required',
                        minLength: { value: 6, message: 'Password must be at least 6 characters' }
                      })}
                      placeholder="Enter your password"
                    />
                    {registerForm.formState.errors.password && (
                      <span className="error">{registerForm.formState.errors.password.message}</span>
                    )}
                  </div>

                  <button type="submit" disabled={loading} className="submit-btn">
                    {loading ? 'Creating Account...' : 'Create Account'}
                    <ArrowRight size={16} />
                  </button>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="landing-footer">
        <p>Pressure Max API Testing Interface - Built with React & Supabase</p>
        <p>Backend API: <code>http://localhost:3000</code></p>
      </footer>
    </div>
  );
};

export default Landing;
