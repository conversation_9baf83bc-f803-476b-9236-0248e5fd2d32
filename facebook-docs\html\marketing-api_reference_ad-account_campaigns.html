<div class="_1dyy" id="u_0_15_gW"><div class="_33zv _3e1u"><div class="_33zw" tabindex="0" role="button">On This Page<div><i class="img sp_zD_MvjcHflF sx_a3c10c" alt="" data-visualcompletion="css-img"></i><i class="hidden_elem img sp_zD_MvjcHflF sx_6874ff" alt="" data-visualcompletion="css-img"></i></div></div><div class="_5-24 hidden_elem"><a href="#overview"></a></div><div class="_5-24 hidden_elem"><a href="#Reading">Reading</a></div><div class="_5-24 hidden_elem"><a href="#example">Example</a></div><div class="_5-24 hidden_elem"><a href="#parameters">Parameters</a></div><div class="_5-24 hidden_elem"><a href="#fields">Fields</a></div><div class="_5-24 hidden_elem"><a href="#error-codes">Error Codes</a></div><div class="_5-24 hidden_elem"><a href="#Creating">Creating</a></div><div class="_5-24 hidden_elem"><a href="#example-2">Example</a></div><div class="_5-24 hidden_elem"><a href="#parameters-2">Parameters</a></div><div class="_5-24 hidden_elem"><a href="#return-type">Return Type</a></div><div class="_5-24 hidden_elem"><a href="#error-codes-2">Error Codes</a></div><div class="_5-24 hidden_elem"><a href="#Updating">Updating</a></div><div class="_5-24 hidden_elem"><a href="#Deleting">Deleting</a></div><div class="_5-24 hidden_elem"><a href="#parameters-3">Parameters</a></div><div class="_5-24 hidden_elem"><a href="#return-type-2">Return Type</a></div><div class="_5-24 hidden_elem"><a href="#error-codes-3">Error Codes</a></div></div></div><div id="documentation_body_pagelet" data-referrer="documentation_body_pagelet"><div class="_34yh" id="u_0_1_mb"><div data-click-area="main"><div class="_4-u2 _57mb _1u44 _4-u8"><div class="_4-u3 _5rva _mog"><div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_Cu"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_CY"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"><i class="img sp_WbXBGqjC54o sx_514a5c"></i></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_mx"></div></span></div></div></div></div><div class="_1xb4 _3-98"><div class="_4-u2 _57mb _1u44 _4-u8 _3la3"><div class="_4-u3 _588p"><h1 id="overview"></h1><h1>Ad Account, Ad Campaigns</h1><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3"><div class="_4-u3 _588p"><p>The ad campaigns associated with a given ad account.</p><p>On May 1, 2018 with the release of Marketing API 3.0 we removed <code>kpi_custom_conversion_id</code>, <code>kpi_type</code>, and <code>kpi_results</code>.</p><p>Beginning September 15, 2022, with the release of Marketing API v15.0, advertisers will no longer be allowed to create incremental conversion optimization campaigns. Existing conversion optimization campaigns will behave normally.</p><span><h3>Ads About Social Issues, Elections, and Politics</h3>
<div class="_57yz _57y_ _3-8p"><div class="_57y-"><p>Beginning with the release of Marketing API v15.0, advertisers will no longer be able to create Special Ad Audiences. See <a href="/docs/marketing-api/audiences/special-ad-category/#special-ad-audiences">Special Ad Audiences details here</a> for more information.</p>
</div></div></span><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>Returns the campaigns under this ad account. A request with no filters returns only campaigns that were not archived or deleted.</p>
</div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_hS"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_bJ">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_6h">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_xm">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_GW">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_k2">iOS SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_b_p4">cURL</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fcampaigns%3Feffective_status%3D%255B%2522ACTIVE%2522%252C%2522PAUSED%2522%255D%26fields%3Dname%252Cobjective&amp;version=v23.0" target="_blank">Graph API Explorer<i class="_3-99 img sp_c_epTrfICMy sx_7b2121"></i></a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_c_LY" style=""><code><span class="pln">GET </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/</span><span class="pln">act_</span><span class="pun">&lt;</span><span class="pln">AD_ACCOUNT_ID</span><span class="pun">&gt;</span><span class="str">/campaigns?effective_status=%5B%22ACTIVE%22%2C%22PAUSED%22%5D&amp;fields=name%2Cobjective HTTP/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_d_K8" style=""><code><span class="com">/* PHP SDK v5.0.0 */</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">try</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  </span><span class="com">// Returns a `Facebook\FacebookResponse` object</span><span class="pln">
  $response </span><span class="pun">=</span><span class="pln"> $fb</span><span class="pun">-&gt;</span><span class="kwd">get</span><span class="pun">(</span><span class="pln">
    </span><span class="str">'/act_&lt;AD_ACCOUNT_ID&gt;/campaigns?effective_status=%5B%22ACTIVE%22%2C%22PAUSED%22%5D&amp;fields=name%2Cobjective'</span><span class="pun">,</span><span class="pln">
    </span><span class="str">'{access-token}'</span><span class="pln">
  </span><span class="pun">);</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookResponseException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Graph returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookSDKException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Facebook SDK returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln">
$graphNode </span><span class="pun">=</span><span class="pln"> $response</span><span class="pun">-&gt;</span><span class="pln">getGraphNode</span><span class="pun">();</span><span class="pln">
</span><span class="com">/* handle the result */</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_e_co" style=""><code><span class="com">/* make the API call */</span><span class="pln">
FB</span><span class="pun">.</span><span class="pln">api</span><span class="pun">(</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/campaigns"</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"effective_status"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"[\"ACTIVE\",\"PAUSED\"]"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"fields"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"name,objective"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="kwd">function</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="kwd">if</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response </span><span class="pun">&amp;&amp;</span><span class="pln"> </span><span class="pun">!</span><span class="pln">response</span><span class="pun">.</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="com">/* handle the result */</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">);</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_f_+6" style=""><code><span class="typ">Bundle</span><span class="pln"> </span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">Bundle</span><span class="pun">();</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"effective_status"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"[\"ACTIVE\",\"PAUSED\"]"</span><span class="pun">);</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"fields"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"name,objective"</span><span class="pun">);</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">(</span><span class="pln">
    </span><span class="typ">AccessToken</span><span class="pun">.</span><span class="pln">getCurrentAccessToken</span><span class="pun">(),</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/campaigns"</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">params</span><span class="pun">,</span><span class="pln">
    </span><span class="typ">HttpMethod</span><span class="pun">.</span><span class="pln">GET</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">.</span><span class="typ">Callback</span><span class="pun">()</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="kwd">public</span><span class="pln"> </span><span class="kwd">void</span><span class="pln"> onCompleted</span><span class="pun">(</span><span class="typ">GraphResponse</span><span class="pln"> response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
            </span><span class="com">/* handle the result */</span><span class="pln">
        </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">).</span><span class="pln">executeAsync</span><span class="pun">();</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_g_ht" style=""><code><span class="typ">NSDictionary</span><span class="pln"> </span><span class="pun">*</span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="pun">@{</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"effective_status"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"[\"ACTIVE\",\"PAUSED\"]"</span><span class="pun">,</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"fields"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"name,objective"</span><span class="pun">,</span><span class="pln">
</span><span class="pun">};</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> </span><span class="pun">*</span><span class="pln">request </span><span class="pun">=</span><span class="pln"> </span><span class="pun">[[</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> alloc</span><span class="pun">]</span><span class="pln">
                               initWithGraphPath</span><span class="pun">:@</span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/campaigns"</span><span class="pln">
                                      parameters</span><span class="pun">:</span><span class="kwd">params</span><span class="pln">
                                      </span><span class="typ">HTTPMethod</span><span class="pun">:@</span><span class="str">"GET"</span><span class="pun">];</span><span class="pln">
</span><span class="pun">[</span><span class="pln">request startWithCompletionHandler</span><span class="pun">:^(</span><span class="typ">FBSDKGraphRequestConnection</span><span class="pln"> </span><span class="pun">*</span><span class="pln">connection</span><span class="pun">,</span><span class="pln">
                                      id result</span><span class="pun">,</span><span class="pln">
                                      </span><span class="typ">NSError</span><span class="pln"> </span><span class="pun">*</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="com">// Handle the result</span><span class="pln">
</span><span class="pun">}];</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_h_bn" style=""><code><span class="pln">curl </span><span class="pun">-</span><span class="pln">X GET </span><span class="pun">-</span><span class="pln">G \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">'effective_status=[
       "ACTIVE",
       "PAUSED"
     ]'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">'fields="name,objective"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">d </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
  https</span><span class="pun">:</span><span class="com">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/campaigns</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_i_lY"><tr class="row_0"><td><div class="_yc"><span><code>date_preset</code></span></div><div class="_yb">enum{today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year}</div></td><td><p class="_yd"></p><div><div><p>Predefine date range used to aggregate insights metrics.</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>effective_status</code></span></div><div class="_yb">list&lt;enum{ACTIVE, PAUSED, DELETED, PENDING_REVIEW, DISAPPROVED, PREAPPROVED, PENDING_BILLING_INFO, CAMPAIGN_PAUSED, ARCHIVED, ADSET_PAUSED, IN_PROCESS, WITH_ISSUES}&gt;</div></td><td><div>Default value: <code>Vec</code></div><p class="_yd"></p><div><div><p>effective status for the campaigns</p>
</div></div><p></p></td></tr><tr class="row_2"><td><div class="_yc"><span><code>is_completed</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>If <code>true</code>, we return completed campaigns.</p>
</div></div><p></p></td></tr><tr class="row_3 _5m29 _5m27"><td><div class="_yc"><span><code>time_range</code></span></div><div class="_yb">{'since':YYYY-MM-DD,'until':YYYY-MM-DD}</div></td><td><p class="_yd"></p><div><div><p>Date range used to aggregate insights metrics</p>
</div></div><p></p></td></tr><tr class="row_3-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>since</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>A date in the format of "YYYY-MM-DD", which means from the beginning midnight of that day.</p>
</div></div><p></p></td></tr><tr class="row_3-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>until</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>A date in the format of "YYYY-MM-DD", which means to the beginning midnight of the following day.</p>
</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id="fields">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class="_3hux"><p>{
    "<code>data</code>": [],
    "<code>paging</code>": {},
    "<code>summary</code>": {}
}</p>
</pre><div class="_3-8o"><h4><code>data</code></h4>A list of <a target="_blank" href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> nodes.</div><div class="_3-8o"><h4><code>paging</code></h4>For more details about pagination, see the <a href="/docs/graph-api/using-graph-api/#paging">Graph API guide</a>.</div><div class="_3-8o"><h4><code>summary</code></h4><p>Aggregated information about the edge, such as counts. Specify the fields to fetch in the summary param (like <code>summary=insights</code>).</p><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><code>insights</code></span></div><div class="_yb _yc"><span>Edge&lt;AdsInsights&gt;</span></div></td><td><p class="_yd"></p><div><p>Analytics summary for all objects</p>
</div><p></p></td></tr><tr><td><div class="_yc"><span><code>total_count</code></span></div><div class="_yb _yc"><span>unsigned int32</span></div></td><td><p class="_yd"></p><div><p>Total number of objects</p>
</div><p></p><div class="_2pic"><a href="https://developers.facebook.com/docs/graph-api/using-graph-api/#fields" target="blank"><span class="_1vet">Default</span></a></div></td></tr></tbody></table></div></div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr><tr><td>3018</td><td>The start date of the time range cannot be beyond 37 months from the current date</td></tr></tbody></table></div></div></div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Creating">Creating</h2><div class="_844_"><div class="_3-98">You can make a POST request to <code>campaigns</code> edge from the following paths: <ul><li><a href="/docs/marketing-api/reference/ad-account/campaigns/"><code>/act_{ad_account_id}/campaigns</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> will be created.</div><div><h3 id="example-2">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_j_X7"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_k_TF">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_l_Av">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_m_+/">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_n_vE">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_o_5/">iOS SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_p_SG">cURL</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fcampaigns%3Fname%3DMy%2Bcampaign%26objective%3DOUTCOME_TRAFFIC%26status%3DPAUSED%26special_ad_categories%3D%255B%255D&amp;version=v23.0" target="_blank">Graph API Explorer<i class="_3-99 img sp_c_epTrfICMy sx_7b2121"></i></a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_q_ay" style=""><code><span class="pln">POST </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/</span><span class="pln">act_</span><span class="pun">&lt;</span><span class="pln">AD_ACCOUNT_ID</span><span class="pun">&gt;</span><span class="str">/campaigns HTTP/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com

name</span><span class="pun">=</span><span class="typ">My</span><span class="pun">+</span><span class="pln">campaign</span><span class="pun">&amp;</span><span class="pln">objective</span><span class="pun">=</span><span class="pln">OUTCOME_TRAFFIC</span><span class="pun">&amp;</span><span class="pln">status</span><span class="pun">=</span><span class="pln">PAUSED</span><span class="pun">&amp;</span><span class="pln">special_ad_categories</span><span class="pun">=%</span><span class="lit">5B</span><span class="pun">%</span><span class="lit">5D</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_r_6p" style=""><code><span class="com">/* PHP SDK v5.0.0 */</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">try</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  </span><span class="com">// Returns a `Facebook\FacebookResponse` object</span><span class="pln">
  $response </span><span class="pun">=</span><span class="pln"> $fb</span><span class="pun">-&gt;</span><span class="pln">post</span><span class="pun">(</span><span class="pln">
    </span><span class="str">'/act_&lt;AD_ACCOUNT_ID&gt;/campaigns'</span><span class="pun">,</span><span class="pln">
    array </span><span class="pun">(</span><span class="pln">
      </span><span class="str">'name'</span><span class="pln"> </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'My campaign'</span><span class="pun">,</span><span class="pln">
      </span><span class="str">'objective'</span><span class="pln"> </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'OUTCOME_TRAFFIC'</span><span class="pun">,</span><span class="pln">
      </span><span class="str">'status'</span><span class="pln"> </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'PAUSED'</span><span class="pun">,</span><span class="pln">
      </span><span class="str">'special_ad_categories'</span><span class="pln"> </span><span class="pun">=&gt;</span><span class="pln"> </span><span class="str">'[]'</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">),</span><span class="pln">
    </span><span class="str">'{access-token}'</span><span class="pln">
  </span><span class="pun">);</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookResponseException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Graph returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookSDKException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Facebook SDK returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln">
$graphNode </span><span class="pun">=</span><span class="pln"> $response</span><span class="pun">-&gt;</span><span class="pln">getGraphNode</span><span class="pun">();</span><span class="pln">
</span><span class="com">/* handle the result */</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_s_WH" style=""><code><span class="com">/* make the API call */</span><span class="pln">
FB</span><span class="pun">.</span><span class="pln">api</span><span class="pun">(</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/campaigns"</span><span class="pun">,</span><span class="pln">
    </span><span class="str">"POST"</span><span class="pun">,</span><span class="pln">
    </span><span class="pun">{</span><span class="pln">
        </span><span class="str">"name"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"My campaign"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"objective"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"OUTCOME_TRAFFIC"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"status"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"PAUSED"</span><span class="pun">,</span><span class="pln">
        </span><span class="str">"special_ad_categories"</span><span class="pun">:</span><span class="pln"> </span><span class="str">"[]"</span><span class="pln">
    </span><span class="pun">},</span><span class="pln">
    </span><span class="kwd">function</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="kwd">if</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response </span><span class="pun">&amp;&amp;</span><span class="pln"> </span><span class="pun">!</span><span class="pln">response</span><span class="pun">.</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="com">/* handle the result */</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">);</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_t_Hi" style=""><code><span class="typ">Bundle</span><span class="pln"> </span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">Bundle</span><span class="pun">();</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"name"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"My campaign"</span><span class="pun">);</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"objective"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"OUTCOME_TRAFFIC"</span><span class="pun">);</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"status"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"PAUSED"</span><span class="pun">);</span><span class="pln">
</span><span class="kwd">params</span><span class="pun">.</span><span class="pln">putString</span><span class="pun">(</span><span class="str">"special_ad_categories"</span><span class="pun">,</span><span class="pln"> </span><span class="str">"[]"</span><span class="pun">);</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">(</span><span class="pln">
    </span><span class="typ">AccessToken</span><span class="pun">.</span><span class="pln">getCurrentAccessToken</span><span class="pun">(),</span><span class="pln">
    </span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/campaigns"</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">params</span><span class="pun">,</span><span class="pln">
    </span><span class="typ">HttpMethod</span><span class="pun">.</span><span class="pln">POST</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">.</span><span class="typ">Callback</span><span class="pun">()</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="kwd">public</span><span class="pln"> </span><span class="kwd">void</span><span class="pln"> onCompleted</span><span class="pun">(</span><span class="typ">GraphResponse</span><span class="pln"> response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
            </span><span class="com">/* handle the result */</span><span class="pln">
        </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">).</span><span class="pln">executeAsync</span><span class="pun">();</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_u_7G" style=""><code><span class="typ">NSDictionary</span><span class="pln"> </span><span class="pun">*</span><span class="kwd">params</span><span class="pln"> </span><span class="pun">=</span><span class="pln"> </span><span class="pun">@{</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"name"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"My campaign"</span><span class="pun">,</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"objective"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"OUTCOME_TRAFFIC"</span><span class="pun">,</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"status"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"PAUSED"</span><span class="pun">,</span><span class="pln">
  </span><span class="pun">@</span><span class="str">"special_ad_categories"</span><span class="pun">:</span><span class="pln"> </span><span class="pun">@</span><span class="str">"[]"</span><span class="pun">,</span><span class="pln">
</span><span class="pun">};</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> </span><span class="pun">*</span><span class="pln">request </span><span class="pun">=</span><span class="pln"> </span><span class="pun">[[</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> alloc</span><span class="pun">]</span><span class="pln">
                               initWithGraphPath</span><span class="pun">:@</span><span class="str">"/act_&lt;AD_ACCOUNT_ID&gt;/campaigns"</span><span class="pln">
                                      parameters</span><span class="pun">:</span><span class="kwd">params</span><span class="pln">
                                      </span><span class="typ">HTTPMethod</span><span class="pun">:@</span><span class="str">"POST"</span><span class="pun">];</span><span class="pln">
</span><span class="pun">[</span><span class="pln">request startWithCompletionHandler</span><span class="pun">:^(</span><span class="typ">FBSDKGraphRequestConnection</span><span class="pln"> </span><span class="pun">*</span><span class="pln">connection</span><span class="pun">,</span><span class="pln">
                                      id result</span><span class="pun">,</span><span class="pln">
                                      </span><span class="typ">NSError</span><span class="pln"> </span><span class="pun">*</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="com">// Handle the result</span><span class="pln">
</span><span class="pun">}];</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_v_fK" style=""><code><span class="pln">curl </span><span class="pun">-</span><span class="pln">X POST \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'name="My campaign"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'objective="OUTCOME_TRAFFIC"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'status="PAUSED"'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'special_ad_categories=[]'</span><span class="pln"> \
  </span><span class="pun">-</span><span class="pln">F </span><span class="str">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class="pln"> \
  https</span><span class="pun">:</span><span class="com">//graph.facebook.com/v23.0/act_&lt;AD_ACCOUNT_ID&gt;/campaigns</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters-2">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_w_qf"><tr class="row_0"><td><div class="_yc"><span><code>adlabels</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p><a href="/docs/marketing-api/reference/ad-label">Ad Labels</a> associated with this campaign</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>bid_strategy</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_x_wy"><i class="img sp_WbXBGqjC54o sx_ba4112"></i></a></div><div class="_yb">enum{LOWEST_COST_WITHOUT_CAP, LOWEST_COST_WITH_BID_CAP, COST_CAP, LOWEST_COST_WITH_MIN_ROAS}</div></td><td><p class="_yd"></p><div><div><p>Choose bid strategy for this campaign to suit your specific business goals.
        Each strategy has tradeoffs and may be available for certain <code>optimization_goal</code>s:<br>
        <code>LOWEST_COST_WITHOUT_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> without limiting your bid amount. This is the best strategy
        if you care most about cost efficiency. However with this strategy it may be harder to get
        stable average costs as you spend. This strategy is also known as <em>automatic bidding</em>.
        Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br>
        <code>LOWEST_COST_WITH_BID_CAP</code>: Designed to get the most results for your budget based on
        your ad set <code>optimization_goal</code> while limiting actual bid to your specified
        amount. With a bid cap you have more control over your
        cost per actual optimization event. However if you set a limit which is too low you may
        get less ads delivery. If you select this, you must provide
        a bid cap in the <code>bid_amount</code> field for each ad set in this ad campaign.
        Note: during creation this is the default bid strategy if you don't specify.
        This strategy is also known as <em>manual maximum-cost bidding</em>.
        Learn more in <a href="https://www.facebook.com/business/help/721453268045071">Ads Help Center, About bid strategies: Lowest cost</a>.<br></p>

<p><strong>Notes:</strong></p>

<ul>
<li>If you do not enable campaign budget optimization, you should set <code>bid_strategy</code> at ad set level.</li>
<li><code>TARGET_COST</code> bidding strategy has been deprecated with <a href="/docs/graph-api/changelog/version9.0">Marketing API v9</a>.</li>
</ul>
</div></div><p></p></td></tr><tr class="row_2 _5m27"><td><div class="_yc"><span><code>budget_schedule_specs</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div><p>Initial high demand periods to be created with the campaign.<br>
Provide list of <code>time_start</code>, <code>time_end</code>,<code>budget_value</code>, and <code>budget_value_type</code>.<br>For example,<br>-F 'budget_schedule_specs=[{<br>
"time_start":1699081200,<br>
"time_end":1699167600,<br>
"budget_value":100,<br>
"budget_value_type":"ABSOLUTE"<br>
}]'
<br>
See <a href="https://developers.facebook.com/docs/graph-api/reference/high-demand-period/">High Demand Period</a> for more details on each field.</p>
</div></div><p></p></td></tr><tr class="row_2-0 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>id</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-1 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>time_start</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-2 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>time_end</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-3 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>budget_value</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-4 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>budget_value_type</code></span></div><div class="_yb">enum{ABSOLUTE, MULTIPLIER}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-5 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>recurrence_type</code></span></div><div class="_yb">enum{ONE_TIME, WEEKLY}</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-6 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>weekly_schedule</code></span></div><div class="_yb">list&lt;JSON or object-like arrays&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-6-0 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>days</code></span></div><div class="_yb">list&lt;int64&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-6-1 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>minute_start</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-6-2 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>minute_end</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_2-6-3 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>timezone_type</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_3 _5m29"><td><div class="_yc"><span><code>buying_type</code></span></div><div class="_yb">string</div></td><td><div>Default value: <code>AUCTION</code></div><p class="_yd"></p><div><div><p>This field will help Facebook make optimizations to delivery, pricing, and limits. All ad sets in this campaign must match the buying type. Possible values are: <br><code>AUCTION</code> (default)<br><code>RESERVED</code> (for <a href="/docs/marketing-api/reachandfrequency">reach and frequency ads</a>).</p>
</div></div><p></p></td></tr><tr class="row_4"><td><div class="_yc"><span><code>campaign_optimization_type</code></span></div><div class="_yb">enum{NONE, ICO_ONLY}</div></td><td><p class="_yd"></p><div><div><p>campaign_optimization_type</p>
</div></div><p></p></td></tr><tr class="row_5 _5m29"><td><div class="_yc"><span><code>daily_budget</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Daily budget of this campaign. All adsets under this
        campaign will share this budget. You can either set budget at the
        campaign level or at the adset level, not both.</p>
</div></div><p></p></td></tr><tr class="row_6"><td><div class="_yc"><span><code>execution_options</code></span></div><div class="_yb">list&lt;enum{validate_only, include_recommendations}&gt;</div></td><td><div>Default value: <code>Set</code></div><p class="_yd"></p><div><div><p>An execution setting<br> <code>validate_only</code>: when this option is specified, the API call will not perform the mutation but will run through the validation rules against values of each field. <br><code>include_recommendations</code>: this option cannot be used by itself. When this option is used, recommendations  for ad object's configuration will be included. A separate section <a href="/docs/marketing-api/reference/ad-recommendation">recommendations</a> will be included in the response, but only if recommendations for this specification exist.<br>If the call passes validation or review, response will be <code>{"success": true}</code>. If the call does not pass, an error will be returned with more details. These options can be used to improve any UI to display errors to the user much sooner, e.g. as soon as a new value is typed into any field corresponding to this ad object, rather than at the upload/save stage, or after review.</p>
</div></div><p></p></td></tr><tr class="row_7 _5m29"><td><div class="_yc"><span><code>is_skadnetwork_attribution</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>To create an iOS 14 campaign, enable SKAdNetwork attribution for this campaign.</p>
</div></div><p></p></td></tr><tr class="row_8"><td><div class="_yc"><span><code>is_using_l3_schedule</code></span></div><div class="_yb">boolean</div></td><td><p class="_yd"></p><div><div><p>is_using_l3_schedule</p>
</div></div><p></p></td></tr><tr class="row_9 _5m29"><td><div class="_yc"><span><code>iterative_split_test_configs</code></span></div><div class="_yb">list&lt;Object&gt;</div></td><td><p class="_yd"></p><div><div><p>Array of Iterative Split Test Configs created under this campaign .</p>
</div></div><p></p></td></tr><tr class="row_10"><td><div class="_yc"><span><code>lifetime_budget</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>Lifetime budget of this campaign. All adsets under
        this campaign will share this budget. You can either set budget at the
        campaign level or at the adset level, not both.</p>
</div></div><p></p></td></tr><tr class="row_11 _5m29"><td><div class="_yc"><span><code>name</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>Name for this campaign</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Supports Emoji</span></div></td></tr><tr class="row_12"><td><div class="_yc"><span><code>objective</code></span></div><div class="_yb">enum{APP_INSTALLS, BRAND_AWARENESS, CONVERSIONS, EVENT_RESPONSES, LEAD_GENERATION, LINK_CLICKS, LOCAL_AWARENESS, MESSAGES, OFFER_CLAIMS, OUTCOME_APP_PROMOTION, OUTCOME_AWARENESS, OUTCOME_ENGAGEMENT, OUTCOME_LEADS, OUTCOME_SALES, OUTCOME_TRAFFIC, PAGE_LIKES, POST_ENGAGEMENT, PRODUCT_CATALOG_SALES, REACH, STORE_VISITS, VIDEO_VIEWS}</div></td><td><p class="_yd"></p><div><div><p>Campaign's objective. If it is specified the API will validate that any ads created under the campaign match that objective. <br>Currently, with <code>BRAND_AWARENESS</code> objective, all creatives should be either only images or only videos, not mixed.
<br>
See <a href="/docs/marketing-api/reference/ad-campaign-group/#odax">Outcome Ad-Driven Experience Objective Validation</a> for more information.</p>
</div></div><p></p></td></tr><tr class="row_13 _5m29 _5m27"><td><div class="_yc"><span><code>promoted_object</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div><p>The object this campaign is promoting across all its ads. It’s required for Meta iOS 14+ app promotion (SKAdNetwork or Aggregated Event Measurement) campaign creation. Only <code>product_catalog_id</code> is used at the ad set level.</p>
</div></div><p></p></td></tr><tr class="row_13-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>application_id</code></span></div><div class="_yb">int</div></td><td><p class="_yd"></p><div><div><p>The ID of a Facebook Application. Usually related to mobile or canvas games being promoted on Facebook for installs or engagement</p>
</div></div><p></p></td></tr><tr class="row_13-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>pixel_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of a Facebook conversion pixel.  Used with offsite conversion campaigns.</p>
</div></div><p></p></td></tr><tr class="row_13-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>custom_event_type</code></span></div><div class="_yb">enum{AD_IMPRESSION, RATE, TUTORIAL_COMPLETION, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, ADD_TO_CART, ADD_TO_WISHLIST, INITIATED_CHECKOUT, ADD_PAYMENT_INFO, PURCHASE, LEAD, COMPLETE_REGISTRATION, CONTENT_VIEW, SEARCH, SERVICE_BOOKING_REQUEST, MESSAGING_CONVERSATION_STARTED_7D, LEVEL_ACHIEVED, ACHIEVEMENT_UNLOCKED, SPENT_CREDITS, LISTING_INTERACTION, D2_RETENTION, D7_RETENTION, OTHER}</div></td><td><p class="_yd"></p><div><div><p>The event from an App Event of a mobile app,
    not in the standard event list.</p>
</div></div><p></p></td></tr><tr class="row_13-3 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>object_store_url</code></span></div><div class="_yb">URL</div></td><td><p class="_yd"></p><div><div><p>The uri of the mobile / digital store where an application can be bought / downloaded. This is platform specific. When combined with the "application_id" this uniquely specifies an object which can be the subject of a Facebook advertising campaign.</p>
</div></div><p></p></td></tr><tr class="row_13-4 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>offer_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of an Offer from a Facebook Page.</p>
</div></div><p></p></td></tr><tr class="row_13-5 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>page_id</code></span></div><div class="_yb">Page ID</div></td><td><p class="_yd"></p><div><div><p>The ID of a Facebook Page</p>
</div></div><p></p></td></tr><tr class="row_13-6 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>product_catalog_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of a Product Catalog. Used with
      <a href="/docs/marketing-api/dynamic-product-ads">Dynamic Product Ads</a>.</p>
</div></div><p></p></td></tr><tr class="row_13-7 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>product_item_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of the product item.</p>
</div></div><p></p></td></tr><tr class="row_13-8 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>instagram_profile_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of the instagram profile id.</p>
</div></div><p></p></td></tr><tr class="row_13-9 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>product_set_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of a Product Set within an Ad Set level Product
      Catalog. Used with
      <a href="/docs/marketing-api/dynamic-product-ads">Dynamic Product Ads</a>.</p>
</div></div><p></p></td></tr><tr class="row_13-10 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>event_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of a Facebook Event</p>
</div></div><p></p></td></tr><tr class="row_13-11 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>offline_conversion_data_set_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of the offline dataset.</p>
</div></div><p></p></td></tr><tr class="row_13-12 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>fundraiser_campaign_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of the fundraiser campaign.</p>
</div></div><p></p></td></tr><tr class="row_13-13 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>custom_event_str</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>The event from an App Event of a mobile app,
    not in the standard event list.</p>
</div></div><p></p></td></tr><tr class="row_13-14 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>mcme_conversion_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of a MCME conversion.</p>
</div></div><p></p></td></tr><tr class="row_13-15 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>conversion_goal_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of a Conversion Goal.</p>
</div></div><p></p></td></tr><tr class="row_13-16 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>offsite_conversion_event_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of a Offsite Conversion Event</p>
</div></div><p></p></td></tr><tr class="row_13-17 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>boosted_product_set_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>The ID of the Boosted Product Set within an Ad Set level Product
      Catalog. Should only be present when the advertiser has
      opted into Product Set Boosting.</p>
</div></div><p></p></td></tr><tr class="row_13-18 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>lead_ads_form_event_source_type</code></span></div><div class="_yb">enum{inferred, offsite_crm, offsite_web, onsite_crm, onsite_crm_single_event, onsite_web, onsite_p2b_call, onsite_messaging}</div></td><td><p class="_yd"></p><div><div><p>The event source of lead ads form.</p>
</div></div><p></p></td></tr><tr class="row_13-19 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>lead_ads_custom_event_type</code></span></div><div class="_yb">enum{AD_IMPRESSION, RATE, TUTORIAL_COMPLETION, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, ADD_TO_CART, ADD_TO_WISHLIST, INITIATED_CHECKOUT, ADD_PAYMENT_INFO, PURCHASE, LEAD, COMPLETE_REGISTRATION, CONTENT_VIEW, SEARCH, SERVICE_BOOKING_REQUEST, MESSAGING_CONVERSATION_STARTED_7D, LEVEL_ACHIEVED, ACHIEVEMENT_UNLOCKED, SPENT_CREDITS, LISTING_INTERACTION, D2_RETENTION, D7_RETENTION, OTHER}</div></td><td><p class="_yd"></p><div><div><p>The event from an App Event of a mobile app,
    not in the standard event list.</p>
</div></div><p></p></td></tr><tr class="row_13-20 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>lead_ads_custom_event_str</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div><p>The event from an App Event of a mobile app,
    not in the standard event list.</p>
</div></div><p></p></td></tr><tr class="row_13-21 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>lead_ads_offsite_conversion_type</code></span></div><div class="_yb">enum{default, clo}</div></td><td><p class="_yd"></p><div><div><p>The offsite conversion type for lead ads</p>
</div></div><p></p></td></tr><tr class="row_13-22 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>value_semantic_type</code></span></div><div class="_yb">enum {VALUE, MARGIN, LIFETIME_VALUE}</div></td><td><p class="_yd"></p><div><div><p>The semantic of the event value to be using for optimization</p>
</div></div><p></p></td></tr><tr class="row_13-23 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>variation</code></span></div><div class="_yb">enum {OMNI_CHANNEL_SHOP_AUTOMATIC_DATA_COLLECTION, PRODUCT_SET_AND_APP, PRODUCT_SET_AND_IN_STORE, PRODUCT_SET_AND_OMNICHANNEL, PRODUCT_SET_AND_PHONE_CALL, PRODUCT_SET_AND_WEBSITE, PRODUCT_SET_WEBSITE_APP_AND_INSTORE}</div></td><td><p class="_yd"></p><div><div><p>Variation of the promoted object for a PCA ad</p>
</div></div><p></p></td></tr><tr class="row_13-24 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>product_set_optimization</code></span></div><div class="_yb">enum{enabled, disabled}</div></td><td><p class="_yd"></p><div><div><p>Enum defining whether or not the ad should be optimized for the promoted product set</p>
</div></div><p></p></td></tr><tr class="row_13-25 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>full_funnel_objective</code></span></div><div class="_yb">enum{6, 8, 12, 14, 15, 19, 24, 26, 29, 31, 32, 35, 36, 37, 39, 41, 42, 40, 43, 44, 46}</div></td><td><p class="_yd"></p><div><div><p>Enum defining the full funnel objective of the campaign</p>
</div></div><p></p></td></tr><tr class="row_13-26 _5m29 hidden_elem _5m27"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>omnichannel_object</code></span></div><div class="_yb">Object</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_13-26-0 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>app</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_13-26-1 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>pixel</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_13-26-2 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel2"><div class="_yc"><span><code>onsite</code></span></div><div class="_yb">array&lt;JSON object&gt;</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_13-27 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>whats_app_business_phone_number_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_13-28 _5m29 hidden_elem"><td class="devsiteReferenceTableRowLevel1"><div class="_yc"><span><code>whatsapp_phone_number</code></span></div><div class="_yb">string</div></td><td><p class="_yd"></p><div><div></div></div><p></p></td></tr><tr class="row_14"><td><div class="_yc"><span><code>source_campaign_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Used if a campaign has been copied. The ID from the original campaign that was copied.</p>
</div></div><p></p></td></tr><tr class="row_15 _5m29"><td><div class="_yc"><span><code>special_ad_categories</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_y_hi"><i class="img sp_WbXBGqjC54o sx_ba4112"></i></a></div><div class="_yb">array&lt;enum {NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES}&gt;</div></td><td><p class="_yd"></p><div><div><p>special_ad_categories</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_16"><td><div class="_yc"><span><code>special_ad_category_country</code></span><a class="_2pir" href="#" role="button" data-hover="tooltip" id="u_0_z_wq"><i class="img sp_WbXBGqjC54o sx_ba4112"></i></a></div><div class="_yb">array&lt;enum {AD, AE, AF, AG, AI, AL, AM, AN, AO, AQ, AR, AS, AT, AU, AW, AX, AZ, BA, BB, BD, BE, BF, BG, BH, BI, BJ, BL, BM, BN, BO, BQ, BR, BS, BT, BV, BW, BY, BZ, CA, CC, CD, CF, CG, CH, CI, CK, CL, CM, CN, CO, CR, CU, CV, CW, CX, CY, CZ, DE, DJ, DK, DM, DO, DZ, EC, EE, EG, EH, ER, ES, ET, FI, FJ, FK, FM, FO, FR, GA, GB, GD, GE, GF, GG, GH, GI, GL, GM, GN, GP, GQ, GR, GS, GT, GU, GW, GY, HK, HM, HN, HR, HT, HU, ID, IE, IL, IM, IN, IO, IQ, IR, IS, IT, JE, JM, JO, JP, KE, KG, KH, KI, KM, KN, KP, KR, KW, KY, KZ, LA, LB, LC, LI, LK, LR, LS, LT, LU, LV, LY, MA, MC, MD, ME, MF, MG, MH, MK, ML, MM, MN, MO, MP, MQ, MR, MS, MT, MU, MV, MW, MX, MY, MZ, NA, NC, NE, NF, NG, NI, NL, NO, NP, NR, NU, NZ, OM, PA, PE, PF, PG, PH, PK, PL, PM, PN, PR, PS, PT, PW, PY, QA, RE, RO, RS, RU, RW, SA, SB, SC, SD, SE, SG, SH, SI, SJ, SK, SL, SM, SN, SO, SR, SS, ST, SV, SX, SY, SZ, TC, TD, TF, TG, TH, TJ, TK, TL, TM, TN, TO, TR, TT, TV, TW, TZ, UA, UG, UM, US, UY, UZ, VA, VC, VE, VG, VI, VN, VU, WF, WS, XK, YE, YT, ZA, ZM, ZW}&gt;</div></td><td><p class="_yd"></p><div><div><p>special_ad_category_country</p>
</div></div><p></p></td></tr><tr class="row_17 _5m29"><td><div class="_yc"><span><code>spend_cap</code></span></div><div class="_yb">int64</div></td><td><p class="_yd"></p><div><div><p>A spend cap for the campaign, such that it will not spend more than this cap. Defined as integer value of subunit in your currency with a minimum value of $100 USD (or approximate local equivalent). Set the value to 922337203685478 to remove the spend cap. Not available for Reach and Frequency or Premium Self Serve campaigns</p>
</div></div><p></p></td></tr><tr class="row_18"><td><div class="_yc"><span><code>start_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>start_time</p>
</div></div><p></p></td></tr><tr class="row_19 _5m29"><td><div class="_yc"><span><code>status</code></span></div><div class="_yb">enum{ACTIVE, PAUSED, DELETED, ARCHIVED}</div></td><td><p class="_yd"></p><div><div><p>Only <code>ACTIVE</code> and <code>PAUSED</code> are valid during
        creation. Other statuses can be used for update. If it is set to
        <code>PAUSED</code>, its active child objects will be paused and have an effective
        status <code>CAMPAIGN_PAUSED</code>.</p>
</div></div><p></p></td></tr><tr class="row_20"><td><div class="_yc"><span><code>stop_time</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>stop_time</p>
</div></div><p></p></td></tr><tr class="row_21 _5m29"><td><div class="_yc"><span><code>topline_id</code></span></div><div class="_yb">numeric string or integer</div></td><td><p class="_yd"></p><div><div><p>Topline ID</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type">Return Type</h3><div>This endpoint supports <a href="/docs/graph-api/advanced/#read-after-write">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class="_367u"> Struct  {<div class="_uoj"><code>id</code>: numeric string, </div><div class="_uoj"><code>success</code>: bool, </div>}</div><h3 id="error-codes-2">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr></tbody></table></div></div></div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Deleting">Deleting</h2><div class="_844_"><div class="_3-98">You can dissociate a&nbsp;<a href="/docs/marketing-api/reference/ad-campaign-group/">Campaign</a> from an&nbsp;<a href="/docs/marketing-api/reference/ad-account/">AdAccount</a> by making a DELETE request to <a href="/docs/marketing-api/reference/ad-account/campaigns/"><code>/act_{ad_account_id}/campaigns</code></a>.<div><h3 id="parameters-3">Parameters</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class="_5m37" id="u_0_10_q9"><tr class="row_0"><td><div class="_yc"><span><code>before_date</code></span></div><div class="_yb">datetime</div></td><td><p class="_yd"></p><div><div><p>Set a before date to delete campaigns before this date</p>
</div></div><p></p></td></tr><tr class="row_1 _5m29"><td><div class="_yc"><span><code>delete_strategy</code></span></div><div class="_yb">enum{DELETE_ANY, DELETE_OLDEST, DELETE_ARCHIVED_BEFORE}</div></td><td><p class="_yd"></p><div><div><p>Delete strategy</p>
</div></div><p></p><div class="_3-8w"><span class="_1vet">Required</span></div></td></tr><tr class="row_2"><td><div class="_yc"><span><code>object_count</code></span></div><div class="_yb">integer</div></td><td><p class="_yd"></p><div><div><p>Object count</p>
</div></div><p></p></td></tr></tbody></table></div></div><h3 id="return-type-2">Return Type</h3><div class="_367u"> Struct  {<div class="_uoj"><code>objects_left_to_delete_count</code>: unsigned int32, </div><div class="_uoj"><code>deleted_object_ids</code>:  List  [<div class="_uoj">numeric string</div>], </div>}</div><h3 id="error-codes-3">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div></div><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '675141479195042');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '574561515946252');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '1754628768090156');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '217404712025032');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1" /></noscript></div></div></div>