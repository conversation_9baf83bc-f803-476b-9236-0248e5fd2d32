{"title": "Facebook Marketing API - Ad Account User Reference", "summary": "Reference documentation for the Ad Account User endpoint in Facebook's Marketing API v23.0. This endpoint provides read-only access to App Scoped User data associated with ad accounts, including user ID, name, and assigned tasks.", "content": "# Ad Account User\n\n## Overview\n\nThe Ad Account User endpoint provides access to data of App Scoped Users associated with ad accounts in the Facebook Marketing API.\n\n**API Version:** v23.0\n\n## Reading\n\nRetrieve data of App Scoped User associated with an ad account.\n\n### HTTP Request\n\n```http\nGET /v23.0/act_{ad-account-id}?fields={fieldname_of_type_AdAccountUser} HTTP/1.1\nHost: graph.facebook.com\n```\n\n### Parameters\n\nThis endpoint doesn't have any parameters.\n\n### Response Fields\n\n| Field | Type | Description | Default |\n|-------|------|-------------|----------|\n| `id` | numeric string | ID of the App Scoped User | |\n| `name` | string | User public full name | ✓ |\n| `tasks` | list<string> | Tasks of App Scoped User | ✓ |\n\n*Fields marked with ✓ are returned by default*\n\n## Limitations\n\n- **Creating**: Not supported on this endpoint\n- **Updating**: Not supported on this endpoint  \n- **Deleting**: Not supported on this endpoint\n\nThis is a read-only endpoint that only supports GET operations.", "keyPoints": ["Read-only endpoint for accessing App Scoped User data in ad accounts", "Returns user ID, name, and assigned tasks by default", "No parameters required for basic requests", "Only supports GET operations - no create, update, or delete functionality", "Part of Facebook Marketing API v23.0"], "apiEndpoints": ["GET /v23.0/act_{ad-account-id}?fields={fieldname_of_type_AdAccountUser}"], "parameters": ["ad-account-id (required in URL path)", "fields (optional query parameter for field selection)"], "examples": ["GET /v23.0/act_{ad-account-id}?fields={fieldname_of_type_AdAccountUser}"], "tags": ["Facebook Marketing API", "Ad Account", "User Management", "App Scoped User", "Read-only API", "Graph API"], "relatedTopics": ["Graph API", "App Scoped Users", "Ad Account Management", "Facebook Marketing API", "User Permissions", "API Field Selection"], "difficulty": "beginner", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account-user", "processedAt": "2025-06-25T15:50:37.929Z", "processor": "openrouter-claude-sonnet-4"}