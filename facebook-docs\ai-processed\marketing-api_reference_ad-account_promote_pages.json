{"title": "Facebook Marketing API - Ad Account Promote Pages Reference", "summary": "Reference documentation for the Ad Account Promote Pages endpoint in Facebook's Marketing API. This endpoint allows reading promoted Pages for an Ad Account but does not support create, update, or delete operations.", "content": "# Ad Account Promote Pages\n\n## Overview\n\nThe Ad Account Promote Pages endpoint allows you to retrieve all promoted Pages associated with a specific Ad Account using the Facebook Marketing API.\n\n## Reading\n\nGet all the promoted Pages for an Ad Account.\n\n### Requirements\n\nTo use this endpoint, you need the following permissions:\n\n- `pages_show_list` - Required to get access to your app user's Pages\n- `pages_manage_ads` - Required to create and manage ads for your app user's Pages\n\n### HTTP Request\n\n```http\nGET /v23.0/{ad-account-id}/promote_pages HTTP/1.1\nHost: graph.facebook.com\n```\n\n### Parameters\n\nThis endpoint doesn't require any parameters.\n\n### Response Format\n\nReading from this edge returns a JSON formatted result:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Response Fields\n\n- **`data`**: A list of [Page](/docs/graph-api/reference/page/) nodes\n- **`paging`**: Pagination information (see [Graph API guide](/docs/graph-api/using-graph-api/#paging) for details)\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n| 80004 | Too many calls to this ad-account. Wait and try again. See [rate limiting documentation](https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management) |\n| 190 | Invalid OAuth 2.0 Access Token |\n\n## Unsupported Operations\n\nThe following operations are **not supported** on this endpoint:\n- Creating\n- Updating  \n- Deleting\n\n## Additional Resources\n\n- [Using Graph API guide](/docs/graph-api/using-graph-api/)\n- [Graph API Explorer](/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Fpromote_pages&version=v23.0)", "keyPoints": ["Retrieves all promoted <PERSON><PERSON> for a specific Ad Account", "Requires pages_show_list and pages_manage_ads permissions", "Returns a list of Page nodes with pagination support", "Only supports read operations - create, update, and delete are not available", "Subject to rate limiting with specific error codes for common issues"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/promote_pages"], "parameters": ["ad-account-id (path parameter)"], "examples": ["GET /v23.0/{ad-account-id}/promote_pages HTTP/1.1\nHost: graph.facebook.com"], "tags": ["Facebook Marketing API", "Ad Account", "Pages", "Promote Pages", "Graph API", "Marketing", "Advertising"], "relatedTopics": ["Graph API", "Page nodes", "OAuth 2.0 Access Tokens", "Rate limiting", "Pagination", "Facebook permissions", "Ad management"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/promote_pages/", "processedAt": "2025-06-25T15:37:32.343Z", "processor": "openrouter-claude-sonnet-4"}