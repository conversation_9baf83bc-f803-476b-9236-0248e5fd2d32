# Facebook Marketing API - Brand Safety and Suitability

## Summary
Meta's Brand Safety and Suitability APIs provide comprehensive tools for advertisers to control ad placement and ensure brand-appropriate content adjacency across Facebook, Instagram, and Meta Audience Network. The suite includes multiple APIs for blocking unsuitable content, allowing trusted content, reporting delivery metrics, and verifying feed suitability.

## Key Points
- Meta provides multiple brand safety controls that can be used individually or in combination across Facebook, Instagram, and Meta Audience Network
- Block Lists API prevents ads from appearing with unsuitable publishers for your brand
- Content Allow Lists API enables collaboration with Meta Business Partners to curate brand-suitable video content
- Delivery Reports APIs provide transparency at both content and publisher levels for ad placement verification
- Feed Verification and Passback APIs enable measurement and feedback mechanisms for content suitability assessment

## API Endpoints
- `Block Lists API`
- `Content Allow Lists API`
- `Content Delivery Reports API`
- `Feed Verification API`
- `Partner-publisher Lists API`
- `Passback API`
- `Publisher Delivery Reports API`

## Content
# Brand Safety and Suitability

Meta offers several brand suitability controls to help you place ads adjacent to organic content that is more suitable for your brand on Facebook, Instagram and Meta Audience Network. You can apply one of these controls or use them in combination. Meta keeps your brand safe by enforcing [Facebook Community Standards](https://www.facebook.com/business/brand-safety/media-responsibility) and [Instagram Community Guidelines](https://www.facebook.com/help/instagram/***************) for all content and publishers. [Learn more about brand suitability](https://www.facebook.com/business/help/****************?id=****************).

## Available APIs and Features

### Integration Setup
An overview of initial setup steps required for program participation. The main elements it addresses include: setting up a business in Business Manager, creating and obtaining access to ad accounts, and creating an app to access Meta's API.

### Block Lists API
Block lists stop your ads from appearing with publishers you don't consider suitable for your brand or campaign.

### Content Allow Lists API
Content Allow Lists give you the ability to work with trusted Meta Business Partners to review and customize lists of brand suitable videos for running Facebook in-stream campaigns.

### Content Delivery Reports API
Content delivery reports provide transparency into where ads appeared and show impressions at the content level.

### Feed Verification API
Feed verification allows you to measure, verify and understand the suitability of content near your ads to help you make informed decisions in order to reach your marketing goals.

### Partner-publisher Lists API
Partner-publisher lists show publishers that have signed up for monetization and follow our Partner Monetization Policies.

### Passback API
Passback allows Meta Business Partners to share content risk labels and campaign performance data with Meta. The goals are to provide advertisers and partners with a mechanism to give feedback on content, for Meta to be able to take action on that feedback, and for Meta and partners to be able to compare content labels.

### Publisher Delivery Reports API
Publisher delivery reports provide transparency into where ads appeared and show impressions at the publisher level.

---
**Tags:** brand safety, content suitability, ad placement, publisher controls, content verification, delivery reports, Meta Audience Network
**Difficulty:** intermediate
**Content Type:** overview
**Source:** https://developers.facebook.com/docs/marketing-api/brand-safety-and-suitability
**Processed:** 2025-06-25T15:08:11.326Z