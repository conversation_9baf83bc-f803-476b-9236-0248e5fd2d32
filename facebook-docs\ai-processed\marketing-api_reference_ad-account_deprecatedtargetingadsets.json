{"title": "Facebook Marketing API - Ad Account Deprecated Targeting Ad Sets", "summary": "This endpoint allows you to retrieve ad sets from an ad account that use deprecated targeting options. It provides read-only access to identify ad sets that may need updating due to targeting deprecations or delivery pauses.", "content": "# Ad Account Deprecated Targeting Ad Sets\n\nThis endpoint provides access to ad sets within an ad account that use deprecated targeting options or have delivery paused due to targeting issues.\n\n## Reading\n\nRetrieve ad sets with deprecated targeting from a specific ad account.\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/deprecatedtargetingadsets\n```\n\n### Example Request\n\n**HTTP**\n```http\nGET /v23.0/{ad-account-id}/deprecatedtargetingadsets HTTP/1.1\nHost: graph.facebook.com\n```\n\n**PHP SDK**\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/deprecatedtargetingadsets',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n**JavaScript SDK**\n```javascript\nFB.api(\n    \"/{ad-account-id}/deprecatedtargetingadsets\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Parameters\n\n| Parameter | Type | Description | Default |\n|-----------|------|-------------|----------|\n| `type` | string | Query ad sets according to deprecation type. Valid options: `deprecating`, `delivery_paused` | `deprecating` |\n\n### Response Format\n\nThe response returns a JSON object with the following structure:\n\n```json\n{\n    \"data\": [],\n    \"paging\": {}\n}\n```\n\n#### Fields\n\n- **`data`**: A list of AdSet nodes containing the deprecated targeting ad sets\n- **`paging`**: Pagination information for navigating through results\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n\n## Limitations\n\nThis endpoint is **read-only**. The following operations are not supported:\n- Creating new entries\n- Updating existing entries  \n- Deleting entries\n\n## Usage Notes\n\n- Use this endpoint to identify ad sets that may need attention due to deprecated targeting options\n- The `type` parameter allows filtering between ad sets that are deprecating vs. those with delivery paused\n- Results are paginated - use the paging object to navigate through large result sets", "keyPoints": ["Read-only endpoint for retrieving ad sets with deprecated targeting options", "Supports filtering by deprecation type (deprecating or delivery_paused)", "Returns paginated results with AdSet nodes", "No create, update, or delete operations are supported", "Useful for identifying ad sets that need targeting updates"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/deprecatedtargetingadsets"], "parameters": ["type (string): deprecating or delivery_paused", "ad-account-id (path parameter): Target ad account ID"], "examples": ["HTTP GET request to retrieve deprecated targeting ad sets", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest example", "iOS SDK FBSDKGraphRequest example"], "tags": ["Facebook Marketing API", "Ad Account", "Ad Sets", "Deprecated Targeting", "Graph API", "Read-only endpoint"], "relatedTopics": ["AdSet reference documentation", "Graph API pagination", "Facebook Marketing API permissions", "Targeting deprecation policies", "Ad account management"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "processedAt": "2025-06-25T16:27:48.079Z", "processor": "openrouter-claude-sonnet-4"}