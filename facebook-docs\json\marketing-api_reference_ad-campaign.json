{"title": "Ad Set", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_ba\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_kr\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_Uh\"></div></span></div></div>\n\n<h1 id=\"overview\">Ad Set</h1>\n\n<p>An ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data. Ad sets enable you to group ads according to your criteria, and you can retrieve the ad-related statistics that apply to a set. See <a href=\"/docs/marketing-api/optimizedcpm\">Optimized CPM</a> and <a href=\"/docs/marketing-api/reference/ad-campaign/promoted-object\">Promoted Object</a>.</p><p>For example, create an ad set with a daily budget:</p><p></p><div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_5_DC\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_6_di\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_nD\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_IR\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_KS\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_jp\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_b_MR\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_c_Cy\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_d_yC\"><span label=\"Copy Code\" value=\"curl -X POST \\\n  -F 'name=&quot;My Reach Ad Set&quot;' \\\n  -F 'optimization_goal=&quot;REACH&quot;' \\\n  -F 'billing_event=&quot;IMPRESSIONS&quot;' \\\n  -F 'bid_amount=2' \\\n  -F 'daily_budget=1000' \\\n  -F 'campaign_id=&quot;<AD_CAMPAIGN_ID>&quot;' \\\n  -F 'targeting={\n       &quot;geo_locations&quot;: {\n         &quot;countries&quot;: [\n           &quot;US&quot;\n         ]\n       },\n       &quot;facebook_positions&quot;: [\n         &quot;feed&quot;\n       ]\n     }' \\\n  -F 'status=&quot;PAUSED&quot;' \\\n  -F 'promoted_object={\n       &quot;page_id&quot;: &quot;<PAGE_ID>&quot;\n     }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"My Reach Ad Set\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'optimization_goal=\"REACH\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'billing_event=\"IMPRESSIONS\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'bid_amount=2'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'daily_budget=1000'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'campaign_id=\"&lt;AD_CAMPAIGN_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'targeting={\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ]\n       },\n       \"facebook_positions\": [\n         \"feed\"\n       ]\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=\"PAUSED\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'promoted_object={\n       \"page_id\": \"&lt;PAGE_ID&gt;\"\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;/</span><span class=\"pln\">adsets</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets?&amp;version=v23.0&amp;name=My+Reach+Ad+Set&amp;optimization_goal=REACH&amp;billing_event=IMPRESSIONS&amp;bid_amount=2&amp;daily_budget=1000&amp;campaign_id=%3CAD_CAMPAIGN_ID%3E&amp;targeting=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22facebook_positions%22%3A%5B%22feed%22%5D%7D&amp;status=PAUSED&amp;promoted_object=%7B%22page_id%22%3A%22%3CPAGE_ID%3E%22%7D\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _9c6\" href=\"https://l.facebook.com/l.php?u=https%3A%2F%2Fwww.postman.com%2Fmeta%2Ffacebook-marketing-api%2Ffolder%2F8123rwa%2Fadsets&amp;h=AT1u0v1JXIVKvLSb1_hgpI3ZJfUrohtXAkG6ArpJiht0FLRDtBSbVh8fifgYodJacRXB77hOZR0sNDnEedsWGiXUS14tUZ0zXSWMexduy5Ctf5TxRTH95g52Y-ipI0dVuzajFcN-PL_GrRUFTfO97Q\" target=\"_blank\" style=\"font-family: Arial, sans-serif; background-color: #EF5B25\" rel=\"nofollow\" data-lynx-mode=\"asynclazy\">Open In Postman</a></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_t_3/\"><input type=\"hidden\" name=\"jazoest\" value=\"2911\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZHd8\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/reference/ad-campaign\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><p></p><p>Create an ad set with a lifetime budget</p><p></p><div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_u_Lz\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_v_nB\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_w_UI\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_x_pL\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_y_nx\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_z_Zf\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_10_Ps\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_11_O+\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_12_i9\"><span label=\"Copy Code\" value=\"curl -X POST \\\n  -F 'name=&quot;My First Adset&quot;' \\\n  -F 'lifetime_budget=20000' \\\n  -F 'start_time=&quot;2025-06-25T08:51:33-0700&quot;' \\\n  -F 'end_time=&quot;2025-07-05T08:51:33-0700&quot;' \\\n  -F 'campaign_id=&quot;<AD_CAMPAIGN_ID>&quot;' \\\n  -F 'bid_amount=100' \\\n  -F 'billing_event=&quot;LINK_CLICKS&quot;' \\\n  -F 'optimization_goal=&quot;LINK_CLICKS&quot;' \\\n  -F 'targeting={\n       &quot;facebook_positions&quot;: [\n         &quot;feed&quot;\n       ],\n       &quot;geo_locations&quot;: {\n         &quot;countries&quot;: [\n           &quot;US&quot;\n         ]\n       },\n       &quot;publisher_platforms&quot;: [\n         &quot;facebook&quot;,\n         &quot;audience_network&quot;\n       ]\n     }' \\\n  -F 'status=&quot;PAUSED&quot;' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adsets\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=\"My First Adset\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'lifetime_budget=20000'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'start_time=\"2025-06-25T08:51:33-0700\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'end_time=\"2025-07-05T08:51:33-0700\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'campaign_id=\"&lt;AD_CAMPAIGN_ID&gt;\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'bid_amount=100'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'billing_event=\"LINK_CLICKS\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'optimization_goal=\"LINK_CLICKS\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'targeting={\n       \"facebook_positions\": [\n         \"feed\"\n       ],\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ]\n       },\n       \"publisher_platforms\": [\n         \"facebook\",\n         \"audience_network\"\n       ]\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'status=\"PAUSED\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;/</span><span class=\"pln\">adsets</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets?&amp;version=v23.0&amp;name=My+First+Adset&amp;lifetime_budget=20000&amp;start_time=2025-06-25T08%3A51%3A33-0700&amp;end_time=2025-07-05T08%3A51%3A33-0700&amp;campaign_id=%3CAD_CAMPAIGN_ID%3E&amp;bid_amount=100&amp;billing_event=LINK_CLICKS&amp;optimization_goal=LINK_CLICKS&amp;targeting=%7B%22facebook_positions%22%3A%5B%22feed%22%5D%2C%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22publisher_platforms%22%3A%5B%22facebook%22%2C%22audience_network%22%5D%7D&amp;status=PAUSED\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_1i_Qd\"><input type=\"hidden\" name=\"jazoest\" value=\"2911\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZHd8\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/reference/ad-campaign\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><p></p><h3 id=\"limits\">Limits</h3><p>The following are the limits on ad sets</p><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nLimit\n</th><th>\nValue\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_1j_QD\"><tr class=\"row_0\"><td><p>Maximum number of ad sets per regular ad account</p>\n</td><td><p>5000 non-deleted ad sets</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p>Maximum number of ad sets per bulk ad account</p>\n</td><td><p>10000 non-deleted ad sets</p>\n</td></tr><tr class=\"row_2\"><td><p>Maximum number of ads per ad set</p>\n</td><td><p>50 non-archived ads</p>\n</td></tr></tbody></table></div><h3>Housing, Employment and Credit Ads</h3><p><span></span></p><p>Facebook is committed to protecting people from discrimination, and we are continually improving our ability to detect and deter potential abuse. It’s already against <a href=\"https://www.facebook.com/policies/ads/prohibited_content/discriminatory_practices\">our policies</a> to discriminate by wrongfully targeting or excluding specific groups of people. As part of a <a href=\"https://l.facebook.com/l.php?u=https%3A%2F%2Fnewsroom.fb.com%2Fnews%2F2019%2F03%2Fprotecting-against-discrimination-in-ads%2F&amp;h=AT1F5eXYq9Y6N7zoiY2_aefjRxpzTHd0BBssqCaO0nCY9jSxqI1FEngsk-uNTo40BM_9-7m6VZRwaBhYmis9DcqdWZ7CcsN64OWVHLZN3XgPr6vlgeUKYQzB4C6R_EKX-oEZmR6BNWnZD2WIh8N4kw\" target=\"_blank\" rel=\"noreferrer nofollow\" data-lynx-mode=\"asynclazy\">historic settlement agreement</a>, we are making changes to the way we manage housing, employment and credit ads.</p>\n<p></p><p><span></span></p><p>Advertisers must specify a <code>special_ad_category</code> for ad campaigns that market housing, employment, and credit. In doing so, the set of targeting options available for ads in these campaigns will be restricted. See <a href=\"/docs/marketing-api/special-ad-category\">Special Ad Category</a> for more information.</p>\n<p></p><h3>Targeting European Union Ads</h3><p><span></span></p><p>Beginning Tuesday, May 16, 2023 advertisers who include the European Union (EU), associated territories, or select global/worldwide in their ad targeting on Facebook and Instagram will be asked to include information about who benefits from the ad (the beneficiary) and who is paying for the ad (the payor) for each ad set. Advertisers will be prompted for this information in all ads buying surfaces including Ads Manager and the Marketing API. Beginning Wednesday, August 16, 2023, if beneficiary and payer information is not provided, the ad will not be published.</p>\n<p></p><p><span></span></p><p>We are launching this requirement to respond to the EU Digital Services Act (DSA) which goes into full effect for Facebook and Instagram later this year.</p>\n<p></p><p><span></span></p><p>Ad sets targeted to the EU and/or associated territories (see <a href=\"https://www.facebook.com/business/help/***************/\">here</a> for a complete list) are required to provide beneficiary information (who benefits from the ad running), and payer information (who pays for the ad). This applies to new ads, duplicated ads, or significantly edited ads from May 16 forward, and without the required information, the API will respond with a wrong parameter error. For convenience the advertiser can set a saved beneficiary and payor in their ad account, which will be auto-populated during ad set creation, copying, and updating targets to include EU locations and ads under existing ad seta without configured the payor and beneficiary.. For more information about the ad account level parameters, <code>default_dsa_payor</code> and <code>default_dsa_beneficiary</code>, see to the check the <a href=\"/docs/marketing-api/reference/ad-account\">Ad Account reference document</a>.</p>\n<p></p><p><span></span></p><p>To facilitate the creation of ad sets targeting the EU, we're offering a new API which allows developers to get a list of likely beneficiary/payer strings, based on ad account activity. See <a href=\"/docs/marketing-api/reference/ad-account/dsa_recommendations\">Ad Account DSA Recommendations</a> for more information.</p>\n<p></p><p><span></span></p><p><strong>Notice:</strong></p>\n\n<ul>\n<li>When the default values are set in the ad account, during ad set creation, updating, and ad creation under an existing ad set, if one of them is not provided, the API will automatically fill the default value listed in the ad account. <strong>Do not pass only one of them and expect the API to set the other one to be the same value.</strong> For example, in the ad account settings, <code>default_dsa_payor</code> is <code>payor_default</code> and <code>default_dsa_beneficiary</code> is <code>beneficiary_default</code>. During ad set creation, if only <code>dsa_payor</code> is passed with the payor, the <code>dsa_beneficiary</code> will be automatically filled with value of <code>beneficiary_default</code> instead of <code>dsa_payor</code>.</li>\n<li>If no saved default values are set or the values are unset, without explicitly passing the payor or beneficiary during ad set creation or when making updates, it will trigger an error and the request will fail.</li>\n<li>The <code>payer</code> and the <code>beneficiary</code> fields are only for ad sets targeting the EU and/or associated territories. </li>\n<li>For ad sets targeting regions other than the EU and/or associated territories, that information will not be saved even if it is provided.</li>\n</ul>\n\n<p>To facilitate the creation of ad sets targeting the EU, we're offering a new API which allows developers to get a list of likely beneficiary/payer strings, based on ad account activity. See <a href=\"/docs/marketing-api/reference/ad-account/dsa_recommendations\">Ad Account Dsa Recommendations</a> for more information.</p>\n<p></p>\n\n<h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>An ad set is a group of ads that share the same daily or lifetime budget, schedule, bid type, bid info, and targeting data. Ad sets enable you to group ads according to your criteria, and you can retrieve the ad-related statistics that apply to a set.</p>\n</div><div><div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>The <code>date_preset = lifetime</code> parameter is disabled in Graph API v10.0 and replaced with <code>date_preset = maximum</code>, which returns a maximum of 37 months of data. For v9.0 and below, <code>date_preset = maximum</code> will be enabled on May 25, 2021, and any <code>lifetime</code> calls will default to <code>maximum</code> and return only 37 months of data.</p>\n</div></div></div></div></div><h3 id=\"read-examples\">Examples</h3><p></p><div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_1k_B+\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1l_eV\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1m_xL\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1n_py\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1o_bc\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_1p_w9\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_1q_Hi\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_1r_xs\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_1s_QT\"><span label=\"Copy Code\" value=\"curl -X GET \\\n  -d 'fields=&quot;name,status&quot;' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=\"name,status\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">AD_SET_ID</span><span class=\"pun\">&gt;/</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=%3CAD_SET_ID%3E%2F?fields=name%2Cstatus&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_28_0Y\"><input type=\"hidden\" name=\"jazoest\" value=\"2911\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZHd8\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/reference/ad-campaign\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><p></p><p>To retrieve date-time related fields in a UNIX timestamp format, use the <code>date_format</code> parameter:</p><p></p><div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_29_Zp\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2a_77\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2b_1E\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2c_xx\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2d_iJ\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_2e_/1\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_2f_33\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_2g_2r\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_2h_Ar\"><span label=\"Copy Code\" value=\"curl -X GET \\\n  -d 'fields=&quot;id,name,start_time,end_time&quot;' \\\n  -d 'date_format=&quot;U&quot;' \\\n  -d 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X GET \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'fields=\"id,name,start_time,end_time\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'date_format=\"U\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">AD_SET_ID</span><span class=\"pun\">&gt;/</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=GET&amp;path=%3CAD_SET_ID%3E%2F?fields=id%2Cname%2Cstart_time%2Cend_time%26date_format=U&amp;version=v23.0\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_2x_qJ\"><input type=\"hidden\" name=\"jazoest\" value=\"2911\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZHd8\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/reference/ad-campaign\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><p></p><p>To retrieve information for multiple ad sets:</p><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_2y_5m\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_2z_qS\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_30_az\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_31_+G\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdSet</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdSetFields</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$ad_set_ids </span><span class=\"pun\">=</span><span class=\"pln\"> array</span><span class=\"pun\">(&lt;</span><span class=\"pln\">AD_SET_1_ID</span><span class=\"pun\">&gt;,</span><span class=\"pln\"> </span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_SET_2_ID</span><span class=\"pun\">&gt;,</span><span class=\"pln\"> </span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_SET_3_ID</span><span class=\"pun\">&gt;);</span><span class=\"pln\">\n$fields </span><span class=\"pun\">=</span><span class=\"pln\"> array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">CONFIGURED_STATUS</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">EFFECTIVE_STATUS</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">);</span><span class=\"pln\">\n$adsets </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"typ\">AdSet</span><span class=\"pun\">::</span><span class=\"pln\">readIds</span><span class=\"pun\">(</span><span class=\"pln\">$ad_set_ids</span><span class=\"pun\">,</span><span class=\"pln\"> $fields</span><span class=\"pun\">);</span><span class=\"pln\">\n\n</span><span class=\"kwd\">foreach</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">$adsets </span><span class=\"kwd\">as</span><span class=\"pln\"> $adset</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo $adset</span><span class=\"pun\">-&gt;{</span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME</span><span class=\"pun\">}.</span><span class=\"pln\">PHP_EOL</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></code></pre></div></div><p></p><p>To read all ad sets from one ad account:</p><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_33_lY\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_34_Wm\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_35_CV\">Python Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_36_Xf\">Java Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_37_d4\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_38_kS\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdAccount</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdSetFields</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$account </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$adsets </span><span class=\"pun\">=</span><span class=\"pln\"> $account</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getAdSets</span><span class=\"pun\">(</span><span class=\"pln\">array</span><span class=\"pun\">(</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">CONFIGURED_STATUS</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">EFFECTIVE_STATUS</span><span class=\"pun\">,</span><span class=\"pln\">\n</span><span class=\"pun\">));</span><span class=\"pln\">\n\n</span><span class=\"kwd\">foreach</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">$adsets </span><span class=\"kwd\">as</span><span class=\"pln\"> $adset</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo $adset</span><span class=\"pun\">-&gt;{</span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME</span><span class=\"pun\">}.</span><span class=\"pln\">PHP_EOL</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></code></pre></div></div><p></p><p>To read the names of ad sets with status paused in an ad account</p><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_3c_I0\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_3d_nL\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3e_zd\">Java Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3f_9A\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_3g_Yv\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdAccount</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdSet</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdSetFields</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$account </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdAccount</span><span class=\"pun\">(</span><span class=\"str\">'act_&lt;AD_ACCOUNT_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$adsets </span><span class=\"pun\">=</span><span class=\"pln\"> $account</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getAdSets</span><span class=\"pun\">(</span><span class=\"pln\">\n  array</span><span class=\"pun\">(),</span><span class=\"pln\">\n  array</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">EFFECTIVE_STATUS </span><span class=\"pun\">=&gt;</span><span class=\"pln\"> array</span><span class=\"pun\">(</span><span class=\"pln\">\n      </span><span class=\"typ\">AdSet</span><span class=\"pun\">::</span><span class=\"pln\">STATUS_PAUSED</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"pun\">),</span><span class=\"pln\">\n  </span><span class=\"pun\">));</span><span class=\"pln\">\n\n</span><span class=\"kwd\">foreach</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">$adsets </span><span class=\"kwd\">as</span><span class=\"pln\"> $adset</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo $adset</span><span class=\"pun\">-&gt;{</span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">NAME</span><span class=\"pun\">}.</span><span class=\"pln\">PHP_EOL</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></code></pre></div></div><p></p><p>To read the <code>end_time</code> of multiple ad sets.</p><p></p><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_3j_E+\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_3k_XY\">PHP Business SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3l_Jk\">cURL</button></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_3m_IW\" style=\"\"><code><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdSet</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"kwd\">use</span><span class=\"pln\"> </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\Fields\\AdSetFields</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$adsets </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"typ\">AdSet</span><span class=\"pun\">::</span><span class=\"pln\">readIds</span><span class=\"pun\">(</span><span class=\"pln\">\n  array</span><span class=\"pun\">(</span><span class=\"pln\">$ad_set_1_id</span><span class=\"pun\">,</span><span class=\"pln\"> $ad_set_2_id</span><span class=\"pun\">),</span><span class=\"pln\">\n  array</span><span class=\"pun\">(</span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">END_TIME</span><span class=\"pun\">));</span><span class=\"pln\">\n\n</span><span class=\"kwd\">foreach</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">$adsets </span><span class=\"kwd\">as</span><span class=\"pln\"> $adset</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo $adset</span><span class=\"pun\">-&gt;{</span><span class=\"typ\">AdSetFields</span><span class=\"pun\">::</span><span class=\"pln\">END_TIME</span><span class=\"pun\">}.</span><span class=\"pln\">PHP_EOL</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></code></pre></div></div><p></p></div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_3o_jR\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_3p_Zq\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3q_ax\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3r_ru\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3s_At\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3t_D7\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_3u_Qt\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=%3CAD_SET_ID%3E%2F%3Ffields%3Dadset_schedule&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_3v_Om\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">AD_SET_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/?fields=adset_schedule HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_41_ni\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>date_preset</code></span></div><div class=\"_yb\">enum{today, yesterday, this_month, last_month, this_quarter, maximum, data_maximum, last_3d, last_7d, last_14d, last_28d, last_30d, last_90d, last_week_mon_sun, last_week_sun_sat, last_quarter, last_year, this_week_mon_today, this_week_sun_today, this_year}</div></td><td><p class=\"_yd\"></p><div><div><p>Date Preset</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>time_range</code></span></div><div class=\"_yb\">{'since':YYYY-MM-DD,'until':YYYY-MM-DD}</div></td><td><p class=\"_yd\"></p><div><div><p>Time Range. Note if time range is invalid, it will be ignored.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID for the Ad Set</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>account_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID for the Ad Account associated with this Ad Set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>adlabels</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-label/\">list&lt;AdLabel&gt;</a></div></td><td><p class=\"_yd\"></p><div><div><p>Ad Labels associated with this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>adset_schedule</code></span></div><div class=\"_yb _yc\"><span>list&lt;DayPart&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Ad set schedule, representing a delivery schedule for a single day</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>asset_feed_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the asset feed that constains a content to create ads</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>attribution_spec</code></span></div><div class=\"_yb _yc\"><span>list&lt;AttributionSpec&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Conversion attribution spec used for attributing conversions for optimization. Supported window lengths differ by optimization goal and campaign objective. See <a href=\"/docs/marketing-api/reference/ad-campaign-group#attribution_spec\">Objective, Optimization Goal and <code>attribution_spec</code></a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>bid_adjustments</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-bid-adjustments/\">AdBidAdjustments</a></div></td><td><p class=\"_yd\"></p><div><div><p>Map of bid adjustment types to values</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>bid_amount</code></span></div><div class=\"_yb _yc\"><span>unsigned int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>Bid cap or target cost for this ad set. The bid cap used in a <em>lowest cost bid strategy</em> is defined as the maximum bid you want to pay for a result based on your <code>optimization_goal</code>. The target cost used in a <em>target cost bid strategy</em> lets Facebook bid on your behalf to meet your target on average and keep costs stable as you raise budget.</p>\n\n<p>The bid amount's unit is cents for currencies like USD, EUR, and the basic unit for currencies like JPY, KRW. The bid amount for ads with <code>IMPRESSION</code> or <code>REACH</code> as <code>billing_event</code> is per 1,000 occurrences of that event, and the bid amount for ads with other <code>billing_event</code>s is for each occurrence.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>bid_constraints</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-bid-constraint/\">AdCampaignBidConstraint</a></div></td><td><p class=\"_yd\"></p><div><div><p>Choose bid constraints for ad set to suit your specific business goals. It usually works together with <code>bid_strategy</code> field.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>bid_info</code></span></div><div class=\"_yb _yc\"><span>map&lt;string, unsigned int32&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Map of bid objective to bid value.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>bid_strategy</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_42_WG\"></a></div><div class=\"_yb _yc\"><span>enum {LOWEST_COST_WITHOUT_CAP, LOWEST_COST_WITH_BID_CAP, COST_CAP, LOWEST_COST_WITH_MIN_ROAS}</span></div></td><td><p class=\"_yd\"></p><div><div><p>Bid strategy for this ad set when you use <code>AUCTION</code> as your buying type:<br>\n        <code>LOWEST_COST_WITHOUT_CAP</code>: Designed to get the most results for your budget based on\n        your ad set <code>optimization_goal</code> without limiting your bid amount. This is the best strategy\n        if you care most about cost efficiency. However with this strategy it may be harder to get\n        stable average costs as you spend. This strategy is also known as <em>automatic bidding</em>.\n        Learn more in <a href=\"https://www.facebook.com/business/help/721453268045071\">Ads Help Center, About bid strategies: Lowest cost</a>.<br>\n        <code>LOWEST_COST_WITH_BID_CAP</code>: Designed to get the most results for your budget based on\n        your ad set <code>optimization_goal</code> while limiting actual bid to your specified\n        amount. With a bid cap you have more control over your\n        cost per actual optimization event. However if you set a limit which is too low you may\n        get less ads delivery. Get your bid cap with the field <code>bid_amount</code>.\n        This strategy is also known as <em>manual maximum-cost bidding</em>.\n        Learn more in <a href=\"https://www.facebook.com/business/help/721453268045071\">Ads Help Center, About bid strategies: Lowest cost</a>.<br>\nNotes:</p>\n\n<ul>\n<li>If you enable campaign budget optimization, you should get <code>bid_strategy</code> at the parent campaign level.</li>\n<li><code>TARGET_COST</code> bidding strategy has been deprecated with <a href=\"/docs/graph-api/changelog/version9.0\">Marketing API v9</a>.</li>\n</ul>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>billing_event</code></span></div><div class=\"_yb _yc\"><span>enum {APP_INSTALLS, CLICKS, IMPRESSIONS, LINK_CLICKS, NONE, OFFER_CLAIMS, PAGE_LIKES, POST_ENGAGEMENT, THRUPLAY, PURCHASE, LISTING_INTERACTION}</span></div></td><td><p class=\"_yd\"></p><div><div><p>The billing event for this ad set:<br><code>APP_INSTALLS</code>: Pay when people install your app.<br><code>CLICKS</code>: Pay when people click anywhere in the ad. <br><code>IMPRESSIONS</code>: Pay when the ads are shown to people.<br><code>LINK_CLICKS</code>: Pay when people click on the link of the ad.<br><code>OFFER_CLAIMS</code>: Pay when people claim the offer.<br><code>PAGE_LIKES</code>: Pay when people like your page.<br><code>POST_ENGAGEMENT</code>: Pay when people engage with your post.<br><code>VIDEO_VIEWS</code>: Pay when people watch your video ads for at least 10 seconds.<br><code>THRUPLAY</code>: Pay for ads that are played to completion, or played for at least 15 seconds.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>brand_safety_config</code></span></div><div class=\"_yb _yc\"><span>BrandSafetyCampaignConfig</span></div></td><td><p class=\"_yd\"></p><div><div><p>brand_safety_config</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>budget_remaining</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Remaining budget of this Ad Set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>campaign</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/\">Campaign</a></div></td><td><p class=\"_yd\"></p><div><div><p>The campaign that contains this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>campaign_active_time</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Campaign running length</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>campaign_attribution</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>campaign_attribution, a new field for app ads campaign, used to indicate a campaign's attribution type, eg: SKAN or AEM</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>campaign_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the campaign that contains this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>configured_status</code></span></div><div class=\"_yb _yc\"><span>enum {ACTIVE, PAUSED, DELETED, ARCHIVED}</span></div></td><td><p class=\"_yd\"></p><div><div><p>The status set at the ad set level. It can be different from the\n        effective status due to its parent campaign. Prefer using 'status'\n        instead of this.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>contextual_bundling_spec</code></span></div><div class=\"_yb _yc\"><span>ContextualBundlingSpec</span></div></td><td><p class=\"_yd\"></p><div><div><p>specs of contextual bundling Ad Set setup, including signal of opt-in/out the feature</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>created_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>Time when this Ad Set was created</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>creative_sequence</code></span></div><div class=\"_yb _yc\"><span>list&lt;numeric string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Order of the adgroup sequence to be shown to users</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>daily_budget</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The daily budget of the set defined in your <a href=\"/docs/marketing-api/adset/budget-limits\">account currency</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>daily_min_spend_target</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Daily minimum spend target of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. This target is not a guarantee but our best effort.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>daily_spend_cap</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Daily spend cap of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>destination_type</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Destination of ads in this Ad Set.</p>\n\n<p>Options include: <code>WEBSITE</code>, <code>APP</code>, <code>MESSENGER</code>, <code>INSTAGRAM_DIRECT</code>.</p>\n\n<p>The <code>ON_AD</code>, <code>ON_POST</code>, <code>ON_VIDEO</code>, <code>ON_PAGE</code>, and <code>ON_EVENT</code> destination types are currently in limited beta testing. Trying to duplicate campaigns with existing destination types using these new destination types may throw an error. See the <a href=\"#odax\">Outcome-Driven Ads Experiences</a> section below for more information.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>dsa_beneficiary</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The beneficiary of all ads in this ad set.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>dsa_payor</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The payor of all ads in this ad set.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>effective_status</code></span></div><div class=\"_yb _yc\"><span>enum {ACTIVE, PAUSED, DELETED, CAMPAIGN_PAUSED, ARCHIVED, IN_PROCESS, WITH_ISSUES}</span></div></td><td><p class=\"_yd\"></p><div><div><p>The effective status of the adset. The status could be effective either\n        because of its own status, or the status of its parent campaign. <code>WITH_ISSUES</code> is available for version 3.2 or higher. <code>IN_PROCESS</code> is available for version 4.0 or higher.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>end_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>End time, in UTC UNIX timestamp</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>frequency_control_specs</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-frequency-control-specs/\">list&lt;AdCampaignFrequencyControlSpecs&gt;</a></div></td><td><p class=\"_yd\"></p><div><div><p>An array of frequency control specs for this ad set. As there is only one event type currently supported, this array has no more than one element. Writes to this field are only available in ad sets where <code>REACH</code> is the objective.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>instagram_user_id</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_43_LH\"></a></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Represents your Instagram account id, used for ads, including dynamic creative ads on Instagram.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_dynamic_creative</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_44_Dl\"></a></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether this ad set is a dynamic creative ad set. dynamic creative ad can be created only under ad set with this field set to be true.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_incremental_attribution_enabled</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Whether the campaign should use incremental attribution optimization.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>issues_info</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_45_QT\"></a></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-issues-info/\">list&lt;AdCampaignIssuesInfo&gt;</a></div></td><td><p class=\"_yd\"></p><div><div><p>Issues for this ad set that prevented it from deliverying</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>learning_stage_info</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-learning-stage-info/\">AdCampaignLearningStageInfo</a></div></td><td><p class=\"_yd\"></p><div><div><p>Info about whether the ranking or delivery system is still learning for this ad set. While the ad set is still in learning , we might unstablized delivery performances.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>lifetime_budget</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The lifetime budget of the set defined in your <a href=\"/docs/marketing-api/adset/budget-limits\">account currency</a>.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>lifetime_imps</code></span></div><div class=\"_yb _yc\"><span>int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime impressions. Available only for campaigns with <code>buying_type=FIXED_CPM</code></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>lifetime_min_spend_target</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime minimum spend target of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. This target is not a guarantee but our best effort.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>lifetime_spend_cap</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime spend cap of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>min_budget_spend_percentage</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>min_budget_spend_percentage</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>multi_optimization_goal_weight</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>multi_optimization_goal_weight</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Name of the ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>optimization_goal</code></span></div><div class=\"_yb _yc\"><span>enum {NONE, APP_INSTALLS, AD_RECALL_LIFT, ENGAGED_USERS, EVENT_RESPONSES, IMPRESSIONS, LEAD_GENERATION, QUALITY_LEAD, LINK_CLICKS, OFFSITE_CONVERSIONS, PAGE_LIKES, POST_ENGAGEMENT, QUALITY_CALL, REACH, LANDING_PAGE_VIEWS, VISIT_INSTAGRAM_PROFILE, VALUE, THRUPLAY, DERIVED_EVENTS, APP_INSTALLS_AND_OFFSITE_CONVERSIONS, CONVERSATIONS, IN_APP_VALUE, MESSAGING_PURCHASE_CONVERSION, SUBSCRIBERS, REMINDERS_SET, MEANINGFUL_CALL_ATTEMPT, PROFILE_VISIT, PROFILE_AND_PAGE_ENGAGEMENT, ADVERTISER_SILOED_VALUE, AUTOMATIC_OBJECTIVE, MESSAGING_APPOINTMENT_CONVERSION}</span></div></td><td><p class=\"_yd\"></p><div><div><p>The optimization goal this ad set is using.<br>\n<code>NONE</code>: Only available in read mode for campaigns created pre-v2.4.<br>\n<code>APP_INSTALLS</code>: Optimize for people more likely to install your app.<br>\n<code>AD_RECALL_LIFT</code>: Optimize for people more likely to remember seeing your ads.<br>\n<code>CLICKS</code>: Deprecated. Only available in read mode.<br>\n<code>ENGAGED_USERS</code>: Optimize for people more likely to take a particular action in your app.<br>\n<code>EVENT_RESPONSES</code>: Optimize for people more likely to attend your event.<br>\n<code>IMPRESSIONS</code>: Show the ads as many times as possible.<br>\n<code>LEAD_GENERATION</code>: Optimize for people more likely to fill out a lead generation form.<br>\n<code>QUALITY_LEAD</code>: Optimize for people who are likely to have a deeper conversation with advertisers after lead submission.<br>\n<code>LINK_CLICKS</code>: Optimize for people more likely to click in the link of the ad.<br>\n<code>OFFSITE_CONVERSIONS</code>: Optimize for people more likely to make a conversion on the site.<br>\n<code>PAGE_LIKES</code>: Optimize for people more likely to like your page.<br>\n<code>POST_ENGAGEMENT</code>: Optimize for people more likely to engage with your post.<br>\n<code>QUALITY_CALL</code>: Optimize for people who are likely to call the advertiser.<br>\n<code>REACH</code>: Optimize to reach the most unique users for each day or interval specified in <code>frequency_control_specs</code>.<br>\n<code>LANDING_PAGE_VIEWS</code>: Optimize for people who are most likely to click on and load your chosen landing page.<br>\n<code>VISIT_INSTAGRAM_PROFILE</code>: Optimize for visits to the advertiser's Instagram profile.<br>\n<code>VALUE</code>: Optimize for maximum total purchase value within the specified attribution window.<br>\n<code>THRUPLAY</code>: Optimize delivery of your ads to people who are more likely to play your ad to completion, or play it for at least 15 seconds.<br>\n<code>DERIVED_EVENTS</code>: Optimize for retention, which reaches people who are most likely to return to the app and open it again during a given time frame after installing. You can choose either two days, meaning the app is likely to be reopened between 24 and 48 hours after installation; or seven days, meaning the app is likely to be reopened between 144 and 168 hours after installation.<br>\n<code>APP_INSTALLS_AND_OFFSITE_CONVERSIONS</code>: Optimizes for people more likely to install your app and make a conversion on your site. <br>\n<code>CONVERSATIONS</code>: Directs ads to people more likely to have a conversation with the business.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>optimization_sub_event</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Optimization sub event for a specific optimization goal. For example: Sound-On event for Video-View-2s optimization goal.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>pacing_type</code></span></div><div class=\"_yb _yc\"><span>list&lt;string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Defines the pacing type, standard or using ad scheduling</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>promoted_object</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-promoted-object/\">AdPromotedObject</a></div></td><td><p class=\"_yd\"></p><div><div><p>The object this ad set is promoting across all its ads.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>recommendations</code></span></div><div class=\"_yb _yc\"><span>list&lt;AdRecommendation&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>If there are recommendations for this ad set, this field includes them. Otherwise, will not be included in the response. This field is not included in redownload mode.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>recurring_budget_semantics</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>If this field is <code>true</code>, your daily spend may be more than your daily budget while your weekly spend will not exceed 7 times your daily budget. More details explained in the <a href=\"/docs/marketing-api/adset/budget-limits\">Ad Set Budget</a> document. If this is <code>false</code>, your amount spent daily will not exceed the daily budget. This field is not applicable for lifetime budgets.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>regional_regulated_categories</code></span></div><div class=\"_yb _yc\"><span>list&lt;enum&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>This param is used to specify <code>regional_regulated_categories</code>. Currently it supports <code>null</code> and the following values:</p>\n\n<ol>\n<li>TAIWAN_FINSERV: Use this value to declare a Financial Service Ad Set if the ad targets Taiwan Audience</li>\n<li>AUSTRALIA_FINSERV: Use this value to declare a Financial Service Ad Set if the ad set targets Australia Audience</li>\n<li>TAIWAN_UNIVERSAL: Use this value to declare an Ad Set if it targets Taiwan Audience</li>\n<li>SINGAPORE_UNIVERSAL: Use this value to declare an Ad Set if it targets Singapore Audience</li>\n</ol>\n\n<p>If an ad set is a Financial Service Ad and it targets Taiwan, it needs to declare both <code>TAIWAN_FINSERV</code> and <code>TAIWAN_UNIVERSAL</code></p>\n\n<p>Example: <code>null</code> or <code>[AUSTRALIA_FINSERV]</code> or <code>[TAIWAN_FINSERV, TAIWAN_UNIVERSAL]</code></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>regional_regulation_identities</code></span></div><div class=\"_yb _yc\"><span>RegionalRegulationIdentities</span></div></td><td><p class=\"_yd\"></p><div><div><p>This param is used to specify regional_regulation_identities used to represent the ad set. Currently it supports the following fields:</p>\n\n<ol>\n<li>taiwan_finserv_beneficiary: used for TAIWAN_FINSERV category</li>\n<li>taiwan_finserv_payer: used for TAIWAN_FINSERV category</li>\n<li>australia_finserv_beneficiary: used for AUSTRALIA_FINSERV category</li>\n<li>australia_finserv_payer: used for AUSTRALIA_FINSERV category</li>\n<li>taiwan_universal_beneficiary: used for TAIWAN_UNIVERSAL category</li>\n<li>taiwan_universal_payer: used for TAIWAN_UNIVERSAL category</li>\n<li>singapore_universal_beneficiary: used for SINGAPORE_UNIVERSAL category</li>\n<li>singapore_universal_payer: used for SINGAPORE_UNIVERSAL category</li>\n</ol>\n\n<p>Example:</p>\n\n<p><code>regional_regulation_identities: {\n    \"taiwan_finserv_beneficiary\": &lt;verified_identity_id&gt;,\n    \"taiwan_finserv_payer\": &lt;verified_identity_id&gt;,\n    \"taiwan_universal_beneficiary\": &lt;verified_identity_id&gt;,\n    \"taiwan_universal_payer\": &lt;verified_identity_id&gt;,\n  }</code></p>\n\n<p>During creation and update, the passed identities fields need to correspond to declared categories.</p>\n\n<p>To update an existing ad set identities, you need to pass new values for both categories and identities to overwrite the identity id or <code>null</code> to remove existing id.</p>\n\n<p>For example:</p>\n\n<p>Upon creation, <code>regional_regulated_categories</code> is <code>[TAIWAN_FINSERV, TAIWAN_UNIVERSAL]</code> and <code>regional_regulation_identities</code> is</p>\n\n<p><code>regional_regulation_identities: {\n    \"taiwan_finserv_beneficiary\": &lt;id_123&gt;,\n    \"taiwan_finserv_payer\": &lt;id_123&gt;,\n    \"taiwan_universal_beneficiary\": &lt;id_456&gt;,\n    \"taiwan_universal_payer\": &lt;id_456&gt;,\n  }</code></p>\n\n<p>For update, passing <code>[TAIWAN_UNIVERSAL]</code> and \n<code>regional_regulation_identities: {\n    \"taiwan_finserv_beneficiary\": null\n    \"taiwan_finserv_payer\": null,\n    \"taiwan_universal_beneficiary\": &lt;id_789&gt;,\n    \"taiwan_universal_payer\": &lt;id_789&gt;,\n  }</code></p>\n\n<p>will remove <code>TAIWAN_FINSERV</code> declaration and update the identities ID of <code>TAIWAN_UNIVERSAL</code></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>review_feedback</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>Reviews for dynamic creative ad</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>rf_prediction_id</code></span></div><div class=\"_yb _yc\"><span>id</span></div></td><td><p class=\"_yd\"></p><div><div><p>Reach and frequency prediction ID</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>source_adset</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/ad-campaign/\">AdSet</a></div></td><td><p class=\"_yd\"></p><div><div><p>The source ad set that this ad set was copied from</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>source_adset_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The source ad set id that this ad set was copied from</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>start_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>Start time, in UTC UNIX timestamp</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb _yc\"><span>enum {ACTIVE, PAUSED, DELETED, ARCHIVED}</span></div></td><td><p class=\"_yd\"></p><div><div><p>The status set at the ad set level. It can be different from the\n        effective status due to its parent campaign. The field returns the same\n        value as <code>configured_status</code>, and is the suggested one to use.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>targeting</code></span></div><div class=\"_yb _yc\"><span>Targeting</span></div></td><td><p class=\"_yd\"></p><div><div><p>Targeting</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>targeting_optimization_types</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_46_om\"></a></div><div class=\"_yb _yc\"><span>list&lt;KeyValue:string,int32&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Targeting options that are relaxed and used as a signal for optimization</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>time_based_ad_rotation_id_blocks</code></span></div><div class=\"_yb _yc\"><span>list&lt;list&lt;integer&gt;&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Specify ad creative that displays at custom date ranges in a campaign\n        as an array. A list of Adgroup IDs. The list of ads to display for each\n        time range in a given schedule. For example display first ad in Adgroup\n        for first date range, second ad for second date range, and so on. You\n        can display more than one ad per date range by providing more than\n        one ad ID per array. For example set\n        <code>time_based_ad_rotation_id_blocks</code> to [[1], [2, 3], [1, 4]]. On the\n        first date range show ad 1, on the second date range show ad 2 and ad 3\n        and on the last date range show ad 1 and ad 4. Use with\n        <code>time_based_ad_rotation_intervals</code> to specify date ranges.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>time_based_ad_rotation_intervals</code></span></div><div class=\"_yb _yc\"><span>list&lt;unsigned int32&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Date range when specific ad creative displays during a campaign.\n        Provide date ranges in an array of UNIX timestamps where each\n        timestamp represents the start time for each date range. For example a\n        3-day campaign from May 9 12am to  May 11 11:59PM PST can have three\n        date ranges, the first date range starts from May 9 12:00AM to\n        May 9 11:59PM, second date range starts from May 10 12:00AM to\n        May 10 11:59PM and last starts from  May 11 12:00AM to  May 11 11:59PM.\n        The first timestamp should match the campaign start time. The last\n        timestamp should be at least 1 hour before the campaign end time. You\n        must provide at least two date ranges. All date ranges must cover the\n        whole campaign length, so any date range cannot exceed campaign length.\n        Use with <code>time_based_ad_rotation_id_blocks</code> to specify ad creative for\n        each date range.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>updated_time</code></span></div><div class=\"_yb _yc\"><span>datetime</span></div></td><td><p class=\"_yd\"></p><div><div><p>Time when the Ad Set was updated</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>use_new_app_click</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>If set, allows Mobile App Engagement ads to optimize for LINK_CLICKS</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>value_rule_set_id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>value_rule_set_id</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"edges\">Edges</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/activities/\"><code>activities</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdActivity&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The activities of this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/ad_studies/\"><code>ad_studies</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdStudy&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ad studies containing this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/adcreatives/\"><code>adcreatives</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdCreative&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The creatives of this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/adrules_governed/\"><code>adrules_governed</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdRule&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Ad rules that govern this ad set - by default, this only returns rules that either directly mention the ad set by id or indirectly through the set <code>entity_type</code></p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/ads/\"><code>ads</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Adgroup&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The ads under this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/asyncadrequests/\"><code>asyncadrequests</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdAsyncRequest&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Async ad requests for this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/copies/\"><code>copies</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdCampaign&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The copies of this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/delivery_estimate/\"><code>delivery_estimate</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AdCampaignDeliveryEstimate&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The delivery estimate for this ad set</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/message_delivery_estimate/\"><code>message_delivery_estimate</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;MessageDeliveryEstimate&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Delivery estimation of the marketing message campaign</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/ad-campaign/targetingsentencelines/\"><code>targetingsentencelines</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;TargetingSentenceLine&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The targeting description sentence for this ad set</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>2500</td><td>Error parsing graph query</td></tr></tbody></table></div></div></div>\n\n<div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>The <code>date_preset = lifetime</code> parameter is disabled in Graph API v10.0 and replaced with <code>date_preset = maximum</code>, which returns a maximum of 37 months of data. For v9.0 and below, <code>date_preset = maximum</code> will be enabled on May 25, 2021, and any <code>lifetime</code> calls will default to <code>maximum</code> and return only 37 months of data.</p>\n</div></div>\n\n<h2 id=\"Creating\">Creating</h2><div><div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>For v20.0+, the Impressions optimization goal is deprecated for the legacy Post Engagement objective and the <code>ON_POST</code> destination_type.</p>\n</div></div><h3 id=\"create-examples\">Examples</h3><p>Validate an ad set with a daily budget where the campaign objective is set to <code>APP_INSTALLS</code>.</p><p></p><pre class=\"_5s-8 prettyprint lang-html prettyprinted\" style=\"\"><span class=\"pln\">curl -X POST \\\n  -F 'name=\"Mobile App Installs Ad Set\"' \\\n  -F 'daily_budget=1000' \\\n  -F 'bid_amount=2' \\\n  -F 'billing_event=\"IMPRESSIONS\"' \\\n  -F 'optimization_goal=\"APP_INSTALLS\"' \\\n  -F 'campaign_id=\"</span><span class=\"tag\">&lt;AD_CAMPAIGN_ID&gt;</span><span class=\"pln\">\"' \\\n  -F 'promoted_object={\n       \"application_id\": \"</span><span class=\"tag\">&lt;APP_ID&gt;</span><span class=\"pln\">\",\n       \"object_store_url\": \"</span><span class=\"tag\">&lt;APP_STORE_URL&gt;</span><span class=\"pln\">\"\n     }' \\\n  -F 'targeting={\n       \"device_platforms\": [\n         \"mobile\"\n       ],\n       \"facebook_positions\": [\n         \"feed\"\n       ],\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ]\n       },\n       \"publisher_platforms\": [\n         \"facebook\",\n         \"audience_network\"\n       ],\n       \"user_os\": [\n         \"IOS\"\n       ]\n     }' \\\n  -F 'status=\"PAUSED\"' \\\n  -F 'access_token=</span><span class=\"tag\">&lt;ACCESS_TOKEN&gt;</span><span class=\"pln\">' \\\nhttps://graph.facebook.com/</span><code><span class=\"pln\">v23.0</span></code><span class=\"pln\">/act_</span><span class=\"tag\">&lt;AD_ACCOUNT_ID&gt;</span><span class=\"pln\">/adsets</span></pre><p></p><h3 id=\"create-considerations\">Considerations</h3><h4>Bid/Budget Validations</h4><p><b>Note:</b></p><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\">All values in this section are in US Dollars. </div></li><li><div class=\"fcb\">Differenct currencies have different minimum daily budget limits.</div></li><li><div class=\"fcb\">Minimum values are defined in terms of the daily budget but apply to lifetime budgets as well.</div></li></ul>\n      \n      When creating an ad set, there will be a minimum budget for different billing events (Clicks, Impressions, Actions).  If the minimum daily budget is $5, a campaign lasting 5 days will need at least $25 for budget. \n    <p></p><div class=\"_57yz _57z1 _3-8p\"><div class=\"_57y-\"><p>Budget amounts shown are for illustrative purposes only and can change based on situation.</p>\n</div></div><p>If <code>bid_strategy</code> is set to <code>LOWEST_COST_WITHOUT_CAP</code> in the ad set:</p><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nBilling Event\n</th><th>\nMinimum Daily Budget\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_47_Xj\"><tr class=\"row_0\"><td><p>Impressions</p>\n</td><td><p>$0.50</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p>Clicks/Likes/Video Views</p>\n</td><td><p>$2.50</p>\n</td></tr><tr class=\"row_2\"><td><p>Low-frequency Actions<br>\n(Includes mobile app installs, offer claims, or canvas app installs)</p>\n</td><td><p>$40<br>\n<strong>Important:</strong> This minimum daily budget is the same for all countries.</p>\n</td></tr></tbody></table></div><p>If <code>bid_strategy</code> is set to <code>LOWEST_COST_WITH_BID_CAP</code> in the ad set:</p><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>\nBilling Event\n</th><th>\nMinimum Daily Budget\n</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_48_u2\"><tr class=\"row_0\"><td><p>Impressions</p>\n</td><td><p>At least the <code>bid_amount</code>. For example, if the bid amount is $10, then $10 will be the minimum budget required.</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p>Clicks/Actions</p>\n</td><td><p>5x the <code>bid_amount</code> for a Click or Action. For example, if the bid amount is $5 per click/action, then $25 will be the minimum budget required.</p>\n</td></tr></tbody></table></div><div class=\"_57yz _57y_ _3-8p\"><div class=\"_57y-\"><p>Budgets in non-USD currencies will be converted and validated upon time of ad set creation.</p></div></div><div class=\"_57yz _57y_ _3-8p\"><div class=\"_57y-\"><p>For ads belonging to ad accounts from countries in the list below, the minimum values are 2x the ones in the tables. For example, if the billing event is an Impression, the minimum daily budget is $0.50, but in the the following countries the minimum would be $1.00:</p><p>Australia, Austria, Belgium, Canada, Denmark, Finland, France, Germany, Greece, Hong Kong, Israel, Italy, Japan, Netherlands, New Zealand, Norway, Singapore, South Korea, Spain, Sweden, Switzerland, Taiwan, United Kingdom, United States of America.</p><p>The only exception to this rule are Low-Frequency Actions when <code>bid_strategy</code> is <code>LOWEST_COST_WITHOUT_CAP</code>.</p></div></div><h4>Locale targeted page post</h4><p>If you promote a Page post which has been targeted by locale the ad set targeting must include the same, or a subset of, locale targeting as the Page post.</p><p>E.g. if the Page post is targeted at locales 6 (US English) and 24 (UK English), then the ad set must be targeted at one or more of the same locales.</p><h4>Mobile App Ads</h4><p>Mobile app ad sets should</p><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\">be used in conjunction with <a href=\"/docs/reference/ads-api/targeting-specs#mobile\">targeting spec</a> fields <code>user_device</code> and <code>user_os</code></div></li><li><div class=\"fcb\">have a <code>MOBILE_APP_*</code> objective on the <a href=\"/docs/marketing-api/adcampaign\">campaign</a></div></li></ul><h4>Desktop App Ads</h4><p>Desktop app ad sets must</p><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\">include a <a href=\"/docs/reference/ads-api/targeting-specs\">targeting spec</a> of either\n\n<ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\"><code>'page_types':['desktopfeed']</code> or</div></li><li><div class=\"fcb\"><code>'page_types':['rightcolumn']</code> or</div></li><li><div class=\"fcb\"><code>'page_types':['desktop']</code> along with the other targeting options you have selected.</div></li></ul></div></li><li><div class=\"fcb\">include a <code>CANVAS_APP_*</code> objective</div></li></ul><h4>Lookalike Expansion</h4><p>Beginning with v13.0, for newly created ad sets that optimize for value, conversions, or app events, lookalike expansion will be turned on by default and cannot be disabled. When getting an ad set that optimizes for value, conversions, or app events, we will return a new lookalike property in the <code>targeting_optimization_types</code> map that indicates lookalike expansion is enabled and complements the existing <code>detailed_targeting</code> property for the detailed targeting expansion.\n    </p><h4>Targeting DSA Regulated Locations (EU)</h4><p>For ad sets targeting the EU and/or associated territories, the <code>dsa_payor</code> and <code>dsa_beneficiary</code> fields are required. The information provided in these 2 fields  will be shown to end users to indicate who is paying for the ad and who is the beneficiary of the ad.</p><p><b>Request</b><br>\n      Include the following fields in an API call to the <code>/{adset_id}</code> endpoint.\n</p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"str\">\"dsa_payor\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;PAYOR_NAME&gt;\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"dsa_beneficiary\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"&lt;BENEFICIARY_NAME&gt;\"</span><span class=\"pln\">\n  </span><span class=\"pun\">...</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></pre><p></p><p><b>Fields</b></p><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Name</th><th>Description</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_49_Ss\"><tr class=\"row_0\"><td><p><code>dsa_payor</code></p>\n<div style=\"color:darkgrey;\">string (max 512 char)</div></td><td><p>The payor of all ads in this ad set.</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><code>dsa_beneficiary</code></p>\n<div style=\"color:darkgrey;\">string (max 512 char)</div></td><td><p>The beneficiary of all ads in this ad set.</p>\n</td></tr></tbody></table></div><p></p><p style=\"margin-bottom: 20px;\">If these fields are not provided, the API may returns the following errors:\n<br><b>Payor missing error</b></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"str\">\"error\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"str\">\"message\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"Invalid parameter\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"type\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"FacebookApiException\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"code\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"lit\">100</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_data\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"{\\\"blame_field_specs\\\":[[\\\"dsa_payor\\\"]]}\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_subcode\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"lit\">3858079</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"is_transient\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"kwd\">false</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_user_title\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"No payor provided in DSA regulated region\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_user_msg\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"The DSA requires ads to provide payor information in regulated regions. Updating/creating ad needs to provide payor of the ad.\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"fbtrace_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"fbtrace_id\"</span><span class=\"pln\">\n  </span><span class=\"pun\">},</span><span class=\"pln\">\n  </span><span class=\"str\">\"__fb_trace_id__\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"fbtrace_id\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"__www_request_id__\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"request_id\"</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></pre><b>Beneficiary missing error</b><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"str\">\"error\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"str\">\"message\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"Invalid parameter\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"type\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"FacebookApiException\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"code\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"lit\">100</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_data\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"{\\\"blame_field_specs\\\":[[\\\"dsa_beneficiary\\\"]]}\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_subcode\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"lit\">3858081</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"is_transient\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"kwd\">false</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_user_title\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"No payor/beneficiary provided in DSA regulated location\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"error_user_msg\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"The DSA requires ads to provide beneficiary information in regulated regions. Updating/creating ad needs to provide beneficiary of the ad.\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">\"fbtrace_id\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"fbtrace_id\"</span><span class=\"pln\">\n  </span><span class=\"pun\">},</span><span class=\"pln\">\n  </span><span class=\"str\">\"__fb_trace_id__\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"fbtrace_id\"</span><span class=\"pun\">,</span><span class=\"pln\">\n  </span><span class=\"str\">\"__www_request_id__\"</span><span class=\"pun\">:</span><span class=\"pln\"> </span><span class=\"str\">\"request_id\"</span><span class=\"pln\">\n</span><span class=\"pun\">}</span></pre><p></p></div><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>copies</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-campaign/copies/\"><code>/{ad_set_id}/copies</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-campaign/\">AdSet</a> will be created.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_4a_2D\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>campaign_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Single ID of a campaign to make parent of the copy. The copy inherits all campaign settings, such as budget from the parent.Ignore if you want to keep the copy under the original campaign parent.</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>deep_copy</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div><p>Whether to copy all the child ads. Limits: the total number of children ads to copy should not exceed 3 for a synchronous call and 51 for an asynchronous call.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>end_time</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>The end time of the set, e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. UTC UNIX timestamp. When creating a set with a daily budget, specify <code>end_time=0</code> to set the set to be ongoing without end date. If not set, the copied adset will inherit the end time from the original set</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>rename_options</code></span></div><div class=\"_yb\">JSON or object-like arrays</div></td><td><p class=\"_yd\"></p><div><div><p>Rename options</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>start_time</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>The start time of the set, e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. UTC UNIX timestamp. If not set, the copied adset will inherit the start time from the original set</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>status_option</code></span></div><div class=\"_yb\">enum {ACTIVE, PAUSED, INHERITED_FROM_SOURCE}</div></td><td><div>Default value: <code>PAUSED</code></div><p class=\"_yd\"></p><div><div><p><code>ACTIVE</code>: the copied adset will have active status. <code>PAUSED</code>: the copied adset will have paused status. <code>INHERITED_FROM_SOURCE</code>: the copied adset will have the status from the original set.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>copied_adset_id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>copied_adset_id</code>: numeric string, </div><div class=\"_uoj\"><code>ad_object_ids</code>:  List  [<div class=\"_uoj\"> Struct  {<div class=\"_uoj\"><code>ad_object_type</code>: enum {unique_adcreative, ad, ad_set, campaign, opportunities, privacy_info_center, topline, ad_account, product}, </div><div class=\"_uoj\"><code>source_id</code>: numeric string, </div><div class=\"_uoj\"><code>copied_id</code>: numeric string, </div>}</div>], </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can make a POST request to <code>adsets</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/ad-account/adsets/\"><code>/act_{ad_account_id}/adsets</code></a></li></ul><div>When posting to this edge, an&nbsp;<a href=\"/docs/marketing-api/reference/ad-campaign/\">AdSet</a> will be created.</div><div><h3 id=\"example-2\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_4b_e1\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_4c_Tx\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4d_JZ\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4e_My\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4f_hE\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4g_b1\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4h_AN\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=POST&amp;path=act_%3CAD_ACCOUNT_ID%3E%2Fadsets%3Fname%3DMy%2BFirst%2BAdSet%26daily_budget%3D10000%26bid_amount%3D300%26billing_event%3DIMPRESSIONS%26optimization_goal%3DREACH%26campaign_id%3D%253CAD_CAMPAIGN_ID%253E%26promoted_object%3D%257B%2522page_id%2522%253A%2522%253CPAGE_ID%253E%2522%257D%26targeting%3D%257B%2522facebook_positions%2522%253A%255B%2522feed%2522%255D%252C%2522geo_locations%2522%253A%257B%2522countries%2522%253A%255B%2522US%2522%255D%252C%2522regions%2522%253A%255B%257B%2522key%2522%253A%********%2522%257D%255D%252C%2522cities%2522%253A%255B%257B%2522key%2522%253A777934%252C%2522radius%2522%253A10%252C%2522distance_unit%2522%253A%2522mile%2522%257D%255D%257D%252C%2522genders%2522%253A%255B1%255D%252C%2522age_max%2522%253A24%252C%2522age_min%2522%253A20%252C%2522publisher_platforms%2522%253A%255B%2522facebook%2522%252C%2522audience_network%2522%255D%252C%2522device_platforms%2522%253A%255B%2522mobile%2522%255D%252C%2522flexible_spec%2522%253A%255B%257B%2522interests%2522%253A%255B%257B%2522id%2522%253A%2522%253CINTEREST_ID%253E%2522%252C%2522name%2522%253A%2522%253CINTEREST_NAME%253E%2522%257D%255D%257D%255D%257D%26status%3DPAUSED&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_4i_UA\" style=\"\"><code><span class=\"pln\">POST </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/</span><span class=\"pln\">act_</span><span class=\"pun\">&lt;</span><span class=\"pln\">AD_ACCOUNT_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/adsets HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com\n\nname</span><span class=\"pun\">=</span><span class=\"typ\">My</span><span class=\"pun\">+</span><span class=\"typ\">First</span><span class=\"pun\">+</span><span class=\"typ\">AdSet</span><span class=\"pun\">&amp;</span><span class=\"pln\">daily_budget</span><span class=\"pun\">=</span><span class=\"lit\">10000</span><span class=\"pun\">&amp;</span><span class=\"pln\">bid_amount</span><span class=\"pun\">=</span><span class=\"lit\">300</span><span class=\"pun\">&amp;</span><span class=\"pln\">billing_event</span><span class=\"pun\">=</span><span class=\"pln\">IMPRESSIONS</span><span class=\"pun\">&amp;</span><span class=\"pln\">optimization_goal</span><span class=\"pun\">=</span><span class=\"pln\">REACH</span><span class=\"pun\">&amp;</span><span class=\"pln\">campaign_id</span><span class=\"pun\">=%</span><span class=\"lit\">3CAD</span><span class=\"pln\">_CAMPAIGN_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">&amp;</span><span class=\"pln\">promoted_object</span><span class=\"pun\">=%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22page</span><span class=\"pln\">_id</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3CPAGE</span><span class=\"pln\">_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">&amp;</span><span class=\"pln\">targeting</span><span class=\"pun\">=%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22facebook</span><span class=\"pln\">_positions</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22feed</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22geo</span><span class=\"pln\">_locations</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22countries</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22US</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22regions</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22key</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">224081</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22cities</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22key</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A777934</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22radius</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A10</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22distance</span><span class=\"pln\">_unit</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22mile</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22genders</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B1</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22age</span><span class=\"pln\">_max</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A24</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22age</span><span class=\"pln\">_min</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A20</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22publisher</span><span class=\"pln\">_platforms</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22facebook</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22audience</span><span class=\"pln\">_network</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22device</span><span class=\"pln\">_platforms</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">22mobile</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22flexible</span><span class=\"pln\">_spec</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22interests</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">5B</span><span class=\"pun\">%</span><span class=\"lit\">7B</span><span class=\"pun\">%</span><span class=\"lit\">22id</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3CINTEREST</span><span class=\"pln\">_ID</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">2C</span><span class=\"pun\">%</span><span class=\"lit\">22name</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3A</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">3CINTEREST</span><span class=\"pln\">_NAME</span><span class=\"pun\">%</span><span class=\"lit\">3E</span><span class=\"pun\">%</span><span class=\"lit\">22</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">%</span><span class=\"lit\">5D</span><span class=\"pun\">%</span><span class=\"lit\">7D</span><span class=\"pun\">&amp;</span><span class=\"pln\">status</span><span class=\"pun\">=</span><span class=\"pln\">PAUSED</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_4o_Lw\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>adlabels</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Specifies list of labels to be associated with this object. This field is optional</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>adset_schedule</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Ad set schedule, representing a delivery schedule for a single day</p>\n</div></div><p></p></td></tr><tr class=\"row_2 _5m27\"><td><div class=\"_yc\"><span><code>attribution_spec</code></span></div><div class=\"_yb\">list&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Conversion attribution spec used for attributing conversions for optimization. Supported window lengths differ by optimization goal and campaign objective.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>bid_amount</code></span></div><div class=\"_yb\">integer</div></td><td><p class=\"_yd\"></p><div><div><p>Bid cap or target cost for this ad set. The bid cap used in a <em>lowest cost bid strategy</em> is defined as the maximum bid you want to pay for a result based on your <code>optimization_goal</code>. The target cost used in a <em>target cost bid strategy</em> lets Facebook bid to meet your target on average and keep costs stable as you spend. If an ad level <code>bid_amount</code> is specified, updating this value will overwrite the previous ad level bid. Unless you are using <a href=\"/docs/marketing-api/reachandfrequency\">Reach and Frequency</a>, <code>bid_amount</code> is required if <code>bid_strategy</code> is set to <code>LOWEST_COST_WITH_BID_CAP</code> or <code>COST_CAP</code>.\n<br>\n        The bid amount's unit is cents for currencies like USD, EUR, and the basic unit for currencies like JPY, KRW. The bid amount for ads with <code>IMPRESSION</code> or <code>REACH</code> as <code>billing_event</code> is per 1,000 occurrences, and has to be at least 2 US cents or more. For ads with other <code>billing_event</code>s, the bid amount is for each occurrence, and has a minimum value 1 US cents. The minimum bid amounts of other currencies are of similar value to the US Dollar values provided.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>bid_strategy</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_4p_BT\"></a></div><div class=\"_yb\">enum{LOWEST_COST_WITHOUT_CAP, LOWEST_COST_WITH_BID_CAP, COST_CAP, LOWEST_COST_WITH_MIN_ROAS}</div></td><td><p class=\"_yd\"></p><div><div><p>Choose bid strategy for this ad set to suit your specific business goals.\n        Each strategy has tradeoffs and may be available for certain <code>optimization_goal</code>s:<br>\n        <code>LOWEST_COST_WITHOUT_CAP</code>: Designed to get the most results for your budget based on\n        your ad set <code>optimization_goal</code> without limiting your bid amount. This is the best strategy\n        if you care most about cost efficiency. However with this strategy it may be harder to get\n        stable average costs as you spend. This strategy is also known as <em>automatic bidding</em>.\n        Learn more in <a href=\"https://www.facebook.com/business/help/721453268045071\">Ads Help Center, About bid strategies: Lowest cost</a>.<br>\n        <code>LOWEST_COST_WITH_BID_CAP</code>: Designed to get the most results for your budget based on\n        your ad set <code>optimization_goal</code> while limiting actual bid to your specified\n        amount. With a bid cap you have more control over your\n        cost per actual optimization event. However if you set a limit which is too low you may\n        get less ads delivery. If you select this, you must provide\n        a bid cap with the <code>bid_amount</code> field.\n        Note: during creation this bid strategy is set if you provide <code>bid_amount</code> only.\n        This strategy is also known as <em>manual maximum-cost bidding</em>.\n        Learn more in <a href=\"https://www.facebook.com/business/help/721453268045071\">Ads Help Center, About bid strategies: Lowest cost</a>.<br></p>\n\n<p>Notes:</p>\n\n<ul>\n<li><p>If you enable campaign budget optimization, you should set <code>bid_strategy</code> at the parent campaign level.</p></li>\n<li><p><code>TARGET_COST</code> bidding strategy has been deprecated with <a href=\"/docs/graph-api/changelog/version9.0\">Marketing API v9</a>.</p></li>\n</ul>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>billing_event</code></span></div><div class=\"_yb\">enum{APP_INSTALLS, CLICKS, IMPRESSIONS, LINK_CLICKS, NONE, OFFER_CLAIMS, PAGE_LIKES, POST_ENGAGEMENT, THRUPLAY, PURCHASE, LISTING_INTERACTION}</div></td><td><p class=\"_yd\"></p><div><div><p>The billing event that this ad set is using:<br>APP_INSTALLS: Pay when people install your app.<br>CLICKS: Deprecated.<br>IMPRESSIONS: Pay when the ads are shown to people.<br>LINK_CLICKS: Pay when people click on the link of the ad.<br>OFFER_CLAIMS: Pay when people claim the offer.<br>PAGE_LIKES: Pay when people like your page.<br>POST_ENGAGEMENT: Pay when people engage with your post.<br>VIDEO_VIEWS: Pay when people watch your video ads for at least 10 seconds.<br>THRUPLAY: Pay for ads that are played to completion, or played for at least 15 seconds.</p>\n</div></div><p></p></td></tr><tr class=\"row_6 _5m27\"><td><div class=\"_yc\"><span><code>budget_schedule_specs</code></span></div><div class=\"_yb\">list&lt;JSON or object-like arrays&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Initial high demand periods to be created with the ad set.<br>\nProvide list of <code>time_start</code>, <code>time_end</code>,<code>budget_value</code>, and <code>budget_value_type</code>.<br>For example,<br>-F 'budget_schedule_specs=[{<br>\n\"time_start\":1699081200,<br>\n\"time_end\":1699167600,<br>\n\"budget_value\":100,<br>\n\"budget_value_type\":\"ABSOLUTE\"<br>\n}]'\n<br>\nSee <a href=\"https://developers.facebook.com/docs/graph-api/reference/high-demand-period/\">High Demand Period</a> for more details on each field.</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>budget_source</code></span></div><div class=\"_yb\">enum{NONE, RMN}</div></td><td><p class=\"_yd\"></p><div><div><p>budget_source</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>budget_split_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>budget_split_set_id</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>campaign_attribution</code></span></div><div class=\"_yb\">enum{}</div></td><td><p class=\"_yd\"></p><div><div><p>campaign_attribution</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>campaign_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ad campaign you wish to add this ad set to.</p>\n</div></div><p></p></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>campaign_spec</code></span></div><div class=\"_yb\">Campaign spec</div></td><td><p class=\"_yd\"></p><div><div><p>Provide <code>name</code>, <code>objective</code> and <code>buying_type</code> for a campaign you want to create. Otherwise you need to provide <code>campaign_id</code> for an existing ad campaign. For example:<br>-F 'campaign_spec={<br><span>&nbsp;&nbsp;</span>\"name\": \"Inline created campaign\",<br><span>&nbsp;&nbsp;</span>\"objective\": \"CONVERSIONS\",<br><span>&nbsp;&nbsp;</span>\"buying_type\": \"AUCTION\"<br>}'\n<br><br>\nPlease refer to the <a href=\"/docs/marketing-api/reference/ad-campaign-group#odax-mapping\">Outcome-Driven Ads Experiences mapping table</a> to find new objectives and their corresponding destination types, optimization goals and promoted objects.</p>\n</div></div><p></p></td></tr><tr class=\"row_12 _5m27\"><td><div class=\"_yc\"><span><code>contextual_bundling_spec</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>settings of Contextual Bundle to support ads serving in Facebook contextual surfaces</p>\n</div></div><p></p></td></tr><tr class=\"row_13 _5m29\"><td><div class=\"_yc\"><span><code>creative_sequence</code></span></div><div class=\"_yb\">list&lt;numeric string or integer&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Order of the adgroup sequence to be shown to users</p>\n</div></div><p></p></td></tr><tr class=\"row_14\"><td><div class=\"_yc\"><span><code>daily_budget</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>The daily budget defined in your <a href=\"/docs/marketing-api/adset/budget-limits\">account currency</a>, allowed only for ad sets with a duration (difference between <code>end_time</code> and <code>start_time</code>) longer than 24 hours. <br>Either <code>daily_budget</code> or <code>lifetime_budget</code> must be greater than 0.</p>\n</div></div><p></p></td></tr><tr class=\"row_15 _5m29\"><td><div class=\"_yc\"><span><code>daily_imps</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Daily impressions. Available only for campaigns with <code>buying_type=FIXED_CPM</code></p>\n</div></div><p></p></td></tr><tr class=\"row_16\"><td><div class=\"_yc\"><span><code>daily_min_spend_target</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Daily minimum spend target of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. This target is not a guarantee but our best effort.</p>\n</div></div><p></p></td></tr><tr class=\"row_17 _5m29\"><td><div class=\"_yc\"><span><code>daily_spend_cap</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Daily spend cap of the ad set defined in your account currency. To use this field, daily budget must be specified in the Campaign. Set the value to *************** to remove the spend cap.</p>\n</div></div><p></p></td></tr><tr class=\"row_18\"><td><div class=\"_yc\"><span><code>destination_type</code></span></div><div class=\"_yb\">enum{WEBSITE, APP, MESSENGER, APPLINKS_AUTOMATIC, WHATSAPP, INSTAGRAM_DIRECT, FACEBOOK, MESSAGING_MESSENGER_WHATSAPP, MESSAGING_INSTAGRAM_DIRECT_MESSENGER, MESSAGING_INSTAGRAM_DIRECT_MESSENGER_WHATSAPP, MESSAGING_INSTAGRAM_DIRECT_WHATSAPP, SHOP_AUTOMATIC, ON_AD, ON_POST, ON_EVENT, ON_VIDEO, ON_PAGE, INSTAGRAM_PROFILE, FACEBOOK_PAGE, INSTAGRAM_PROFILE_AND_FACEBOOK_PAGE, INSTAGRAM_LIVE, FACEBOOK_LIVE, IMAGINE}</div></td><td><p class=\"_yd\"></p><div><div><p>Destination of ads in this Ad Set. Options include: Website, App, Messenger, <code>INSTAGRAM_DIRECT</code>, <code>INSTAGRAM_PROFILE</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_19 _5m29\"><td><div class=\"_yc\"><span><code>dsa_beneficiary</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>dsa_beneficiary</p>\n</div></div><p></p></td></tr><tr class=\"row_20\"><td><div class=\"_yc\"><span><code>dsa_payor</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>dsa_payor</p>\n</div></div><p></p></td></tr><tr class=\"row_21 _5m29\"><td><div class=\"_yc\"><span><code>end_time</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>End time, required when <code>lifetime_budget</code> is specified. e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. When creating a set with a daily budget, specify <code>end_time=0</code> to set the set to be ongoing and have no end date. UTC UNIX timestamp</p>\n</div></div><p></p></td></tr><tr class=\"row_22\"><td><div class=\"_yc\"><span><code>execution_options</code></span></div><div class=\"_yb\">list&lt;enum{validate_only, include_recommendations}&gt;</div></td><td><div>Default value: <code>Set</code></div><p class=\"_yd\"></p><div><div><p>An execution setting<br> <code>validate_only</code>: when this option is specified, the API call will not perform the mutation but will run through the validation rules against values of each field. <br><code>include_recommendations</code>: this option cannot be used by itself. When this option is used, recommendations  for ad object's configuration will be included. A separate section <a href=\"/docs/marketing-api/reference/ad-recommendation\">recommendations</a> will be included in the response, but only if recommendations for this specification exist.<br>If the call passes validation or review, response will be <code>{\"success\": true}</code>. If the call does not pass, an error will be returned with more details. These options can be used to improve any UI to display errors to the user much sooner, e.g. as soon as a new value is typed into any field corresponding to this ad object, rather than at the upload/save stage, or after review.</p>\n</div></div><p></p></td></tr><tr class=\"row_23 _5m29\"><td><div class=\"_yc\"><span><code>existing_customer_budget_percentage</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>existing_customer_budget_percentage</p>\n</div></div><p></p></td></tr><tr class=\"row_24 _5m27\"><td><div class=\"_yc\"><span><code>frequency_control_specs</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>An array of frequency control specs for this ad set. As there is only one event type currently supported, this array has no more than one element. Writes to this field are only available in ad sets where <code>REACH</code> is the objective.</p>\n</div></div><p></p></td></tr><tr class=\"row_25 _5m29\"><td><div class=\"_yc\"><span><code>is_dynamic_creative</code></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_4q_aw\"></a></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>Indicates the ad set must only be used for dynamic creatives. Dynamic creative ads can be created in this ad set. Defaults to <code>false</code></p>\n</div></div><p></p></td></tr><tr class=\"row_26\"><td><div class=\"_yc\"><span><code>is_sac_cfca_terms_certified</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>is_sac_cfca_terms_certified</p>\n</div></div><p></p></td></tr><tr class=\"row_27 _5m29\"><td><div class=\"_yc\"><span><code>lifetime_budget</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime budget, defined in  your <a href=\"/docs/marketing-api/adset/budget-limits\">account currency</a>. If specified, you must also specify an <code>end_time</code>.<br>Either <code>daily_budget</code> or <code>lifetime_budget</code> must be greater than 0.</p>\n</div></div><p></p></td></tr><tr class=\"row_28\"><td><div class=\"_yc\"><span><code>lifetime_imps</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime impressions. Available only for campaigns with <code>buying_type=FIXED_CPM</code></p>\n</div></div><p></p></td></tr><tr class=\"row_29 _5m29\"><td><div class=\"_yc\"><span><code>lifetime_min_spend_target</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime minimum spend target of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. This target is not a guarantee but our best effort.</p>\n</div></div><p></p></td></tr><tr class=\"row_30\"><td><div class=\"_yc\"><span><code>lifetime_spend_cap</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>Lifetime spend cap of the ad set defined in your account currency. To use this field, lifetime budget must be specified in the Campaign. Set the value to *************** to remove the spend cap.</p>\n</div></div><p></p></td></tr><tr class=\"row_31 _5m29\"><td><div class=\"_yc\"><span><code>max_budget_spend_percentage</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>max_budget_spend_percentage</p>\n</div></div><p></p></td></tr><tr class=\"row_32\"><td><div class=\"_yc\"><span><code>min_budget_spend_percentage</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>min_budget_spend_percentage</p>\n</div></div><p></p></td></tr><tr class=\"row_33 _5m29\"><td><div class=\"_yc\"><span><code>multi_optimization_goal_weight</code></span></div><div class=\"_yb\">enum{UNDEFINED, BALANCED, PREFER_INSTALL, PREFER_EVENT}</div></td><td><p class=\"_yd\"></p><div><div><p>multi_optimization_goal_weight</p>\n</div></div><p></p></td></tr><tr class=\"row_34\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Ad set name, max length of 400 characters.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span><span class=\"_1vet\">Supports Emoji</span></div></td></tr><tr class=\"row_35 _5m29\"><td><div class=\"_yc\"><span><code>optimization_goal</code></span></div><div class=\"_yb\">enum{NONE, APP_INSTALLS, AD_RECALL_LIFT, ENGAGED_USERS, EVENT_RESPONSES, IMPRESSIONS, LEAD_GENERATION, QUALITY_LEAD, LINK_CLICKS, OFFSITE_CONVERSIONS, PAGE_LIKES, POST_ENGAGEMENT, QUALITY_CALL, REACH, LANDING_PAGE_VIEWS, VISIT_INSTAGRAM_PROFILE, VALUE, THRUPLAY, DERIVED_EVENTS, APP_INSTALLS_AND_OFFSITE_CONVERSIONS, CONVERSATIONS, IN_APP_VALUE, MESSAGING_PURCHASE_CONVERSION, SUBSCRIBERS, REMINDERS_SET, MEANINGFUL_CALL_ATTEMPT, PROFILE_VISIT, PROFILE_AND_PAGE_ENGAGEMENT, ADVERTISER_SILOED_VALUE, AUTOMATIC_OBJECTIVE, MESSAGING_APPOINTMENT_CONVERSION}</div></td><td><p class=\"_yd\"></p><div><div><p>What the ad set is optimizing for. <br><code>APP_INSTALLS</code>: Will optimize for people more likely to install your app.<br><code>ENGAGED_USERS</code>: Will optimize for people more likely to take a particular action in your app.<br><code>EVENT_RESPONSES</code>: Will optimize for people more likely to attend your event.<br><code>IMPRESSIONS</code>: Will show the ads as many times as possible.<br><code>LEAD_GENERATION</code>: Will optimize for people more likely to fill out a lead generation form.<br><code>LINK_CLICKS</code>: Will optimize for people more likely to click in the link of the ad.<br><code>OFFER_CLAIMS</code>: Will optimize for people more likely to claim the offer.<br><code>OFFSITE_CONVERSIONS</code>: Will optimize for people more likely to make a conversion in the site<br><code>PAGE_ENGAGEMENT</code>: Will optimize for people more likely to engage with your page.<br><code>PAGE_LIKES</code>: Will optimize for people more likely to like your page.<br><code>POST_ENGAGEMENT</code>: Will optimize for people more likely to engage with your post.<br><code>REACH</code>: Optimize to reach the most unique users of each day or interval specified in <code>frequency_control_specs</code>.<br><code>SOCIAL_IMPRESSIONS</code>: Increase the number of impressions with social context. For example, with the names of one or more of the user's friends attached to the ad who have already liked the page or installed the app.<br><code>VALUE</code>: Will optimize for maximum total purchase value within the specified attribution window.<br><code>THRUPLAY</code>: Will optimize delivery of your ads to people are more likely to play your ad to completion, or play it for at least 15 seconds.<br><code>AD_RECALL_LIFT</code>: Optimize for people more likely to remember seeing your ads.<br><code>VISIT_INSTAGRAM_PROFILE</code>: Optimize for visits to the advertiser's instagram profile.</p>\n</div></div><p></p></td></tr><tr class=\"row_36\"><td><div class=\"_yc\"><span><code>optimization_sub_event</code></span></div><div class=\"_yb\">enum{NONE, VIDEO_SOUND_ON, TRIP_CONSIDERATION, TRAVEL_INTENT, TRAVEL_INTENT_NO_DESTINATION_INTENT, TRAVEL_INTENT_BUCKET_01, TRAVEL_INTENT_BUCKET_02, TRAVEL_INTENT_BUCKET_03, TRAVEL_INTENT_BUCKET_04, TRAVEL_INTENT_BUCKET_05}</div></td><td><p class=\"_yd\"></p><div><div><p>Optimization sub event for a specific optimization goal (ex: Sound-On event for Video-View-2s optimization goal)</p>\n</div></div><p></p></td></tr><tr class=\"row_37 _5m29\"><td><div class=\"_yc\"><span><code>pacing_type</code></span></div><div class=\"_yb\">list&lt;string&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Defines the pacing type, standard by default or using <a href=\"/docs/marketing-api/adset/pacing\">ad scheduling</a></p>\n</div></div><p></p></td></tr><tr class=\"row_38 _5m27\"><td><div class=\"_yc\"><span><code>promoted_object</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>The object this ad set is promoting across all its ads.\n       Required with certain campaign objectives.<br>\n       <b>CONVERSIONS</b>\n       </p><ul>\n       <li><code>pixel_id</code> (Conversion pixel ID)</li>\n       <li><code>pixel_id</code> (Facebook pixel ID) and <code>custom_event_type</code></li>\n       <li><code>pixel_id</code> (Facebook pixel ID) and <code>pixel_rule</code> and <code>custom_event_type</code></li>\n       <li><code>event_id</code> (Facebook event ID) and <code>custom_event_type</code></li>\n       <li><code>application_id</code>, <code>object_store_url</code>, and <code>custom_event_type</code> for\n            mobile app events</li>\n       <li><code>offline_conversion_data_set_id</code> (Offline dataset ID) and\n            <code>custom_event_type</code> for offline conversions</li>\n       </ul>\n     <b>PAGE_LIKES</b>\n     <ul>\n     <li><code>page_id</code></li>\n     </ul>\n    <b>OFFER_CLAIMS</b>\n     <ul>\n     <li><code>page_id</code></li>\n     </ul>\n     <b>LINK_CLICKS</b>\n     <ul>\n     <li><code>application_id</code> and <code>object_store_url</code> for mobile app or Canvas app engagement link clicks</li>\n     </ul>\n     <b>APP_INSTALLS</b>\n     <ul>\n     <li><code>application_id</code> and <code>object_store_url</code></li>\n     </ul>\n     <b> if the <code>optimization_goal</code> is <code>OFFSITE_CONVERSIONS</code></b>\n     <ul>\n     <li><code>application_id</code>, <code>object_store_url</code>, and <code>custom_event_type</code> (Standard Events)</li>\n     <li><code>application_id</code>, <code>object_store_url</code>, <code>custom_event_type = OTHER</code> and <code>custom_event_str</code> (Custom Events)</li>\n     </ul>\n     <b>PRODUCT_CATALOG_SALES</b>\n     <ul>\n     <li><code>product_set_id</code></li>\n     <li><code>product_set_id</code> and <code>custom_event_type</code></li>\n     </ul>\nWhen <code>optimization_goal</code> is <code>LEAD_GENERATION</code>, <code>page_id</code> needs to be passed as promoted_object.\n<br><br>\nPlease refer to the <a href=\"/docs/marketing-api/reference/ad-campaign-group#odax-mapping\">Outcome-Driven Ads Experiences mapping table</a> to find new objectives and their corresponding destination types, optimization goals and promoted objects.\n</div></div><p></p></td></tr><tr class=\"row_39 _5m29\"><td><div class=\"_yc\"><span><code>rf_prediction_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Reach and frequency prediction ID</p>\n</div></div><p></p></td></tr><tr class=\"row_40\"><td><div class=\"_yc\"><span><code>source_adset_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The source adset id that this ad is copied from (if applicable).</p>\n</div></div><p></p></td></tr><tr class=\"row_41 _5m29\"><td><div class=\"_yc\"><span><code>start_time</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>The start time of the set, e.g. <code>2015-03-12 23:59:59-07:00</code> or <code>2015-03-12 23:59:59 PDT</code>. UTC UNIX timestamp</p>\n</div></div><p></p></td></tr><tr class=\"row_42\"><td><div class=\"_yc\"><span><code>status</code></span></div><div class=\"_yb\">enum{ACTIVE, PAUSED, DELETED, ARCHIVED}</div></td><td><p class=\"_yd\"></p><div><div><p>Only <code>ACTIVE</code> and <code>PAUSED</code> are valid for creation. The other statuses\n        can be used for update. If it is set to <code>PAUSED</code>, all its active ads\n        will be paused and have an effective status <code>ADSET_PAUSED</code>.</p>\n</div></div><p></p></td></tr><tr class=\"row_43 _5m29\"><td><div class=\"_yc\"><span><code>targeting</code></span></div><div class=\"_yb\">Targeting object</div></td><td><p class=\"_yd\"></p><div><div><p>An ad set's targeting structure.  \"countries\" is required. See <a href=\"/docs/marketing-api/targeting-specs\">targeting</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_44\"><td><div class=\"_yc\"><span><code>time_based_ad_rotation_id_blocks</code></span></div><div class=\"_yb\">list&lt;list&lt;int64&gt;&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Specify ad creative that displays at custom date ranges in a campaign\n        as an array. A list of Adgroup IDs. The list of ads to display for each\n        time range in a given schedule. For example display first ad in Adgroup\n        for first date range, second ad for second date range, and so on. You\n        can display more than one ad per date range by providing more than\n        one ad ID per array. For example set\n        <code>time_based_ad_rotation_id_blocks</code> to [[1], [2, 3], [1, 4]]. On the\n        first date range show ad 1, on the second date range show ad 2 and ad 3\n        and on the last date range show ad 1 and ad 4. Use with\n        <code>time_based_ad_rotation_intervals</code> to specify date ranges.</p>\n</div></div><p></p></td></tr><tr class=\"row_45 _5m29\"><td><div class=\"_yc\"><span><code>time_based_ad_rotation_intervals</code></span></div><div class=\"_yb\">list&lt;int64&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Date range when specific ad creative displays during a campaign.\n        Provide date ranges in an array of UNIX timestamps where each\n        timestamp represents the start time for each date range. For example a\n        3-day campaign from May 9 12am to  May 11 11:59PM PST can have three\n        date ranges, the first date range starts from May 9 12:00AM to\n        May 9 11:59PM, second date range starts from May 10 12:00AM to\n        May 10 11:59PM and last starts from  May 11 12:00AM to  May 11 11:59PM.\n        The first timestamp should match the campaign start time. The last\n        timestamp should be at least 1 hour before the campaign end time. You\n        must provide at least two date ranges. All date ranges must cover the\n        whole campaign length, so any date range cannot exceed campaign length.\n        Use with <code>time_based_ad_rotation_id_blocks</code> to specify ad creative for\n        each date range.</p>\n</div></div><p></p></td></tr><tr class=\"row_46\"><td><div class=\"_yc\"><span><code>time_start</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>Time start</p>\n</div></div><p></p></td></tr><tr class=\"row_47 _5m29\"><td><div class=\"_yc\"><span><code>time_stop</code></span></div><div class=\"_yb\">datetime</div></td><td><p class=\"_yd\"></p><div><div><p>Time stop</p>\n</div></div><p></p></td></tr><tr class=\"row_48\"><td><div class=\"_yc\"><span><code>tune_for_category</code></span></div><div class=\"_yb\">enum{NONE, EMPLOYMENT, HOUSING, CREDIT, ISSUES_ELECTIONS_POLITICS, ONLINE_GAMBLING_AND_GAMING, FINANCIAL_PRODUCTS_SERVICES}</div></td><td><p class=\"_yd\"></p><div><div><p>tune_for_category</p>\n</div></div><p></p></td></tr><tr class=\"row_49 _5m29\"><td><div class=\"_yc\"><span><code>value_rule_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Value Rule Set ID</p>\n</div></div><p></p></td></tr><tr class=\"row_50\"><td><div class=\"_yc\"><span><code>value_rules_applied</code></span></div><div class=\"_yb\">boolean</div></td><td><p class=\"_yd\"></p><div><div><p>value_rules_applied</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div><div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>2641</td><td>Your ad includes or excludes locations that are currently restricted</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>2695</td><td>The ad set creation reached its campaign group(ios14) limit.</td></tr><tr><td>900</td><td>No such application exists.</td></tr><tr><td>300</td><td>Edit failure</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Updating\">Updating</h2><div><h3 id=\"update-examples\">Examples</h3><p></p><div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_4r_jf\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4s_ft\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4t_Em\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4u_oO\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4v_uV\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_4w_uj\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_4x_tK\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_4y_H7\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_4z_tO\"><span label=\"Copy Code\" value=\"curl -X POST \\\n  -F 'billing_event=&quot;IMPRESSIONS&quot;' \\\n  -F 'optimization_goal=&quot;LINK_CLICKS&quot;' \\\n  -F 'bid_amount=200' \\\n  -F 'targeting={\n       &quot;geo_locations&quot;: {\n         &quot;countries&quot;: [\n           &quot;US&quot;\n         ]\n       },\n       &quot;facebook_positions&quot;: [\n         &quot;feed&quot;\n       ]\n     }' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'billing_event=\"IMPRESSIONS\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'optimization_goal=\"LINK_CLICKS\"'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'bid_amount=200'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'targeting={\n       \"geo_locations\": {\n         \"countries\": [\n           \"US\"\n         ]\n       },\n       \"facebook_positions\": [\n         \"feed\"\n       ]\n     }'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">AD_SET_ID</span><span class=\"pun\">&gt;/</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=POST&amp;path=%3CAD_SET_ID%3E%2F?&amp;version=v23.0&amp;billing_event=IMPRESSIONS&amp;optimization_goal=LINK_CLICKS&amp;bid_amount=200&amp;targeting=%7B%22geo_locations%22%3A%7B%22countries%22%3A%5B%22US%22%5D%7D%2C%22facebook_positions%22%3A%5B%22feed%22%5D%7D\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_5f_TT\"><input type=\"hidden\" name=\"jazoest\" value=\"2911\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZHd8\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/reference/ad-campaign\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><p></p><p>To update the <code>end_time</code> of an ad set, using ISO-8601 date-time format</p><p></p><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_5g_8w\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5h_yR\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5i_ty\">cURL</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_5j_Sc\"><pre class=\"prettyprint lang-cpp prettyprinted\" style=\"\"><code><span class=\"pln\">use </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdSet</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$adset </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdSet</span><span class=\"pun\">(</span><span class=\"str\">'&lt;AD_SET_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$adset</span><span class=\"pun\">-&gt;</span><span class=\"pln\">end_time </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"str\">'2013-10-02T00:00:00-0700'</span><span class=\"pun\">;</span><span class=\"pln\">\n$adset</span><span class=\"pun\">-&gt;</span><span class=\"pln\">update</span><span class=\"pun\">();</span></code></pre><div class=\"_3-95\"></div><div></div></div></div><p></p><p>To update the status of an ad set to paused</p><p></p><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_5m_Vl\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5n_GC\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5o_VJ\">cURL</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_5p_5h\"><pre class=\"prettyprint lang-cpp prettyprinted\" style=\"\"><code><span class=\"pln\">use </span><span class=\"typ\">FacebookAds</span><span class=\"pln\">\\Object\\AdSet</span><span class=\"pun\">;</span><span class=\"pln\">\n\n$adset </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">AdSet</span><span class=\"pun\">(</span><span class=\"str\">'&lt;AD_SET_ID&gt;'</span><span class=\"pun\">);</span><span class=\"pln\">\n$adset</span><span class=\"pun\">-&gt;</span><span class=\"pln\">campaign_status </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"typ\">AdSet</span><span class=\"pun\">::</span><span class=\"pln\">STATUS_PAUSED</span><span class=\"pun\">;</span><span class=\"pln\">\n$adset</span><span class=\"pun\">-&gt;</span><span class=\"pln\">update</span><span class=\"pun\">();</span></code></pre><div class=\"_3-95\"></div><div></div></div></div><p></p><h3 id=\"remarks\">Remarks</h3><p>An archived ad set can only update two fields: <code>name</code> and <code>campaign_status</code>. The <code>campaign_status</code> field can only be changed to <code>DELETED</code>.</p><p>A deleted ad set can only change its <code>name</code>.</p><p>There are two considerations to take into account when adjusting an ad set's budget value or budget type:</p><ul class=\"uiList _4of _4kg\"><li><div class=\"fcb\">When updating a set's lifetime or daily budget to a lower value, the new value must be at least 10% greater than the current amount spent already. For example: if an ad set has a $1000 lifetime budget and has spend $300 so far, the lowest new lifetime budget would be $330.</div></li><li><div class=\"fcb\">Since <code>v2.4</code>, ad sets have a minimum required budget. Any update must take that into consideration. Check the details at the <a href=\"#create-considerations\">Create Considerations</a> section from this page.</div></li></ul><b>Note:</b> When using the Reservation buying type, some fields may not be available to be updated through the API.\n  </div><div class=\"_844_\">You can't perform this operation on this endpoint.</div>\n\n<h2 id=\"Deleting\">Deleting</h2><div><h3 id=\"delete-examples\">Examples</h3><p></p><div><div class=\"_4gnb\"><div class=\"_51xa\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_5s_av\">cURL</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5t_Jq\">Node.js SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5u_tj\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5v_rc\">Python SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5w_sh\">Java SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_5x_Ee\">Ruby SDK</button></div><div class=\"_4gnf _4fa6\" id=\"u_0_5y_+K\"><pre class=\"prettyprint lang-sh prettyprinted\" style=\"\"><div class=\"_4nnw\"><span id=\"u_0_5z_ZC\"><a tabindex=\"-1\" href=\"#\" role=\"button\" id=\"u_0_60_CK\"><span label=\"Copy Code\" value=\"curl -X DELETE \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n  https://graph.facebook.com/v23.0/<AD_SET_ID>/\" tooltipsuccess=\"Code Copied!\" tooltipsuccessduration=\"1000\" type=\"string\" tabindex=\"0\" class=\"_xd6\" data-pitloot-persistonclick=\"true\" role=\"button\" data-hover=\"tooltip\" data-tooltip-content=\"Copy Code to Clipboard\"><div class=\"_2lj1\">Copy Code</div></span></a></span></div><code><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X DELETE \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">://</span><span class=\"pln\">graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">AD_SET_ID</span><span class=\"pun\">&gt;/</span></code></pre><div class=\"_3-95\"><a role=\"button\" class=\"_42ft _4jy0 _4jy4 _4jy2 selected _51sy\" href=\"https://developers.facebook.com/tools/explorer/?method=DELETE&amp;path=%3CAD_SET_ID%3E%2F?&amp;version=v23.0&amp;\" target=\"_blank\" style=\"font-family: Arial, sans-serif;\">Open In Graph API Explorer</a><div></div></div></div><form rel=\"async\" class=\"_3gzd _69pv\" action=\"/x/ajax/sample_feedback/\" method=\"post\" id=\"u_0_6g_RU\"><input type=\"hidden\" name=\"jazoest\" value=\"2911\" autocomplete=\"off\"><input type=\"hidden\" name=\"lsd\" value=\"AVpiLb3ZHd8\" autocomplete=\"off\"><input type=\"hidden\" autocomplete=\"off\" name=\"path\" value=\"/docs/marketing-api/reference/ad-campaign\"><button value=\"1\" class=\"_42ft _3g_o _3g_s _3g_v\" type=\"submit\">Give Feedback</button><input type=\"hidden\" autocomplete=\"off\" name=\"prev_step\" value=\"1\"></form></div></div><p></p></div><div class=\"_844_\"><div class=\"_3-98\">You can delete an&nbsp;<a href=\"/docs/marketing-api/reference/ad-campaign/\">AdSet</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/ad-campaign/\"><code>/{ad_set_id}</code></a>.<div><h3 id=\"example-3\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_6h_1i\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6i_BC\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_6j_Zf\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_6k_Fa\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_6l_y8\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_6m_L1\">iOS SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_6n_By\">cURL</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=DELETE&amp;path=%3CAD_SET_ID%3E%2F&amp;version=v23.0\" target=\"_blank\">Graph API Explorer</a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_6o_E4\" style=\"\"><code><span class=\"pln\">DELETE </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/&lt;</span><span class=\"pln\">AD_SET_ID</span><span class=\"pun\">&gt;</span><span class=\"str\">/ HTTP/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters-3\">Parameters</h3>This endpoint doesn't have any parameters.</div><h3 id=\"return-type-2\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-3\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr></tbody></table></div></div></div>\n\n<span><h2 id=\"odax\">Outcome-Driven Ads Experiences</h2>\n\n<h3 id=\"example-4\">Example</h3>\n\n<p><strong>Outcome-Driven Ads Experiences (Engagement Outcome + <code>ON_PAGE</code> destination_type)</strong></p>\n<table class=\"uiGrid _51mz _57v1 _5f0n\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr class=\"_51mx\"><td class=\"_51m- vTop _57v2 _2cs2\"></td><td class=\"_51m- vTop _57v2 _2cs2 _51mw\"><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">i </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"name=New ODAX Adset\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"autobid=true\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"optimization_goal=PAGE_LIKES\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"destination_type=ON_PAGE\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"billing_event=IMPRESSIONS\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"daily_budget=500\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"targeting={\\\"geo_locations\\\": {\\\"countries\\\": [\\\"US\\\"]}}\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"promoted_object={\\\"page_id\\\": PAGE_ID}\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"campaign_id=CAMPAIGN_ID\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"status=PAUSED\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"access_token=ACCESS_TOKEN\"</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v11.0/</span><span class=\"pln\">\n  act_AD_ACCOUNT_ID</span><span class=\"pun\">/</span><span class=\"pln\">adsets\n</span></pre></td></tr></tbody></table><div style=\"margin-bottom:40px;\"></div><p><strong>Legacy</strong></p>\n<table class=\"uiGrid _51mz _57v1 _5f0n\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr class=\"_51mx\"><td class=\"_51m- vTop _57v2 _2cs2\"></td><td class=\"_51m- vTop _57v2 _2cs2 _51mw\"><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">i </span><span class=\"pun\">-</span><span class=\"pln\">X POST \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"name=New ODAX Adset\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"autobid=true\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"optimization_goal=PAGE_LIKES\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"billing_event=IMPRESSIONS\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"daily_budget=500\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"targeting={\\\"geo_locations\\\": {\\\"countries\\\": [\\\"US\\\"]}}\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"promoted_object={\\\"page_id\\\": PAGE_ID}\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"campaign_id=CAMPAIGN_ID\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"status=PAUSED\"</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"access_token=ACCESS_TOKEN\"</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/v11.0/</span><span class=\"pln\">\n  act_AD_ACCOUNT_ID</span><span class=\"pun\">/</span><span class=\"pln\">adsets</span></pre></td></tr></tbody></table><h3 id=\"restrictions\">Restrictions</h3>\n\n<p>There will be new restrictions on Outcome-Driven Ads Experiences (ODAX) campaigns as outlined in the table below. Refer to the <a href=\"/docs/marketing-api/reference/ad-campaign-group#odax-mapping\">Outcome-Driven Ads Experiences mapping table</a> to find the new objectives and their corresponding destination types, optimization goals and promoted objects.</p>\n<div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>ODAX Objectives</th><th>Conversion Location (L2)</th><th>Conversion Events (L2)</th><th>Optimization Goals (L2)</th><th>Legacy Objectives</th></tr></thead><tbody class=\"_5m37\" id=\"u_0_6u_2x\"><tr class=\"row_0\"><td><p><strong>Awareness</strong><br>\n<em>Reach the largest number of people who are likely to remember your ad.</em></p>\n</td><td><p>N/A</p>\n</td><td><p>N/A</p>\n</td><td><p>Ad Recall Lift, Reach, Impressions</p>\n<br><p>API enum {<code>AD_RECALL_LIFT</code>, <code>REACH</code>, <code>IMPRESSIONS</code>}</p>\n</td><td><p>Reach, Brand Awareness</p>\n</td></tr><tr class=\"row_1 _5m29\"><td><p><strong>Traffic</strong><br>\n<em>Send people to a destination like your website, app or Shop.</em></p>\n</td><td><p>Facebook Shops (closed beta)</p>\n</td><td><p>N/A</p>\n</td><td><p>Link Clicks</p>\n<br><p>API enum {<code>LINK_CLICKS</code>}</p>\n</td><td><p>Traffic</p>\n</td></tr><tr class=\"row_2\"><td></td><td><p>Website</p>\n</td><td><p>N/A</p>\n</td><td><p>Landing Page Views, Link Clicks, Impressions, Daily Unique Reach</p>\n<br><p>API enum {<code>LANDING_PAGE_VIEWS</code>, <code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Traffic</p>\n</td></tr><tr class=\"row_3 _5m29\"><td></td><td><p>App</p>\n</td><td><p>N/A</p>\n</td><td><p>Link Clicks, Daily Unique Reach</p>\n<br><p>API enum {<code>LINK_CLICKS</code>, <code>REACH</code>}</p>\n</td><td><p>Traffic</p>\n</td></tr><tr class=\"row_4\"><td></td><td><p>Messenger</p>\n</td><td><p>N/A</p>\n</td><td><p>Link Clicks, Impressions, Daily Unique Reach</p>\n<br><p>API enum {<code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Traffic</p>\n</td></tr><tr class=\"row_5 _5m29\"><td></td><td><p>WhatsApp</p>\n</td><td><p>N/A</p>\n</td><td><p>Link Clicks, Impressions, Daily Unique Reach</p>\n<br><p>API enum {<code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Traffic</p>\n</td></tr><tr class=\"row_6\"><td><p><strong>Engagement</strong><br>\n<em>Find people likely to interact with your business online, and take actions like starting a conversation or commenting on posts.</em></p>\n</td><td><p>On Video</p>\n</td><td><p>N/A</p>\n</td><td><p>ThruPlay, 2 second continuous view</p>\n<br><p>API enum {<code>THRUPLAY</code>, <code>TWO_SECOND_CONTINUOUS_VIDEO_VIEWS</code>}</p>\n</td><td><p>Video Views</p>\n</td></tr><tr class=\"row_7 _5m29\"><td></td><td><p>On Post</p>\n</td><td><p>N/A</p>\n</td><td><p>Post Engagement, Impressions, Daily Unique Reach</p>\n<br><p>API enum {<code>POST_ENGAGEMENT</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Post Engagement</p>\n</td></tr><tr class=\"row_8\"><td></td><td><p>On Event</p>\n</td><td><p>N/A</p>\n</td><td><p>Event Response, Impressions, Post Engagement, Daily Unique Reach</p>\n<br><p>API enum {<code>EVENT_RESPONSES</code>, <code>IMPRESSIONS</code>, <code>POST_ENGAGEMENT</code>, <code>REACH</code>}</p>\n</td><td><p>Event Responses</p>\n</td></tr><tr class=\"row_9 _5m29\"><td></td><td><p>Messenger</p>\n</td><td><p>N/A</p>\n</td><td><p>Conversations, Link Clicks</p>\n<br><p>API enum {<code>CONVERSATIONS</code>, <code>LINK_CLICKS</code>}</p>\n</td><td><p>Messages</p>\n</td></tr><tr class=\"row_10\"><td></td><td><p>WhatsApp</p>\n</td><td><p>N/A</p>\n</td><td><p>Conversations, Link Clicks</p>\n<br><p>API enum {<code>CONVERSATIONS</code>, <code>LINK_CLICKS</code>}</p>\n</td><td><p>Messages</p>\n</td></tr><tr class=\"row_11 _5m29\"><td></td><td><p>Instagram</p>\n</td><td><p>N/A</p>\n</td><td><p>Conversations, Link Clicks</p>\n<br><p>API enum {<code>CONVERSATIONS</code>, <code>LINK_CLICKS</code>}</p>\n</td><td><p>Messages</p>\n</td></tr><tr class=\"row_12\"><td></td><td><p>Website</p>\n</td><td><p>AddToWishlist, Contact, CustomizeProduct, Donate, FindLocation,, Schedule, Search, StartTrial, SubmitApplication, Subscribe, ViewContent</p>\n</td><td><p>Conversions, Landing Page Views, Link Clicks, Impressions, Daily Unique Reach</p>\n<br><p>API enum {<code>OFFSITE_CONVERSIONS</code>, <code>ONSITE_CONVERSIONS</code>,  <code>LANDING_PAGE_VIEWS</code>, <code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_13 _5m29\"><td></td><td><p>App</p>\n</td><td><p>Achieve Level, Activate App, Add to Wishlist, Complete Tutorial, Contact, Customize Product, Donate, Find Location, In-App Ad Click, In-App Ad Impression, Rate, Schedule, Search, Spent Credits, Start Trial, Submit Application, Subscribe, Unlock Achievement, View Content</p>\n</td><td><p>App Events, Link Clicks, Daily Unique Reach</p>\n<br><p>API enum {<code>APP_INSTALLS_AND_OFFSITE_CONVERSIONS</code>, <code>LINK_CLICKS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_14\"><td></td><td><p>On Page</p>\n</td><td><p>N/A</p>\n</td><td><p>Page Likes</p>\n<br><p>API enum {<code>PAGE_LIKES</code>}</p>\n</td><td><p>Engagement</p>\n</td></tr><tr class=\"row_15 _5m29\"><td><p><strong>Leads</strong><br>\n<em>Find people interested in your business who are likely to share their contact information.</em></p>\n</td><td><p>Website</p>\n</td><td><p>Lead, CompleteRegistration, Contact, FindLocation, Schedule, StartTrial, SubmitApplication, Subscribe</p>\n</td><td><p>Conversions, Landing Page Views, Link Clicks, Impressions, Daily Unique Reach</p>\n<br><p>API enum {<code>OFFSITE_CONVERSIONS</code>, <code>ONSITE_CONVERSIONS</code>,  <code>LANDING_PAGE_VIEWS</code>, <code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_16\"><td></td><td><p>Instant Forms</p>\n</td><td><p>N/A</p>\n</td><td><p>Leads</p>\n<br><p>API enum {<code>LEAD_GENERATION</code>, <code>QUALITY_LEAD</code>}</p>\n</td><td><p>Lead Generation</p>\n</td></tr><tr class=\"row_17 _5m29\"><td></td><td><p>Messenger</p>\n</td><td><p>N/A</p>\n</td><td><p>Leads</p>\n<br><p>API enum {<code>LEAD_GENERATION</code>, <code>QUALITY_LEAD</code>}</p>\n</td><td><p>Messages</p>\n</td></tr><tr class=\"row_18\"><td></td><td><p>Calls</p>\n</td><td><p>N/A</p>\n</td><td><p>Calls</p>\n<br><p>API enum {<code>QUALITY_CALL</code>}</p>\n</td><td><p>Lead Generation</p>\n</td></tr><tr class=\"row_19 _5m29\"><td></td><td><p>App</p>\n</td><td><p>Complete Registration, Complete Tutorial, Contact, Find Location, Schedule, Start Trial, Submit Application, Subscribe</p>\n</td><td><p>App Events, Link Clicks, Daily Unique Reach</p>\n<br><p>API enum {<code>APP_INSTALLS_AND_OFFSITE_CONVERSIONS</code>, <code>LINK_CLICKS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_20\"><td><p><strong>App Promotion</strong><br>\n<em>Find people likely to install your app.</em></p>\n</td><td><p>N/A</p>\n</td><td><p>All app events, including all custom events</p>\n</td><td><p>Non-AAA: Link Clicks, App Installs, App Events, Value</p>\n<br><p>API enum {<code>LINK_CLICKS</code>, <code>APP_INSTALLS</code>, <code>APP_INSTALLS_AND_OFFSITE_CONVERSIONS</code>, <code>VALUE</code>}</p>\n<br><p>AAA: App Installs, App Installs w/ App Events, App Events, Value</p>\n<br><p>API enum {<code>APP_INSTALLS</code>, <code>APP_INSTALLS_AND_OFFSITE_CONVERSIONS</code>, <code>VALUE</code>}</p>\n</td><td><p>App Installs</p>\n</td></tr><tr class=\"row_21 _5m29\"><td><p><strong>Sales</strong><br>\n<em>Find people likely to make purchases or take other important actions online or in store.</em></p>\n</td><td><p>Website &amp; Facebook Shops (closed beta)</p>\n</td><td><p>Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent</p>\n</td><td><p>(source of truth: same as today's Conversions objective + web and shop)</p>\n<br><p>API enum {<code>OFFSITE_CONVERSIONS</code>, <code>VALUE</code>, <code>LINK_CLICKS</code>, <code>LANDING_PAGE_VIEWS</code>, <code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_22\"><td></td><td><p>Website</p>\n</td><td><p>Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent</p>\n</td><td><p>Conversions, Value, Landing Page Views, Link Clicks, Impressions, Daily Unique Reach</p>\n<br><p>API enum {<code>OFFSITE_CONVERSIONS</code>, <code>VALUE</code>, <code>LANDING_PAGE_VIEWS</code>, <code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_23 _5m29\"><td></td><td><p>App</p>\n</td><td><p>Purchase, Initiate Checkout, Add Payment Info, Add to Cart, Complete Registration, Donate, In-App Ad Click, In-App Ad Impression, Spent Credits, Start Trial, Subscribe, View Content</p>\n</td><td><p>App Events, Link Clicks, Daily Unique Reach</p>\n<br><p>API enum {<code>OFFSITE_CONVERSIONS</code>, <code>LINK_CLICKS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_24\"><td></td><td><p>Website &amp; App</p>\n</td><td><p>Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent</p>\n</td><td><p>Conversions</p>\n<br><p>API enum {<code>OFFSITE_CONVERSIONS</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_25 _5m29\"><td></td><td><p>Messenger</p>\n</td><td><p>Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent</p>\n</td><td><p>Conversations, Conversions, Link Clicks, Impressions, Reach</p>\n<br><p>API enum {<code>CONVERSATIONS</code>, <code>OFFSITE_CONVERSIONS</code>, <code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr><tr class=\"row_26\"><td></td><td><p>WhatsApp</p>\n</td><td><p>Purchase, InitiateCheckout, AddPaymentInfo, AddToCart, CompleteRegistration, Donate, StartTrial, Subscribe, ViewContent</p>\n</td><td><p>Conversions, Link Clicks, Impressions, Reach</p>\n<br><p>API enum {<code>OFFSITE_CONVERSIONS</code>, <code>LINK_CLICKS</code>, <code>IMPRESSIONS</code>, <code>REACH</code>}</p>\n</td><td><p>Conversions</p>\n</td></tr></tbody></table></div></span>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-campaign", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/optimizedcpm", "/docs/marketing-api/reference/ad-campaign/promoted-object", "/docs/marketing-api/special-ad-category", "/docs/marketing-api/reference/ad-account/dsa_recommendations", "/docs/marketing-api/reference/ad-campaign-group#attribution_spec", "/docs/marketing-api/adset/budget-limits", "/docs/marketing-api/reference/ad-campaign/activities/", "/docs/marketing-api/reference/ad-campaign/ad_studies/", "/docs/marketing-api/reference/ad-campaign/adcreatives/", "/docs/marketing-api/reference/ad-campaign/adrules_governed/", "/docs/marketing-api/reference/ad-campaign/ads/", "/docs/marketing-api/reference/ad-campaign/asyncadrequests/", "/docs/marketing-api/reference/ad-campaign/copies/", "/docs/marketing-api/reference/ad-campaign/delivery_estimate/", "/docs/marketing-api/reference/ad-campaign/message_delivery_estimate/", "/docs/marketing-api/reference/ad-campaign/targetingsentencelines/", "/docs/marketing-api/adcampaign", "/docs/marketing-api/reference/ad-campaign/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reachandfrequency", "/docs/marketing-api/reference/ad-campaign-group#odax-mapping", "/docs/marketing-api/reference/ad-recommendation", "/docs/marketing-api/adset/pacing", "/docs/marketing-api/dynamic-product-ads", "/docs/marketing-api/targeting-specs"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-campaign", "timestamp": "2025-06-25T15:51:41.638Z"}