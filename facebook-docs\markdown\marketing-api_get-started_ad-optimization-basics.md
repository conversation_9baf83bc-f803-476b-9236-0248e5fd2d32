# Ad Optimization Basics

# Ad Optimization Basics

The Marketing API offers endpoints to manage audiences and analyze advertising campaign insights. Understanding these endpoints and their functionalities is important for both new and experienced developers looking to optimize their advertising strategies.

## Ad Optimization Endpoints

### The `customaudiences` endpoint

The [`customaudiences` endpoint](/docs/marketing-api/reference/ad-account/customaudiences) allows you to create and manage custom and lookalike audiences, tailoring ads to specific user segments based on demographics, interests, and behaviors.

**Example API Request**

```
curl \-X POST \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/customaudiences \\
  \-F 'name=My Custom Audience' \\
  \-F 'subtype=CUSTOM' \\
  \-F 'access\_token=<ACCESS\_TOKEN>'
```

### The `insights` endpoint

The [`insights` endpoint](/docs/marketing-api/reference/ad-account/insights) provides valuable analytics about the performance of campaigns, ad sets, and ads, allowing you to track key metrics such as impressions, clicks, and conversions.

**Example API Request**

```
curl \-X GET \\
  https://graph.facebook.com/`v23.0`/act\_<AD\_ACCOUNT\_ID>/insights \\
  \-F 'fields=impressions,clicks,spend' \\
  \-F 'time\_range={"since":"2023-01-01","until":"2023-12-31"}' \\
  \-F 'access\_token=<ACCESS\_TOKEN>'
```

[

→

Next

Monitoring and Analytics

](/docs/marketing-api/get-started/ad-optimization-basics/monitoring-and-analytics)