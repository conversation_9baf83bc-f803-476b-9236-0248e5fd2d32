{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '../lib/supabase';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [session, setSession] = useState(null);\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const {\n        data: {\n          session\n        },\n        error\n      } = await supabase.auth.getSession();\n      if (error) {\n        console.error('Error getting session:', error);\n      } else {\n        var _session$user;\n        setSession(session);\n        setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n      }\n      setLoading(false);\n    };\n    getInitialSession();\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      var _session$user2;\n      console.log('Auth state changed:', event, session);\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      setLoading(false);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n\n  // Sign up function\n  const signUp = async (email, password, metadata = {}) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: metadata\n        }\n      });\n      if (error) throw error;\n      toast.success('Account created! Please check your email to verify.');\n      return data;\n    } catch (error) {\n      console.error('Error signing up:', error);\n      toast.error(error.message);\n      throw error;\n    }\n  };\n\n  // Sign in function\n  const signIn = async (email, password) => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n      if (error) throw error;\n      toast.success('Login successful!');\n      return data;\n    } catch (error) {\n      console.error('Error signing in:', error);\n      toast.error(error.message);\n      throw error;\n    }\n  };\n\n  // Sign out function\n  const signOut = async () => {\n    try {\n      const {\n        error\n      } = await supabase.auth.signOut();\n      if (error) throw error;\n      toast.success('Logged out successfully');\n    } catch (error) {\n      console.error('Error signing out:', error);\n      toast.error('Error signing out');\n      throw error;\n    }\n  };\n\n  // Reset password function\n  const resetPassword = async email => {\n    try {\n      const {\n        data,\n        error\n      } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/reset-password`\n      });\n      if (error) throw error;\n      toast.success('Password reset email sent!');\n      return data;\n    } catch (error) {\n      console.error('Error resetting password:', error);\n      toast.error(error.message);\n      throw error;\n    }\n  };\n\n  // Get access token\n  const getToken = async () => {\n    try {\n      const {\n        data: {\n          session\n        },\n        error\n      } = await supabase.auth.getSession();\n      if (error) throw error;\n      return session === null || session === void 0 ? void 0 : session.access_token;\n    } catch (error) {\n      console.error('Error getting token:', error);\n      throw error;\n    }\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user && !!session;\n  };\n\n  // Legacy functions for backward compatibility\n  const login = signIn;\n  const register = signUp;\n  const logout = signOut;\n  const value = {\n    user,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    resetPassword,\n    getToken,\n    isAuthenticated,\n    // Legacy compatibility\n    login,\n    register,\n    logout\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"k8IDfYqdQDRIjOgFgI8DROyNge4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "supabase", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "session", "setSession", "getInitialSession", "data", "error", "auth", "getSession", "console", "_session$user", "subscription", "onAuthStateChange", "event", "_session$user2", "log", "unsubscribe", "signUp", "email", "password", "metadata", "options", "success", "message", "signIn", "signInWithPassword", "signOut", "resetPassword", "resetPasswordForEmail", "redirectTo", "window", "location", "origin", "getToken", "access_token", "isAuthenticated", "login", "register", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { supabase } from '../lib/supabase';\nimport toast from 'react-hot-toast';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [session, setSession] = useState(null);\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session }, error } = await supabase.auth.getSession();\n      if (error) {\n        console.error('Error getting session:', error);\n      } else {\n        setSession(session);\n        setUser(session?.user ?? null);\n      }\n      setLoading(false);\n    };\n\n    getInitialSession();\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session);\n        setSession(session);\n        setUser(session?.user ?? null);\n        setLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  // Sign up function\n  const signUp = async (email, password, metadata = {}) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: metadata\n        }\n      });\n\n      if (error) throw error;\n\n      toast.success('Account created! Please check your email to verify.');\n      return data;\n    } catch (error) {\n      console.error('Error signing up:', error);\n      toast.error(error.message);\n      throw error;\n    }\n  };\n\n  // Sign in function\n  const signIn = async (email, password) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n      });\n\n      if (error) throw error;\n\n      toast.success('Login successful!');\n      return data;\n    } catch (error) {\n      console.error('Error signing in:', error);\n      toast.error(error.message);\n      throw error;\n    }\n  };\n\n  // Sign out function\n  const signOut = async () => {\n    try {\n      const { error } = await supabase.auth.signOut();\n      if (error) throw error;\n      toast.success('Logged out successfully');\n    } catch (error) {\n      console.error('Error signing out:', error);\n      toast.error('Error signing out');\n      throw error;\n    }\n  };\n\n  // Reset password function\n  const resetPassword = async (email) => {\n    try {\n      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/reset-password`\n      });\n\n      if (error) throw error;\n\n      toast.success('Password reset email sent!');\n      return data;\n    } catch (error) {\n      console.error('Error resetting password:', error);\n      toast.error(error.message);\n      throw error;\n    }\n  };\n\n  // Get access token\n  const getToken = async () => {\n    try {\n      const { data: { session }, error } = await supabase.auth.getSession();\n      if (error) throw error;\n      return session?.access_token;\n    } catch (error) {\n      console.error('Error getting token:', error);\n      throw error;\n    }\n  };\n\n  // Check if user is authenticated\n  const isAuthenticated = () => {\n    return !!user && !!session;\n  };\n\n  // Legacy functions for backward compatibility\n  const login = signIn;\n  const register = signUp;\n  const logout = signOut;\n\n  const value = {\n    user,\n    session,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    resetPassword,\n    getToken,\n    isAuthenticated,\n    // Legacy compatibility\n    login,\n    register,\n    logout,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,MAAM;QAAEC,IAAI,EAAE;UAAEH;QAAQ,CAAC;QAAEI;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAACC,UAAU,CAAC,CAAC;MACrE,IAAIF,KAAK,EAAE;QACTG,OAAO,CAACH,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD,CAAC,MAAM;QAAA,IAAAI,aAAA;QACLP,UAAU,CAACD,OAAO,CAAC;QACnBH,OAAO,EAAAW,aAAA,GAACR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAY,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;MAChC;MACAT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDG,iBAAiB,CAAC,CAAC;;IAEnB;IACA,MAAM;MAAEC,IAAI,EAAE;QAAEM;MAAa;IAAE,CAAC,GAAGzB,QAAQ,CAACqB,IAAI,CAACK,iBAAiB,CAChE,OAAOC,KAAK,EAAEX,OAAO,KAAK;MAAA,IAAAY,cAAA;MACxBL,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAEF,KAAK,EAAEX,OAAO,CAAC;MAClDC,UAAU,CAACD,OAAO,CAAC;MACnBH,OAAO,EAAAe,cAAA,GAACZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEJ,IAAI,cAAAgB,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAC9Bb,UAAU,CAAC,KAAK,CAAC;IACnB,CACF,CAAC;IAED,OAAO,MAAMU,YAAY,CAACK,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,MAAM,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC,KAAK;IACvD,IAAI;MACF,MAAM;QAAEf,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAACU,MAAM,CAAC;QACjDC,KAAK;QACLC,QAAQ;QACRE,OAAO,EAAE;UACPhB,IAAI,EAAEe;QACR;MACF,CAAC,CAAC;MAEF,IAAId,KAAK,EAAE,MAAMA,KAAK;MAEtBnB,KAAK,CAACmC,OAAO,CAAC,qDAAqD,CAAC;MACpE,OAAOjB,IAAI;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCnB,KAAK,CAACmB,KAAK,CAACA,KAAK,CAACiB,OAAO,CAAC;MAC1B,MAAMjB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMkB,MAAM,GAAG,MAAAA,CAAON,KAAK,EAAEC,QAAQ,KAAK;IACxC,IAAI;MACF,MAAM;QAAEd,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAACkB,kBAAkB,CAAC;QAC7DP,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAIb,KAAK,EAAE,MAAMA,KAAK;MAEtBnB,KAAK,CAACmC,OAAO,CAAC,mBAAmB,CAAC;MAClC,OAAOjB,IAAI;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCnB,KAAK,CAACmB,KAAK,CAACA,KAAK,CAACiB,OAAO,CAAC;MAC1B,MAAMjB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMoB,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAM;QAAEpB;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAACmB,OAAO,CAAC,CAAC;MAC/C,IAAIpB,KAAK,EAAE,MAAMA,KAAK;MACtBnB,KAAK,CAACmC,OAAO,CAAC,yBAAyB,CAAC;IAC1C,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CnB,KAAK,CAACmB,KAAK,CAAC,mBAAmB,CAAC;MAChC,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMqB,aAAa,GAAG,MAAOT,KAAK,IAAK;IACrC,IAAI;MACF,MAAM;QAAEb,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAACqB,qBAAqB,CAACV,KAAK,EAAE;QACvEW,UAAU,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM;MACvC,CAAC,CAAC;MAEF,IAAI1B,KAAK,EAAE,MAAMA,KAAK;MAEtBnB,KAAK,CAACmC,OAAO,CAAC,4BAA4B,CAAC;MAC3C,OAAOjB,IAAI;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDnB,KAAK,CAACmB,KAAK,CAACA,KAAK,CAACiB,OAAO,CAAC;MAC1B,MAAMjB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM2B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAM;QAAE5B,IAAI,EAAE;UAAEH;QAAQ,CAAC;QAAEI;MAAM,CAAC,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAACC,UAAU,CAAC,CAAC;MACrE,IAAIF,KAAK,EAAE,MAAMA,KAAK;MACtB,OAAOJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgC,YAAY;IAC9B,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAO,CAAC,CAACrC,IAAI,IAAI,CAAC,CAACI,OAAO;EAC5B,CAAC;;EAED;EACA,MAAMkC,KAAK,GAAGZ,MAAM;EACpB,MAAMa,QAAQ,GAAGpB,MAAM;EACvB,MAAMqB,MAAM,GAAGZ,OAAO;EAEtB,MAAMa,KAAK,GAAG;IACZzC,IAAI;IACJI,OAAO;IACPF,OAAO;IACPiB,MAAM;IACNO,MAAM;IACNE,OAAO;IACPC,aAAa;IACbM,QAAQ;IACRE,eAAe;IACf;IACAC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC;EAED,oBACEjD,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA3C,QAAA,EAChCA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/C,GAAA,CApJWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}