# Facebook Marketing API - Ad Account Publisher Block Lists

## Summary
This endpoint allows creating publisher block lists for ad accounts to control where ads are displayed. Only POST operations are supported for creating new block lists with a name parameter.

## Key Points
- Only POST (create) operations are supported for publisher block lists
- Requires a name parameter to create a new block list
- Returns a numeric string ID upon successful creation
- Supports read-after-write functionality
- Read, update, and delete operations are not available

## API Endpoints
- `POST /act_{ad_account_id}/publisher_block_lists`

## Parameters
- name (string) - Name of the block list

## Content
# Ad Account Publisher Block Lists

*Graph API Version: v23.0*

## Overview

The Publisher Block Lists endpoint allows you to manage lists of publishers where you don't want your ads to appear. This endpoint only supports creating new block lists.

## Supported Operations

### Reading
❌ **Not supported** - You can't perform read operations on this endpoint.

### Creating
✅ **Supported** - Create new publisher block lists

**Endpoint:** `POST /act_{ad_account_id}/publisher_block_lists`

When posting to this edge, a [PublisherBlockList](/docs/marketing-api/reference/publisher-block-list/) will be created.

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `name` | string | Name of the block list |

#### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.

```json
{
  "id": "numeric string"
}
```

#### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |

### Updating
❌ **Not supported** - You can't perform update operations on this endpoint.

### Deleting
❌ **Not supported** - You can't perform delete operations on this endpoint.

---
**Tags:** Facebook Marketing API, Publisher Block Lists, Ad Account, POST endpoint, Graph API v23.0
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/publisher_block_lists/
**Processed:** 2025-06-25T15:37:52.039Z