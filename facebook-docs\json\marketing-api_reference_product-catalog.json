{"title": "Product Catalog", "breadcrumbs": [], "content": "<div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_Y+\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_3Z\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_8f\"></div></span></div></div>\n\n<h1 id=\"overview\">Product Catalog</h1>\n\n<p>Represents a catalog for your business you can use to deliver ads with <a href=\"/docs/marketing-api/dynamic-ad\">dynamic ads</a>.       \n      </p><p><b>Example</b> — View all product catalogs associated to your business</p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">\"access_token=&lt;ACCESS_TOKEN&gt;\"</span><span class=\"pln\"> \\\n  </span><span class=\"str\">\"https://graph.facebook.com/&lt;API_VERSION&gt;/&lt;BUSINESS_ID&gt;/owned_product_catalogs\"</span></pre><p></p><p>You can associate pixels and apps with a product catalog and then display products in ads based on signals from pixels or apps. </p><p><b>Example</b> — Make an <code>HTTP POST</code></p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'external_event_sources=[&lt;PIXEL_ID&gt;,&lt;APP_ID&gt;]'</span><span class=\"pln\"> \\ \n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;VERSION&gt;/&lt;PRODUCT_CATALOG_ID&gt;/external_event_sources</span></pre><p></p><h2 id=\"permissions\">Permissions</h2><p>You need the appropriate <a href=\"/docs/marketing-api/access#limits\">Marketing API Access Level</a> and must accept the <a href=\"https://business.facebook.com/legal/product_catalog_terms/\">Terms of Service</a> by creating your first catalog through <a href=\"https://business.facebook.com/\">Business Manager</a>.</p>\n\n<h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>Product catalogs contain a list of items like products, hotels or flights, and the information needed to display them in dynamic ads</p>\n</div><div></div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_5_FJ\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>segment_use_cases</code></span></div><div class=\"_yb\">array&lt;enum {AFFILIATE_SELLER_STOREFRONT, AFFILIATE_TAGGED_ONLY_DEPRECATED, COLLAB_ADS, COLLAB_ADS_FOR_MARKETPLACE_PARTNER, COLLAB_ADS_SEGMENT_WITHOUT_SEGMENT_SYNCING, DIGITAL_CIRCULARS, FB_LIVE_SHOPPING, IG_SHOPPING, IG_SHOPPING_SUGGESTED_PRODUCTS, MARKETPLACE_SHOPS, TEST}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>segment_use_cases</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"fields\">Fields</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><code>id</code></span></div><div class=\"_yb _yc\"><span>numeric string</span></div></td><td><p class=\"_yd\"></p><div><div><p>ID of a catalog</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>business</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/business/\">Business</a></div></td><td><p class=\"_yd\"></p><div><div><p>Business that owns a catalog</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>da_display_settings</code></span></div><div class=\"_yb _yc\"><a href=\"https://developers.facebook.com/docs/marketing-api/reference/product-catalog-image-settings/\">ProductCatalogImageSettings</a></div></td><td><p class=\"_yd\"></p><div><div><p>Image display  settings such as background cropping and padding of items in the  catalog for different Dynamic Ad formats</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>default_image_url</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The URL for the default image, which is used for products without images, or when the product image is temporarily unavailable. If a product image matches the default image,  this should be treated as if the image was not loaded</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>fallback_image_url</code></span></div><div class=\"_yb _yc\"><span>list&lt;string&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>The URL for the fallback image. This is used as   the image for auto-generated dynamic items</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>feed_count</code></span></div><div class=\"_yb _yc\"><span>int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>The total number of feeds used by a catalog</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_catalog_segment</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>Verify that you will create ads based on a catalog or catalog segment before you try to create Dynamic Ads. Call this field and determine value otherwise you may get and error when you try to create Dynamic Ads from catalog segments.</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>is_local_catalog</code></span></div><div class=\"_yb _yc\"><span>bool</span></div></td><td><p class=\"_yd\"></p><div><div><p>is_local_catalog</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb _yc\"><span>string</span></div></td><td><p class=\"_yd\"></p><div><div><p>The name of a catalog given by the creator</p>\n</div></div><p></p><div class=\"_2pic\"><a href=\"https://developers.facebook.com/docs/graph-api/using-graph-api/#fields\" target=\"blank\"><span class=\"_1vet\">Default</span></a></div></td></tr><tr><td><div class=\"_yc\"><span><code>product_count</code></span></div><div class=\"_yb _yc\"><span>int32</span></div></td><td><p class=\"_yd\"></p><div><div><p>The total number of products in a catalog</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><code>vertical</code></span></div><div class=\"_yb _yc\"><span>enum</span></div></td><td><p class=\"_yd\"></p><div><div><p>The type of catalog (for example: hotels, commerce, etc)</p>\n</div></div><p></p></td></tr></tbody></table></div></div><div><h3 id=\"edges\">Edges</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Edge</th><th>Description</th></tr></thead><tbody><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/businessmanager/\"><code>agencies</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Business&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Agencies that have access to a catalog</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/assigned_users/\"><code>assigned_users</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AssignedUser&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Users assigned to this catalog</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/automotive_models/\"><code>automotive_models</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;AutomotiveModel&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Automotive models that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/categories/\"><code>categories</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalogCategory&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Categories within the catalog for a given categorization criteria</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/check_batch_request_status/\"><code>check_batch_request_status</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;CheckBatchRequestStatus&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Checks the status of a batch request</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/collaborative_ads_share_settings/\"><code>collaborative_ads_share_settings</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;CollaborativeAdsShareSettings&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>All the collaborative ads share settings for a catalog segment</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/data_sources/\"><code>data_sources</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalogDataSource&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>data_sources updating catalog including feeds, session containers etc</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/destinations/\"><code>destinations</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Destination&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Destinations that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/diagnostics/\"><code>diagnostics</code></a></span><a class=\"_2pir\" href=\"#\" role=\"button\" data-hover=\"tooltip\" id=\"u_0_6_QC\"></a></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalogDiagnosticGroup&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>diagnostics</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/event_stats/\"><code>event_stats</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductEventStat&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Aggregated statistics on the matched and unmatchd events received from the pixels and apps associated to the catalog, broken down by DA event, source and device_type</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/dynamic-product-ads/product-audiences/\"><code>external_event_sources</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ExternalEventSource&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>External event sources (including pixels) for catalog events like ViewContent</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/flights/\"><code>flights</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Flight&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Flights that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/home_listings/\"><code>home_listings</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;HomeListing&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Home listings that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/hotel_rooms_batch/\"><code>hotel_rooms_batch</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalogHotelRoomsBatch&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Batch operations with hotel rooms</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/hotels/\"><code>hotels</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Hotel&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Hotels that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/pricing_variables_batch/\"><code>pricing_variables_batch</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalogPricingVariablesBatch&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Batch operations with hotel room prices</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/product_groups/\"><code>product_groups</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductGroup&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Product groups that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/product_sets/\"><code>product_sets</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductSet&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Product sets belonging to a catalog</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/product_sets_batch/\"><code>product_sets_batch</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductCatalogProductSetsBatch&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Batch operations with product sets</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/products/\"><code>products</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;ProductItem&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Products that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/vehicle_offers/\"><code>vehicle_offers</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;VehicleOffer&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Vehicle offers that a catalog contains</p>\n</div></div><p></p></td></tr><tr><td><div class=\"_yc\"><span><a href=\"/docs/marketing-api/reference/product-catalog/vehicles/\"><code>vehicles</code></a></span></div><div class=\"_yb _yc\"><span>Edge&lt;Vehicle&gt;</span></div></td><td><p class=\"_yd\"></p><div><div><p>Vehicles that a catalog contains</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>80009</td><td>There have been too many calls to this Catalog account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting.</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Creating\">Creating</h2><div><h3 id=\"create_example\">Examples</h3><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'name=Catalog'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;API_VERSION&gt;/&lt;BUSINESS_ID&gt;/product_catalogs</span></pre><p></p></div><div class=\"_844_\"><div class=\"_3-98\">You can make a POST request to <code>items_batch</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/product-catalog/items_batch/\"><code>/{product_catalog_id}/items_batch</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> will be created.</div><div><h3 id=\"parameters-2\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_7_yd\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>allow_upsert</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>true</code></div><p class=\"_yd\"></p><div><div><p>Parameters specifying whether non existing items that are being updated should be inserted or should throw the error</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>item_sub_type</code></span></div><div class=\"_yb\">enum {APPLIANCES, BABY_FEEDING, BABY_TRANSPORT, BEAUTY, BEDDING, CAMERAS, CELL_PHONES_AND_SMART_WATCHES, CLEANING_SUPPLIES, CLOTHING, CLOTHING_ACCESSORIES, COMPUTERS_AND_TABLETS, DIAPERING_AND_POTTY_TRAINING, ELECTRONICS_ACCESSORIES, FURNITURE, HEALTH, HOME_GOODS, JEWELRY, NURSERY, PRINTERS_AND_SCANNERS, PROJECTORS, SHOES_AND_FOOTWEAR, SOFTWARE, TOYS, TVS_AND_MONITORS, VIDEO_GAME_CONSOLES_AND_VIDEO_GAMES, WATCHES}</div></td><td><div>Default value: <code>\"EMPTY\"</code></div><p class=\"_yd\"></p><div><div><p>The sub vertical type of items in the request</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>item_type</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The type of items in the request</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>requests</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Array of JSON objects containing batch requests. Each batch request consists of <code>method</code> and <code>data</code> fields</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3 id=\"return-type\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>handles</code>:  List  [<div class=\"_uoj\">string</div>], </div><div class=\"_uoj\"><code>validation_status</code>:  List  [<div class=\"_uoj\"> Struct  {<div class=\"_uoj\"><code>errors</code>:  List  [<div class=\"_uoj\"> Struct  {<div class=\"_uoj\"><code>message</code>: string, </div>}</div>], </div><div class=\"_uoj\"><code>retailer_id</code>: string, </div><div class=\"_uoj\"><code>warnings</code>:  List  [<div class=\"_uoj\"> Struct  {<div class=\"_uoj\"><code>message</code>: string, </div>}</div>], </div>}</div>], </div>}</div><h3 id=\"error-codes-2\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>80014</td><td>There have been too many calls for the batch uploads to this catalog account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#catalog.</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can make a POST request to <code>assigned_users</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/product-catalog/assigned_users/\"><code>/{product_catalog_id}/assigned_users</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> will be created.</div><div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_8_np\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>tasks</code></span></div><div class=\"_yb\">array&lt;enum {MANAGE, ADVERTISE, MANAGE_AR, AA_ANALYZE}&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>Catalog permission tasks to assign this user</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1 _5m29\"><td><div class=\"_yc\"><span><code>user</code></span></div><div class=\"_yb\">UID</div></td><td><p class=\"_yd\"></p><div><div><p>Business user id or system user id</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>200</td><td>Permissions error</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can make a POST request to <code>vehicles</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/product-catalog/vehicles/\"><code>/{product_catalog_id}/vehicles</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> will be created.</div><div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_9_o1\"><tr class=\"row_0 _5m27\"><td><div class=\"_yc\"><span><code>address</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>address</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>applinks</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>applinks</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>availability</code></span></div><div class=\"_yb\">enum {AVAILABLE, NOT_AVAILABLE, PENDING, UNKNOWN}</div></td><td><p class=\"_yd\"></p><div><div><p>availability</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29\"><td><div class=\"_yc\"><span><code>body_style</code></span></div><div class=\"_yb\">enum {CONVERTIBLE, COUPE, CROSSOVER, ESTATE, GRANDTOURER, HATCHBACK, MINIBUS, MINIVAN, MPV, PICKUP, ROADSTER, SALOON, SEDAN, SMALL_CAR, SPORTSCAR, SUPERCAR, SUPERMINI, SUV, TRUCK, VAN, WAGON, OTHER, NONE}</div></td><td><p class=\"_yd\"></p><div><div><p>body_style</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>condition</code></span></div><div class=\"_yb\">enum {EXCELLENT, VERY_GOOD, GOOD, FAIR, POOR, OTHER, NONE}</div></td><td><p class=\"_yd\"></p><div><div><p>condition</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29\"><td><div class=\"_yc\"><span><code>currency</code></span></div><div class=\"_yb\">ISO 4217 Currency Code</div></td><td><p class=\"_yd\"></p><div><div><p>currency</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>date_first_on_lot</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>date_first_on_lot</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>dealer_id</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>dealer_id</p>\n</div></div><p></p></td></tr><tr class=\"row_8\"><td><div class=\"_yc\"><span><code>dealer_name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>dealer_name</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29\"><td><div class=\"_yc\"><span><code>dealer_phone</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>dealer_phone</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>description</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>description</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_11 _5m29\"><td><div class=\"_yc\"><span><code>drivetrain</code></span></div><div class=\"_yb\">enum {TWO_WD, FOUR_WD, AWD, FWD, RWD, OTHER, NONE}</div></td><td><p class=\"_yd\"></p><div><div><p>drivetrain</p>\n</div></div><p></p></td></tr><tr class=\"row_12\"><td><div class=\"_yc\"><span><code>exterior_color</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>exterior_color</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_13 _5m29\"><td><div class=\"_yc\"><span><code>fb_page_id</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>fb_page_id</p>\n</div></div><p></p></td></tr><tr class=\"row_14\"><td><div class=\"_yc\"><span><code>fuel_type</code></span></div><div class=\"_yb\">enum {DIESEL, ELECTRIC, GASOLINE, FLEX, HYBRID, OTHER, PETROL, PLUGIN_HYBRID, NONE}</div></td><td><p class=\"_yd\"></p><div><div><p>fuel_type</p>\n</div></div><p></p></td></tr><tr class=\"row_15 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>images</code></span></div><div class=\"_yb\">list&lt;Object&gt;</div></td><td><p class=\"_yd\"></p><div><div><p>images</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_16\"><td><div class=\"_yc\"><span><code>interior_color</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>interior_color</p>\n</div></div><p></p></td></tr><tr class=\"row_17 _5m29\"><td><div class=\"_yc\"><span><code>make</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>make</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_18 _5m27\"><td><div class=\"_yc\"><span><code>mileage</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>mileage</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_19 _5m29\"><td><div class=\"_yc\"><span><code>model</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>model</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_20\"><td><div class=\"_yc\"><span><code>price</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>price</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_21 _5m29\"><td><div class=\"_yc\"><span><code>state_of_vehicle</code></span></div><div class=\"_yb\">enum {NEW, USED, CPO}</div></td><td><p class=\"_yd\"></p><div><div><p>state_of_vehicle</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_22\"><td><div class=\"_yc\"><span><code>title</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>title</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_23 _5m29\"><td><div class=\"_yc\"><span><code>transmission</code></span></div><div class=\"_yb\">enum {AUTOMATIC, MANUAL, OTHER, NONE}</div></td><td><p class=\"_yd\"></p><div><div><p>transmission</p>\n</div></div><p></p></td></tr><tr class=\"row_24\"><td><div class=\"_yc\"><span><code>trim</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>trim</p>\n</div></div><p></p></td></tr><tr class=\"row_25 _5m29\"><td><div class=\"_yc\"><span><code>url</code></span></div><div class=\"_yb\">URI</div></td><td><p class=\"_yd\"></p><div><div><p>url</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_26\"><td><div class=\"_yc\"><span><code>vehicle_id</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>vehicle_id</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_27 _5m29\"><td><div class=\"_yc\"><span><code>vehicle_type</code></span></div><div class=\"_yb\">enum {BOAT, CAR_TRUCK, COMMERCIAL, MOTORCYCLE, OTHER, POWERSPORT, RV_CAMPER, TRAILER}</div></td><td><p class=\"_yd\"></p><div><div><p>vehicle_type</p>\n</div></div><p></p></td></tr><tr class=\"row_28\"><td><div class=\"_yc\"><span><code>vin</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>vin</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_29 _5m29\"><td><div class=\"_yc\"><span><code>year</code></span></div><div class=\"_yb\">int64</div></td><td><p class=\"_yd\"></p><div><div><p>year</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>10800</td><td>Duplicate retailer_id when attempting to create a store collection</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can make a POST request to <code>owned_product_catalogs</code> edge from the following paths: <ul><li><a href=\"/docs/marketing-api/reference/business/owned_product_catalogs/\"><code>/{business_id}/owned_product_catalogs</code></a></li></ul><div>When posting to this edge, a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> will be created.</div><div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_a_Th\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>additional_vertical_option</code></span></div><div class=\"_yb\">enum {LOCAL_DA_CATALOG, LOCAL_PRODUCTS}</div></td><td><p class=\"_yd\"></p><div><div><p>Additional catalog configurations that does not introduce either new verticals or subverticals</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>business_metadata</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>business_metadata</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>catalog_segment_filter</code></span></div><div class=\"_yb\">A JSON-encoded rule</div></td><td><p class=\"_yd\"></p><div><div><p>Provide filter for catalog to create a catalog segment.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>da_display_settings</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>Dynamic Ads display settings.</p>\n</div></div><p></p></td></tr><tr class=\"row_4 _5m27\"><td><div class=\"_yc\"><span><code>destination_catalog_settings</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Destination catalog settings.</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>flight_catalog_settings</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Flight catalog settings.</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">UTF-8 encoded string</div></td><td><p class=\"_yd\"></p><div><div><p>Name of the catalog.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_7 _5m29\"><td><div class=\"_yc\"><span><code>parent_catalog_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>Parent catalog ID.</p>\n</div></div><p></p></td></tr><tr class=\"row_8 _5m27\"><td><div class=\"_yc\"><span><code>partner_integration</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Partner integration settings</p>\n</div></div><p></p></td></tr><tr class=\"row_9 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>store_catalog_settings</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Store catalog settings.</p>\n</div></div><p></p></td></tr><tr class=\"row_10\"><td><div class=\"_yc\"><span><code>vertical</code></span></div><div class=\"_yb\">enum {adoptable_pets, commerce, destinations, flights, generic, home_listings, hotels, local_service_businesses, offer_items, offline_commerce, transactable_items, vehicles}</div></td><td><div>Default value: <code>commerce</code></div><p class=\"_yd\"></p><div><div><p>The catalog's industry or vertical, such as <code>commerce</code>.</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3>Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node represented by <code>id</code> in the return type.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>id</code>: numeric string, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>102</td><td>Session key invalid or no longer valid</td></tr><tr><td>2310068</td><td>Cannot create a catalog segment from a non CPAS compliant parent catalog</td></tr><tr><td>415</td><td>Two factor authentication required. User have to enter a code from SMS or TOTP code generator to pass 2fac. This could happen when accessing a 2fac-protected asset like a page that is owned by a 2fac-protected business manager.</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Updating\">Updating</h2><div class=\"_844_\"><div class=\"_3-98\">You can update a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> by making a POST request to <a href=\"/docs/marketing-api/reference/product-catalog/\"><code>/{product_catalog_id}</code></a>.<div><h3 id=\"parameters-3\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_b_XS\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>additional_vertical_option</code></span></div><div class=\"_yb\">enum {LOCAL_DA_CATALOG, LOCAL_PRODUCTS}</div></td><td><p class=\"_yd\"></p><div><div><p>Additional catalog configurations that does not introduce either new verticals or subverticals</p>\n</div></div><p></p></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>da_display_settings</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>The display settings object when used\n                                            will determine which image transformations (like cropping or padding)\n                                            will be applied for the item in the specified dynamic ad format.</p>\n</div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>default_image_url</code></span></div><div class=\"_yb\">URI</div></td><td><p class=\"_yd\"></p><div><div><p>The URL for the default image, which is used for\n                                              products without images or for the cases when the product image is\n                                              temproarily unavailable. If a product image matches the default image,\n                                              this should be treated as if the image was not loaded.</p>\n</div></div><p></p></td></tr><tr class=\"row_3 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>destination_catalog_settings</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Catalog setting for destination\n                                            catalogs.</p>\n</div></div><p></p></td></tr><tr class=\"row_4\"><td><div class=\"_yc\"><span><code>fallback_image_url</code></span></div><div class=\"_yb\">URI</div></td><td><p class=\"_yd\"></p><div><div><p>The URL for the fallback image. This is used as\n                                              the image for the auto-generated dynamic items.</p>\n</div></div><p></p></td></tr><tr class=\"row_5 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>flight_catalog_settings</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Catalog setting for flight catalogs.</p>\n</div></div><p></p></td></tr><tr class=\"row_6\"><td><div class=\"_yc\"><span><code>name</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>Name of the Product Catalog</p>\n</div></div><p></p></td></tr><tr class=\"row_7 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>partner_integration</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Partner integration settings</p>\n</div></div><p></p></td></tr><tr class=\"row_8 _5m27\"><td><div class=\"_yc\"><span><code>store_catalog_settings</code></span></div><div class=\"_yb\">JSON object</div></td><td><p class=\"_yd\"></p><div><div><p>Catalog settings for store catalogs; the page with the location structure for all the stores within this settings will be used to validate the store number for feed uploading</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type-2\">Return Type</h3><div>This endpoint supports <a href=\"/docs/graph-api/advanced/#read-after-write\">read-after-write</a> and will read the node to which you POSTed.</div><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-3\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>\n\n<h2 id=\"Deleting\">Deleting</h2><div><h3 id=\"delete_example\">Examples</h3><p>You can delete a product catalog with the following API. You cannot remove a product catalog from a Business Manager, or transfer a catalog from one Business Manager to another.\n    </p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X DELETE \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;API_VERSION&gt;/&lt;PRODUCT_CATALOG_ID&gt;</span></pre><p></p><p><br>\nTo do this in a batch call, specify the parameter in the query string for the URL:</p><p></p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'batch=[{\n  \"method\":\"DELETE\",\n  \"relative_url\":\"&lt;PRODUCT_CATALOG_ID&gt;\"\n}]'</span><span class=\"pln\"> \\\n </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com</span></pre><p></p><p><br>\nTo remove the association between the catalog and a pixel or app, make an <code>HTTP DELETE</code>:\n    </p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">X DELETE \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'external_event_sources=[&lt;APP_ID&gt;,&lt;PIXEL_ID&gt;]'</span><span class=\"pln\"> \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">F </span><span class=\"str\">'access_token=&lt;TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;API_VERSION&gt;/&lt;PRODUCT_CATALOG_ID&gt;/external_event_sources</span></pre><p></p><p><br>\nTo see all pixel and apps associated with a product catalog, make an <code>HTTP GET</code>:\n    </p><pre class=\"_5s-8 prettyprint lang-code prettyprinted\" style=\"\"><span class=\"pln\">curl </span><span class=\"pun\">-</span><span class=\"pln\">G \\\n  </span><span class=\"pun\">-</span><span class=\"pln\">d </span><span class=\"str\">'access_token=&lt;ACCESS_TOKEN&gt;'</span><span class=\"pln\"> \\\n  https</span><span class=\"pun\">:</span><span class=\"com\">//graph.facebook.com/&lt;API_VERSION&gt;/&lt;PRODUCT_CATALOG_ID&gt;/external_event_sources</span></pre><p></p></div><div class=\"_844_\"><div class=\"_3-98\">You can delete a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/product-catalog/\"><code>/{product_catalog_id}</code></a>.<div><h3 id=\"parameters-4\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_c_NV\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>allow_delete_catalog_with_live_product_set</code></span></div><div class=\"_yb\">boolean</div></td><td><div>Default value: <code>false</code></div><p class=\"_yd\"></p><div><div><p>If the catalog has live product sets, the deletion will be blocked by default. Set this parameter to true to enforce the deletion even if it has live product sets</p>\n</div></div><p></p></td></tr></tbody></table></div></div><h3 id=\"return-type-3\">Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3 id=\"error-codes-4\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>3970</td><td>You must be assigned as an admin of this product catalog before you can delete it.</td></tr><tr><td>801</td><td>Invalid operation</td></tr><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div><div class=\"_4g10\"></div><div class=\"_3-98\">You can dissociate a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> from a&nbsp;<a href=\"/docs/marketing-api/reference/product-catalog/\">ProductCatalog</a> by making a DELETE request to <a href=\"/docs/marketing-api/reference/product-catalog/assigned_users/\"><code>/{product_catalog_id}/assigned_users</code></a>.<div><h3>Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_d_59\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>user</code></span></div><div class=\"_yb\">UID</div></td><td><p class=\"_yd\"></p><div><div><p>Business user id or system user id</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><h3>Return Type</h3><div class=\"_367u\"> Struct  {<div class=\"_uoj\"><code>success</code>: bool, </div>}</div><h3>Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr></tbody></table></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog/assigned_users/", "/docs/marketing-api/reference/product-catalog/automotive_models/", "/docs/marketing-api/reference/product-catalog/batch/", "/docs/marketing-api/reference/product-catalog/catalog_store/", "/docs/marketing-api/reference/product-catalog/categories/", "/docs/marketing-api/reference/product-catalog/check_batch_request_status/", "/docs/marketing-api/reference/product-catalog/check_marketplace_partner_sellers_status/", "/docs/marketing-api/reference/product-catalog/collaborative_ads_share_settings/", "/docs/marketing-api/reference/product-catalog/data_sources/", "/docs/marketing-api/reference/product-catalog/destinations/", "/docs/marketing-api/reference/product-catalog/diagnostics/", "/docs/marketing-api/reference/product-catalog/event_stats/", "/docs/marketing-api/reference/product-catalog/flights/", "/docs/marketing-api/reference/product-catalog/home_listings/", "/docs/marketing-api/reference/product-catalog/hotel_rooms_batch/", "/docs/marketing-api/reference/product-catalog/hotels/", "/docs/marketing-api/reference/product-catalog/items_batch/", "/docs/marketing-api/reference/product-catalog/localized_items_batch/", "/docs/marketing-api/reference/product-catalog/marketplace_partner_signals/", "/docs/marketing-api/reference/product-catalog/pricing_variables_batch/", "/docs/marketing-api/reference/product-catalog/product_feeds/", "/docs/marketing-api/reference/product-catalog/product_groups/", "/docs/marketing-api/reference/product-catalog/product_sets/", "/docs/marketing-api/reference/product-catalog/product_sets_batch/", "/docs/marketing-api/reference/product-catalog/products/", "/docs/marketing-api/reference/product-catalog/vehicle_offers/", "/docs/marketing-api/reference/product-catalog/vehicles/", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/dynamic-ad", "/docs/marketing-api/access#limits", "/docs/marketing-api/businessmanager/", "/docs/marketing-api/dynamic-product-ads/product-audiences/", "/docs/marketing-api/reference/product-catalog/", "/docs/marketing-api/reference/business/owned_product_catalogs/"], "url": "https://developers.facebook.com/docs/marketing-api/reference/product-catalog", "timestamp": "2025-06-25T15:46:51.347Z"}