# Facebook Marketing API - Ad Image Reference

## Summary
Complete reference for uploading, managing, and using images in Facebook ad creatives through the Marketing API. Covers image upload methods, field specifications, and integration with ad creative workflows.

## Key Points
- Images must be uploaded before use in ad creatives and are referenced by unique hash values
- Supports multiple upload methods: direct file upload, zip files, bytes, and copying between accounts
- Images can only be deleted if not currently used in any ad creative
- Temporary URLs are provided but should not be used in ad creative creation
- Filename extensions are required and maximum filename length is 100 characters

## API Endpoints
- `/act_{ad_account_id}/adimages`
- `/act_{ad_account_id}/ads`

## Parameters
- bytes
- copy_from
- hash
- hashes
- source_account_id
- image_file
- filename

## Content
# Facebook Marketing API - Ad Image Reference

## Overview

Upload and manage images to later use in ad creative. Image formats, sizes and design guidelines depend on your type of ad. See [Ads Guide](https://www.facebook.com/business/ads-guide/) and [Image Crop](/docs/marketing-api/image-crops/) for more details.

Supported image formats include `.bmp`, `.jpeg`, and `.gif`.

## Basic Usage

### Uploading an Image

```php
use FacebookAds\Object\AdImage;
use FacebookAds\Object\Fields\AdImageFields;

$image = new AdImage(null, 'act_<AD_ACCOUNT_ID>');
$image->{AdImageFields::FILENAME} = '<IMAGE_PATH>';

$image->create();
echo 'Image Hash: '.$image->{AdImageFields::HASH}.PHP_EOL;
```

### Using Image in Ad Creative

```php
use FacebookAds\Object\AdCreative;
use FacebookAds\Object\Ad;
use FacebookAds\Object\Fields\AdCreativeFields;
use FacebookAds\Object\Fields\AdFields;

$creative = new AdCreative(null, 'act_<AD_ACCOUNT_ID>');
$creative->setData(array(
  AdCreativeFields::TITLE => 'My Test Creative',
  AdCreativeFields::BODY => 'My Test Ad Creative Body',
  AdCreativeFields::OBJECT_URL => 'https://www.facebook.com/facebook',
  AdCreativeFields::IMAGE_HASH => '<IMAGE_HASH>',
));

$ad = new Ad(null, 'act_<AD_ACCOUNT_ID>');
$ad->setData(array(
  AdFields::NAME => 'My Ad',
  AdFields::ADSET_ID => <AD_SET_ID>,
  AdFields::CREATIVE => $creative,
));
$ad->create(array(
  Ad::STATUS_PARAM_NAME => Ad::STATUS_PAUSED,
));
```

## Reading Images

Images can be specified in ad creatives by:
- Image hash value of a previously uploaded image
- Uploading the image at ad or ad creative creation time

### Get Images by Ad Account

```php
use FacebookAds\Object\AdAccount;

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$images = $account->getAdImages();
```

### Get Specific Images by Hash

```php
use FacebookAds\Object\AdAccount;

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$images = $account->getAdImages(
  array(),
  array(
    'hashes' => array(
      '<IMAGE_1_HASH>',
      '<IMAGE_2_HASH>',
    ),
  ));
```

## Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | token | The ID of the image |
| `account_id` | numeric string | The ad account that owns the image |
| `created_time` | datetime | Time the image was created |
| `creatives` | list<numeric string> | List of ad creative IDs using this image |
| `hash` | string | Unique identifier hash for the image (Default field) |
| `height` | unsigned int32 | Height of the image |
| `is_associated_creatives_in_adgroups` | bool | Self explanatory |
| `name` | string | Filename (max 100 characters) |
| `original_height` | unsigned int32 | Original upload height |
| `original_width` | unsigned int32 | Original upload width |
| `permalink_url` | string | Permanent URL for story creatives |
| `status` | enum | Status: ACTIVE, INTERNAL, DELETED |
| `updated_time` | datetime | Time the image was updated |
| `url` | string | Temporary URL (do not use in ad creative creation) |
| `url_128` | string | Temporary URL for 128x128 resized version |
| `width` | unsigned int32 | Width of the image |

## Creating Images

### Upload from Zip File

```php
use FacebookAds\Object\AdImage;
use FacebookAds\Object\Fields\AdImageFields;

$images = AdImage::createFromZip('<ZIP_PATH>', 'act_<AD_ACCOUNT_ID>');

foreach ($images as $image) {
  echo $image->{AdImageFields::HASH}.PHP_EOL;
}
```

### Upload from Bytes

```bash
curl \
  -F 'bytes=iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fI...' \
  -F 'access_token=<ACCESS_TOKEN>' \
"https://graph.facebook.com/<API_VERSION>/act_<ACCOUNT_ID>/adimages"
```

### Upload Image on Ad Create

```bash
curl \
  -F 'campaign_id=<AD_SET_ID>' \
  -F 'creative={"title":"test title","body":"test","object_url":"http://www.test.com","image_file":"test.jpg"}' \
  -F 'test.jpg=@test.jpg' \
  -F 'name=My ad' \
  -F 'access_token=<ACCESS_TOKEN>' \
"https://graph.facebook.com/<API_VERSION>/act_<ACCOUNT_ID>/ads"
```

### Copy Images Between Accounts

```bash
curl \
  -F 'copy_from={"source_account_id":"<SOURCE_ACCOUNT_ID>", "hash":"02bee5277ec507b6fd0f9b9ff2f22d9c"}' \
  -F 'access_token=<ACCESS_TOKEN>' \
"https://graph.facebook.com/<API_VERSION>/act_<DESTINATION_ACCOUNT_ID>/adimages"
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `bytes` | Base64 UTF-8 string | Image file content in bytes format |
| `copy_from` | JSON object | Copy image from source account: `{"source_account_id":"ID", "hash":"hash"}` |

### Return Type

Returns a map structure with image details including hash, URLs, dimensions, and name.

## Deleting Images

You can only delete ad images **not currently being used** in an ad creative.

```php
use FacebookAds\Object\AdImage;
use FacebookAds\Object\Fields\AdImageFields;

$image = new AdImage(<IMAGE_ID>, 'act_<AD_ACCOUNT_ID>');
$image->{AdImageFields::HASH} = '<IMAGE_HASH>';
$image->deleteSelf();
```

### Delete Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `hash` | string | Hash of the image to delete (Required) |

## Error Codes

| Error | Description |
|-------|-------------|
| 100 | Invalid parameter |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | Action deemed abusive or disallowed |
| 2635 | Deprecated API version |
| 80004 | Rate limit exceeded |

## Notes

- Images must include filename extensions (e.g., `sample.jpg`, not `sample`)
- Temporary URLs should not be used in ad creative creation
- Your app must have read access to source account when copying images
- Images cannot be updated once uploaded
- Maximum filename length is 100 characters

## Examples
PHP image upload with hash retrieval

Using image hash in ad creative creation

Reading images by ad account

Reading specific images by hash

Uploading from zip file

Uploading via bytes with cURL

Copying images between accounts

Deleting unused images

---
**Tags:** facebook-marketing-api, ad-images, image-upload, ad-creative, image-management, api-reference
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-image
**Processed:** 2025-06-25T15:42:35.137Z