{"title": "Facebook Marketing API - Ad Account Asyncadcreatives Reference", "summary": "Documentation for the Ad Account Asyncadcreatives endpoint in Facebook Marketing API v23.0. This endpoint allows creating asynchronous ad creative jobs but does not support reading, updating, or deleting operations.", "content": "# Ad Account Asyncadcreatives\n\n## Overview\n\nThe Ad Account Asyncadcreatives endpoint is part of the Facebook Marketing API v23.0 that handles asynchronous ad creative operations.\n\n## Supported Operations\n\n### Reading\nReading operations are not supported on this endpoint.\n\n### Creating\nYou can make a POST request to the `asyncadcreatives` edge from the following path:\n- `/act_{ad_account_id}/asyncadcreatives`\n\nWhen posting to this edge, no Graph object will be created.\n\n#### Parameters\n\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| `creative_spec` | AdCreative | Specs for ad creative | Yes (Supports Emoji) |\n| `name` | UTF-8 encoded string | Name of async job | Yes |\n| `notification_mode` | enum{OFF, ON_COMPLETE} | Specify `0` for no notifications and `1` for notification on completion | No |\n| `notification_uri` | URL | If notifications are enabled, specify the URL to send them | No |\n\n#### Return Type\nThis endpoint supports read-after-write and will read the node represented by `id` in the return type.\n\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n#### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n\n### Updating\nUpdating operations are not supported on this endpoint.\n\n### Deleting\nDeleting operations are not supported on this endpoint.", "keyPoints": ["Only POST (creating) operations are supported on this endpoint", "No Graph object is created when posting to this edge", "Supports read-after-write functionality", "Requires creative_spec and name parameters", "Optional notification system with completion callbacks"], "apiEndpoints": ["/act_{ad_account_id}/asyncadcreatives"], "parameters": ["creative_spec", "name", "notification_mode", "notification_uri"], "examples": [], "tags": ["Facebook Marketing API", "Ad Account", "Async Ad Creatives", "POST endpoint", "v23.0"], "relatedTopics": ["AdCreative", "Ad Account", "Graph API", "Asynchronous operations", "Read-after-write"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/asyncadcreatives/", "processedAt": "2025-06-25T16:24:55.784Z", "processor": "openrouter-claude-sonnet-4"}