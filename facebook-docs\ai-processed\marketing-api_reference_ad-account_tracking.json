{"title": "Facebook Marketing API - Ad Account Tracking Reference", "summary": "Complete reference documentation for the Ad Account Tracking endpoint in Facebook's Marketing API. Covers reading tracking data from ad accounts and creating new tracking specifications at the account level.", "content": "# Ad Account Tracking\n\n## Overview\n\nThe Ad Account Tracking endpoint allows you to manage tracking specifications for Facebook ad accounts. This endpoint supports reading existing tracking data and creating new tracking specifications.\n\n**Graph API Version:** v23.0\n\n## Reading Tracking Data\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/tracking\n```\n\n### Parameters\nThis endpoint doesn't require any parameters.\n\n### Response Format\nReading from this edge returns a JSON formatted result:\n\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Response Fields\n- **`data`**: A list of AdAccountTrackingData nodes\n- **`paging`**: Pagination information (see [Graph API guide](https://developers.facebook.com/docs/graph-api/using-graph-api/#paging) for details)\n\n### Example Request\n```http\nGET /v23.0/{ad-account-id}/tracking HTTP/1.1\nHost: graph.facebook.com\n```\n\n## Creating Tracking Specifications\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/tracking\n```\n\nWhen posting to this edge, no Graph object will be created.\n\n### Parameters\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| `tracking_specs` | Object | Tracking specs to add to the account level | Yes |\n\n### Return Type\nThis endpoint supports [read-after-write](https://developers.facebook.com/docs/graph-api/advanced/#read-after-write) and will read the node to which you POSTed.\n\n```json\n{\n  \"success\": bool\n}\n```\n\n## Limitations\n- **Updating**: Not supported on this endpoint\n- **Deleting**: Not supported on this endpoint\n\n## Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 80004 | Too many calls to this ad-account. Wait and try again. See [rate limiting documentation](https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management) |\n\n## Additional Resources\n- [Using Graph API guide](https://developers.facebook.com/docs/graph-api/using-graph-api/)\n- [Graph API Explorer](https://developers.facebook.com/tools/explorer/?method=GET&path=%7Bad-account-id%7D%2Ftracking&version=v23.0)", "keyPoints": ["Supports reading existing tracking data and creating new tracking specifications", "Reading endpoint requires no parameters and returns AdAccountTrackingData nodes", "Creating requires tracking_specs parameter and supports read-after-write", "Update and delete operations are not supported on this endpoint", "Subject to rate limiting with specific error code 80004 for too many calls"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/tracking", "POST /act_{ad_account_id}/tracking"], "parameters": ["tracking_specs (Object, required for POST)", "ad-account-id (path parameter)", "data (response field)", "paging (response field)", "success (return field)"], "examples": ["GET /v23.0/{ad-account-id}/tracking HTTP/1.1", "Response format: {\"data\": [], \"paging\": {}}", "Return type: {\"success\": bool}"], "tags": ["Facebook Marketing API", "Ad Account", "Tracking", "Graph API", "API Reference", "v23.0"], "relatedTopics": ["Graph API usage", "AdAccountTrackingData", "OAuth 2.0 Access Tokens", "Rate limiting", "Read-after-write", "Pagination", "Error handling"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/tracking/", "processedAt": "2025-06-25T15:40:59.137Z", "processor": "openrouter-claude-sonnet-4"}