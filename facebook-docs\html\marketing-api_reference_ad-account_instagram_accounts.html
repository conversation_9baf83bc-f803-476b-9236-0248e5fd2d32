<div class="_1dyy" id="u_0_k_Xk"><div class="_33zv _3e1u"><div class="_33zw" tabindex="0" role="button">On This Page<div><i class="img sp_zD_MvjcHflF sx_a3c10c" alt="" data-visualcompletion="css-img"></i><i class="hidden_elem img sp_zD_MvjcHflF sx_6874ff" alt="" data-visualcompletion="css-img"></i></div></div><div class="_5-24 hidden_elem"><a href="#overview">Ad Account Instagram Accounts</a></div><div class="_5-24 hidden_elem"><a href="#Reading">Reading</a></div><div class="_5-24 hidden_elem"><a href="#example">Example</a></div><div class="_5-24 hidden_elem"><a href="#parameters">Parameters</a></div><div class="_5-24 hidden_elem"><a href="#fields">Fields</a></div><div class="_5-24 hidden_elem"><a href="#error-codes">Error Codes</a></div><div class="_5-24 hidden_elem"><a href="#Creating">Creating</a></div><div class="_5-24 hidden_elem"><a href="#Updating">Updating</a></div><div class="_5-24 hidden_elem"><a href="#Deleting">Deleting</a></div></div></div><div id="documentation_body_pagelet" data-referrer="documentation_body_pagelet"><div class="_34yh" id="u_0_1_YG"><div data-click-area="main"><div class="_4-u2 _57mb _1u44 _4-u8"><div class="_4-u3 _5rva _mog"><div class="clearfix"><span class="lfloat _ohe _c24 _50f4 _50f7"><span><span class="_2iem">Graph API Version</span></span></span><div class="_5s5u rfloat _ohf"><span><div class="_6a _6b"><div class="_6a _6b uiPopover" id="u_0_2_ae"><a role="button" class="_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy" href="#" style="max-width:200px;" aria-haspopup="true" aria-expanded="false" rel="toggle" id="u_0_3_Fi"><span class="_55pe">v23.0</span><span class="_4o_3 _3-99"><i class="img sp_WbXBGqjC54o sx_514a5c"></i></span></a></div><input type="hidden" autocomplete="off" name="" id="u_0_4_S7"></div></span></div></div></div></div><div class="_1xb4 _3-98"><div class="_4-u2 _57mb _1u44 _4-u8 _3la3"><div class="_4-u3 _588p"><h1 id="overview">Ad Account Instagram Accounts</h1><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Reading">Reading</h2><div class="_844_"><div><div><p>Retrieve instagram accounts associated with this Ad Account</p>
</div><div><h3 id="example">Example</h3><div class="_5z09"><div class="_51xa _5gt2 _51xb" id="u_0_5_1e"><button value="1" class="_42ft _51tl selected _42fs" type="submit" id="u_0_6_Wa">HTTP</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_7_Yo">PHP SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_8_G+">JavaScript SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_9_EL">Android SDK</button><button value="1" class="_42ft _51tl selected" type="submit" id="u_0_a_vi">iOS SDK</button><a role="button" class="_42ft _51tl selected" href="/tools/explorer/?method=GET&amp;path=%7Bad-account-id%7D%2Finstagram_accounts&amp;version=v23.0" target="_blank">Graph API Explorer<i class="_3-99 img sp_c_epTrfICMy sx_7b2121"></i></a></div><div class="_xmu"><pre class="_5gt1 prettyprint prettyprinted" id="u_0_b_aV" style=""><code><span class="pln">GET </span><span class="pun">/</span><span class="pln">v23</span><span class="pun">.</span><span class="lit">0</span><span class="pun">/{</span><span class="pln">ad</span><span class="pun">-</span><span class="pln">account</span><span class="pun">-</span><span class="pln">id</span><span class="pun">}/</span><span class="pln">instagram_accounts HTTP</span><span class="pun">/</span><span class="lit">1.1</span><span class="pln">
</span><span class="typ">Host</span><span class="pun">:</span><span class="pln"> graph</span><span class="pun">.</span><span class="pln">facebook</span><span class="pun">.</span><span class="pln">com</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_c_9G" style=""><code><span class="com">/* PHP SDK v5.0.0 */</span><span class="pln">
</span><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">try</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  </span><span class="com">// Returns a `Facebook\FacebookResponse` object</span><span class="pln">
  $response </span><span class="pun">=</span><span class="pln"> $fb</span><span class="pun">-&gt;</span><span class="kwd">get</span><span class="pun">(</span><span class="pln">
    </span><span class="str">'/{ad-account-id}/instagram_accounts'</span><span class="pun">,</span><span class="pln">
    </span><span class="str">'{access-token}'</span><span class="pln">
  </span><span class="pun">);</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookResponseException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Graph returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln"> </span><span class="kwd">catch</span><span class="pun">(</span><span class="typ">Facebook</span><span class="pln">\Exceptions\FacebookSDKException $e</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
  echo </span><span class="str">'Facebook SDK returned an error: '</span><span class="pln"> </span><span class="pun">.</span><span class="pln"> $e</span><span class="pun">-&gt;</span><span class="pln">getMessage</span><span class="pun">();</span><span class="pln">
  </span><span class="kwd">exit</span><span class="pun">;</span><span class="pln">
</span><span class="pun">}</span><span class="pln">
$graphNode </span><span class="pun">=</span><span class="pln"> $response</span><span class="pun">-&gt;</span><span class="pln">getGraphNode</span><span class="pun">();</span><span class="pln">
</span><span class="com">/* handle the result */</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_d_in" style=""><code><span class="com">/* make the API call */</span><span class="pln">
FB</span><span class="pun">.</span><span class="pln">api</span><span class="pun">(</span><span class="pln">
    </span><span class="str">"/{ad-account-id}/instagram_accounts"</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">function</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
      </span><span class="kwd">if</span><span class="pln"> </span><span class="pun">(</span><span class="pln">response </span><span class="pun">&amp;&amp;</span><span class="pln"> </span><span class="pun">!</span><span class="pln">response</span><span class="pun">.</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="com">/* handle the result */</span><span class="pln">
      </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">);</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_e_g/" style=""><code><span class="com">/* make the API call */</span><span class="pln">
</span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">(</span><span class="pln">
    </span><span class="typ">AccessToken</span><span class="pun">.</span><span class="pln">getCurrentAccessToken</span><span class="pun">(),</span><span class="pln">
    </span><span class="str">"/{ad-account-id}/instagram_accounts"</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">null</span><span class="pun">,</span><span class="pln">
    </span><span class="typ">HttpMethod</span><span class="pun">.</span><span class="pln">GET</span><span class="pun">,</span><span class="pln">
    </span><span class="kwd">new</span><span class="pln"> </span><span class="typ">GraphRequest</span><span class="pun">.</span><span class="typ">Callback</span><span class="pun">()</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
        </span><span class="kwd">public</span><span class="pln"> </span><span class="kwd">void</span><span class="pln"> onCompleted</span><span class="pun">(</span><span class="typ">GraphResponse</span><span class="pln"> response</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
            </span><span class="com">/* handle the result */</span><span class="pln">
        </span><span class="pun">}</span><span class="pln">
    </span><span class="pun">}</span><span class="pln">
</span><span class="pun">).</span><span class="pln">executeAsync</span><span class="pun">();</span></code></pre><pre class="_5gt1 prettyprint prettyprinted hidden_elem" id="u_0_f_Bg" style=""><code><span class="com">/* make the API call */</span><span class="pln">
</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> </span><span class="pun">*</span><span class="pln">request </span><span class="pun">=</span><span class="pln"> </span><span class="pun">[[</span><span class="typ">FBSDKGraphRequest</span><span class="pln"> alloc</span><span class="pun">]</span><span class="pln">
                               initWithGraphPath</span><span class="pun">:@</span><span class="str">"/{ad-account-id}/instagram_accounts"</span><span class="pln">
                                      parameters</span><span class="pun">:</span><span class="kwd">params</span><span class="pln">
                                      </span><span class="typ">HTTPMethod</span><span class="pun">:@</span><span class="str">"GET"</span><span class="pun">];</span><span class="pln">
</span><span class="pun">[</span><span class="pln">request startWithCompletionHandler</span><span class="pun">:^(</span><span class="typ">FBSDKGraphRequestConnection</span><span class="pln"> </span><span class="pun">*</span><span class="pln">connection</span><span class="pun">,</span><span class="pln">
                                      id result</span><span class="pun">,</span><span class="pln">
                                      </span><span class="typ">NSError</span><span class="pln"> </span><span class="pun">*</span><span class="pln">error</span><span class="pun">)</span><span class="pln"> </span><span class="pun">{</span><span class="pln">
    </span><span class="com">// Handle the result</span><span class="pln">
</span><span class="pun">}];</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href="/docs/graph-api/using-graph-api/">Using Graph API guide</a>.</div><div><h3 id="parameters">Parameters</h3>This endpoint doesn't have any parameters.</div><div><h3 id="fields">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class="_3hux"><p>{
    "<code>data</code>": [],
    "<code>paging</code>": {},
    "<code>summary</code>": {}
}</p>
</pre><div class="_3-8o"><h4><code>data</code></h4>A list of <a target="_blank" href="/docs/graph-api/reference/shadow-ig-user/">IGUser</a> nodes.</div><div class="_3-8o"><h4><code>paging</code></h4>For more details about pagination, see the <a href="/docs/graph-api/using-graph-api/#paging">Graph API guide</a>.</div><div class="_3-8o"><h4><code>summary</code></h4><p>Aggregated information about the edge, such as counts. Specify the fields to fetch in the summary param (like <code>summary=total_count</code>).</p><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><div class="_yc"><span><code>total_count</code></span></div><div class="_yb _yc"><span>int32</span></div></td><td><p class="_yd"></p><div><p>Total number of objects on this edge</p>
</div><p></p><div class="_2pic"><a href="https://developers.facebook.com/docs/graph-api/using-graph-api/#fields" target="blank"><span class="_1vet">Default</span></a></div></td></tr></tbody></table></div></div></div><h3 id="error-codes">Error Codes</h3><div class="_57-c"><table class="_4-ss _5k9x"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>200</td><td>Permissions error</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>80002</td><td>There have been too many calls to this Instagram account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting.</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr></tbody></table></div></div></div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Creating">Creating</h2><div class="_844_">You can't perform this operation on this endpoint.</div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Updating">Updating</h2><div class="_844_">You can't perform this operation on this endpoint.</div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div><div class="_4-u2 _57mb _1u44 _2pig _4-u8 _3la3"><div class="_4-u3 _588p"><h2 id="Deleting">Deleting</h2><div class="_844_">You can't perform this operation on this endpoint.</div><div data-click-area="to_top_nav"><a class="_2k32" href="#"><i alt="" data-visualcompletion="css-img" class="img sp_zD_MvjcHflF sx_c72b6b"></i></a></div></div></div></div><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '675141479195042');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '574561515946252');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '1754628768090156');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1" /></noscript><script nonce="">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,'script','https://connect.facebook.net/en_US/fbevents.js');

fbq('init', '217404712025032');
fbq('track', "PageView");fbq('track', "PageView");</script><noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=217404712025032&amp;ev=PageView&amp;noscript=1" /></noscript></div></div></div>