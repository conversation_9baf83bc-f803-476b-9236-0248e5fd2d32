# 

On This Page

[](#overview)

[Reading](#Reading)

[Example](#example)

[Parameters](#parameters)

[Fields](#fields)

[Error Codes](#error-codes)

[Creating](#Creating)

[Limitations](#limitations)

[Example](#example-2)

[Parameters](#parameters-2)

[Return Type](#return-type)

[Error Codes](#error-codes-2)

[Updating](#Updating)

[Deleting](#Deleting)

Graph API Version

[v23.0](#)

# Ad Account Ad Creatives

[](#)

## Reading

The Ad Creatives that belong to this Ad Account.

  

Contains creative content for an ad account that you can use in your ads. Includes images, videos and so on. Using an ad account creative for a particular ad is subject to validation rules based on the ad type and other rules. See [Facebook Ads Guide](https://www.facebook.com/business/ads-guide?tab0=Mobile%20News%20Feed) and [Validation, Objectives and Creative](/docs/marketing-api/validation#objective_creative).

To retrieve an account's ad creatives, make an HTTP GET call to

PHP Business SDKPython Business SDKJava Business SDKRuby Business SDKcURL

```
`use FacebookAds\Object\AdAccount;
use FacebookAds\Object\Fields\AdCreativeFields;

$account = new AdAccount('act_<AD_ACCOUNT_ID>');
$adcreatives = $account->getAdCreatives(array(
  AdCreativeFields::NAME,
));`
```
```
`from facebookads.adobjects.adaccount import AdAccount
from facebookads.adobjects.adcreative import AdCreative

ad_account = AdAccount('act_<AD_ACCOUNT_ID>')
ad_account.get_ad_creatives(fields=[AdCreative.Field.object_story_id])`
```
```
`APINodeList<AdCreative> adCreatives = new AdAccount(act_<AD_ACCOUNT_ID>, context).getAdCreatives()
  .requestNameField()
  .execute();`
```
```
`ad_account = FacebookAds::AdAccount.get('act_<AD_ACCOUNT_ID>')
creatives = ad_account.adcreatives`
```
```
`curl -G \
  -d 'fields=name' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v2.11/act_<AD_ACCOUNT_ID>/adcreatives`
```

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=GET&path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives%3Ffields%3Dname&version=v23.0)

```
`GET /v23.0/act_<AD_ACCOUNT_ID>/adcreatives?fields=name HTTP/1.1
Host: graph.facebook.com`
```
```
`/* PHP SDK v5.0.0 */
/* make the API call */
try {
  // Returns a `Facebook\FacebookResponse` object
  $response = $fb->get(
    '/act_<AD_ACCOUNT_ID>/adcreatives?fields=name',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
/* handle the result */`
```
```
`/* make the API call */
FB.api(
    "/act_<AD_ACCOUNT_ID>/adcreatives",
    {
        "fields": "name"
    },
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);`
```
```
`Bundle params = new Bundle();
params.putString("fields", "name");
/* make the API call */
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/act_<AD_ACCOUNT_ID>/adcreatives",
    params,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();`
```
```
`NSDictionary *params = @{
  @"fields": @"name",
};
/* make the API call */
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/act_<AD_ACCOUNT_ID>/adcreatives"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];`
```
```
`curl -X GET -G \
  -d 'fields="name"' \
  -d 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

This endpoint doesn't have any parameters.

### Fields

Reading from this edge will return a JSON formatted result:

```


{
    "`data`": \[\],
    "`paging`": {},
    "`summary`": {}
}


```

#### `data`

A list of [AdCreative](/docs/marketing-api/reference/ad-creative/) nodes.

#### `paging`

For more details about pagination, see the [Graph API guide](/docs/graph-api/using-graph-api/#paging).

#### `summary`

Aggregated information about the edge, such as counts. Specify the fields to fetch in the summary param (like `summary=total_count`).

Field

Description

`total_count`

unsigned int32

Total number of creatives in the ad account.

### Error Codes

Error

Description

200

Permissions error

100

Invalid parameter

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

190

Invalid OAuth 2.0 Access Token

2500

Error parsing graph query

[](#)

## Creating

You can make a POST request to `adcreatives` edge from the following paths:

*   [`/act_{ad_account_id}/adcreatives`](/docs/marketing-api/reference/ad-account/adcreatives/)

When posting to this edge, no Graph object will be created.

### Limitations

*   When creating ad creatives, if the `object_story_id` being used is already in use by an existing creative, then the API will return the value of the existing creative\_id instead of creating a new one.
    
*   Using `radius` can cause an error, code: 100, subcode 1815946, when targeting multiple locations. We recommend creating an ad for each location or not using `radius` in your call.
    

### Example

HTTPPHP SDKJavaScript SDKAndroid SDKiOS SDKcURL[Graph API Explorer](/tools/explorer/?method=POST&path=act_%3CAD_ACCOUNT_ID%3E%2Fadcreatives%3Fname%3DSample%2BPromoted%2BPost%26object_story_id%3D%253CPAGE_ID%253E_%253CPOST_ID%253E&version=v23.0)

```
`POST /v23.0/act_<AD_ACCOUNT_ID>/adcreatives HTTP/1.1
Host: graph.facebook.com

name=Sample+Promoted+Post&object_story_id=%3CPAGE_ID%3E_%3CPOST_ID%3E`
```
```
`/* PHP SDK v5.0.0 */
/* make the API call */
try {
  // Returns a `Facebook\FacebookResponse` object
  $response = $fb->post(
    '/act_<AD_ACCOUNT_ID>/adcreatives',
    array (
      'name' => 'Sample Promoted Post',
      'object_story_id' => '<PAGE_ID>_<POST_ID>',
    ),
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
/* handle the result */`
```
```
`/* make the API call */
FB.api(
    "/act_<AD_ACCOUNT_ID>/adcreatives",
    "POST",
    {
        "name": "Sample Promoted Post",
        "object_story_id": "<PAGE_ID>_<POST_ID>"
    },
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);`
```
```
`Bundle params = new Bundle();
params.putString("name", "Sample Promoted Post");
params.putString("object_story_id", "<PAGE_ID>_<POST_ID>");
/* make the API call */
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/act_<AD_ACCOUNT_ID>/adcreatives",
    params,
    HttpMethod.POST,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();`
```
```
`NSDictionary *params = @{
  @"name": @"Sample Promoted Post",
  @"object_story_id": @"<PAGE_ID>_<POST_ID>",
};
/* make the API call */
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/act_<AD_ACCOUNT_ID>/adcreatives"
                                      parameters:params
                                      HTTPMethod:@"POST"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];`
```
```
`curl -X POST \
  -F 'name="Sample Promoted Post"' \
  -F 'object_story_id="<PAGE_ID>_<POST_ID>"' \
  -F 'access_token=<ACCESS_TOKEN>' \
  https://graph.facebook.com/v23.0/act_<AD_ACCOUNT_ID>/adcreatives`
```

If you want to learn how to use the Graph API, read our [Using Graph API guide](/docs/graph-api/using-graph-api/).

### Parameters

Parameter

Description

`actor_id`

int64

The actor ID (Page ID) of this creative.

`ad_disclaimer_spec`

JSON object

[Disclaimer information](/docs/graph-api/reference/ad-creative-ad-disclaimer/) to attach to your creative.

`title`

enum {HEALTH\_DISCLAIMER, IMPORTANT\_SAFETY\_INFORMATION, MEDICATION\_GUIDE, OFFER\_DETAILS, PRESCRIBING\_INFORMATION, TERMS\_AND\_CONDITIONS}

Required

`text`

string

`url`

URL

`adlabels`

list<Object>

[Ad Labels](/docs/marketing-api/reference/ad-label) associated with this creative. Used to group it with related ad objects.

`applink_treatment`

enum{automatic, deeplink\_with\_web\_fallback, deeplink\_with\_appstore\_fallback, web\_only}

Used for [Dynamic Ads](/docs/marketing-api/dynamic-product-ads/ads-management). Specify what action should occur if a person clicks a link in the ad, but the business' app is not installed on their device. For example, open a webpage displaying the product, or open the app in an app store on the person's mobile device.

`asset_feed_spec`

Object

Used for [Dynamic Creative](/docs/marketing-api/dynamic-creative/dynamic-creative-optimization) to automatically experiment and deliver different variations of an ad's creative. Specifies an asset feed with multiple images, text and other assets used to generate variations of an ad. Formatted as a JSON string.

Supports Emoji

`images`

list<Object>

`hash`

string

`url`

URL

`image_crops`

dictionary { enum{191x100, 100x72, 400x150, 600x360, 100x100, 400x500, 90x160} : <list<list<int64>>> }

`url_tags`

string

`adlabels`

list<Object>

`tag`

string

`videos`

list<Object>

`video_id`

int64

`thumbnail_id`

int64

`thumbnail_url`

URL

`thumbnail_hash`

string

`caption_ids`

list<numeric string>

`url_tags`

string

`adlabels`

list<Object>

`do_not_clone_flag`

boolean

`tag`

string

`thumbnail_source`

string

`bodies`

list<Object>

`text`

string

Supports Emoji

`url_tags`

string

`adlabels`

list<Object>

`translation_confidence`

int64

`asset_source`

enum {ACO\_TEXT\_LIBRARY, TEXT\_GEN\_INPUT\_TEXT, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_UNEDITED, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_MULTILINGUAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_MULTILINGUAL\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_LLM\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_LLM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_ORCHESTRATOR\_V\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_ORCHESTRATOR\_V\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_FOR\_IG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_FOR\_IG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V3\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V3\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_FOR\_FB\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_FOR\_FB\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ADS\_MANAGER\_LLM\_HEADLINE\_HEURISTICS\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ADS\_MANAGER\_LLM\_HEADLINE\_HEURISTICS\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_PPO\_ORCHESTRATOR\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_PPO\_ORCHESTRATOR\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_ALIGNER\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_ALIGNER\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_PRIMARY\_TEXT\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_PRIMARY\_TEXT\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_HISTORICAL\_ADS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_HISTORICAL\_ADS\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FREEFORM\_BRAND\_TONE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FREEFORM\_BRAND\_TONE\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_COHORT\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_COHORT\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LIGHTHOUSE\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LIGHTHOUSE\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_SP\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_SP\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_EA\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_EA\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_NON\_EN\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_NON\_EN\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_BASED\_GEN\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_BASED\_GEN\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_SEQUENTIAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_SEQUENTIAL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_MM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_MM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PPO\_TEXT\_IMAGE\_RM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PPO\_TEXT\_IMAGE\_RM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_LLAMA4\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_LLAMA4\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_GPT4O\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_GPT4O\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_HISTORICAL\_ADS\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_HISTORICAL\_ADS\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_COMPOUND\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_COMPOUND\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_GPT4O\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_GPT4O\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_COMPOUND\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_COMPOUND\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_DEEPER\_FUNNEL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_DEEPER\_FUNNEL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LLAMA4\_SEGMENT\_PROMPT\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LLAMA4\_SEGMENT\_PROMPT\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_TRANSLATION\_V1, TEXT\_FIRST\_MANUAL\_OPTION, TEXT\_AUTO\_TRANSLATION\_MT\_V1, TEXT\_AUTO\_TRANSLATION\_LLM\_V1, TEXT\_TRANSLATIONS\_LLAMA4\_V1\_TITLE, TEXT\_TRANSLATIONS\_LLAMA4\_V1\_BODY, TEXT\_AD\_HIGHLIGHT, PROMINENT\_HEADLINE\_NO\_SHOW, TEXT\_GEN\_AUTOMATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_DEEPER\_FUNNEL\_USER\_COHORT\_BATCH\_GENERATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_BRAND\_ALIGNER\_USER\_COHORT\_SEQUENTIAL\_GENERATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_BRAND\_ALIGNER\_USER\_COHORT\_BATCH\_GENERATION, TEXT\_GEN\_SUGGESTION\_MOCK\_UNEDITED, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED\_BEFORE\_AND\_AFTER\_ADD}

`uuid`

string

Supports Emoji

`text_gen_original_text`

string

Supports Emoji

`language`

string

`target_audience`

string

`tracking_tag`

list<enum {NONE, TEXT\_GEN\_LLM\_PERSONA\_EDITED\_TAG}>

`text_gen_input_text`

string

Supports Emoji

`type`

enum {TEMPLATE}

`action_type`

enum {DEFAULT\_ADD, DEFAULT\_ADD\_INTERACTION, MANUAL\_ADD, STICKY\_ADD, STICKY\_ADD\_INTERACTION}

`call_to_action_types`

list<enum{OPEN\_LINK, LIKE\_PAGE, SHOP\_NOW, PLAY\_GAME, INSTALL\_APP, USE\_APP, CALL, CALL\_ME, VIDEO\_CALL, INSTALL\_MOBILE\_APP, USE\_MOBILE\_APP, MOBILE\_DOWNLOAD, BOOK\_TRAVEL, LISTEN\_MUSIC, WATCH\_VIDEO, LEARN\_MORE, SIGN\_UP, DOWNLOAD, WATCH\_MORE, NO\_BUTTON, VISIT\_PAGES\_FEED, CALL\_NOW, APPLY\_NOW, CONTACT, BUY\_NOW, GET\_OFFER, GET\_OFFER\_VIEW, BUY\_TICKETS, UPDATE\_APP, GET\_DIRECTIONS, BUY, SEND\_UPDATES, MESSAGE\_PAGE, DONATE, SUBSCRIBE, SAY\_THANKS, SELL\_NOW, SHARE, DONATE\_NOW, GET\_QUOTE, CONTACT\_US, ORDER\_NOW, START\_ORDER, ADD\_TO\_CART, VIEW\_CART, VIEW\_IN\_CART, VIDEO\_ANNOTATION, RECORD\_NOW, INQUIRE\_NOW, CONFIRM, REFER\_FRIENDS, REQUEST\_TIME, GET\_SHOWTIMES, LISTEN\_NOW, WOODHENGE\_SUPPORT, SOTTO\_SUBSCRIBE, FOLLOW\_USER, RAISE\_MONEY, EVENT\_RSVP, WHATSAPP\_MESSAGE, FOLLOW\_NEWS\_STORYLINE, SEE\_MORE, BOOK\_NOW, FIND\_A\_GROUP, FIND\_YOUR\_GROUPS, PAY\_TO\_ACCESS, PURCHASE\_GIFT\_CARDS, FOLLOW\_PAGE, SEND\_A\_GIFT, SWIPE\_UP\_SHOP, SWIPE\_UP\_PRODUCT, SEND\_GIFT\_MONEY, PLAY\_GAME\_ON\_FACEBOOK, GET\_STARTED, OPEN\_INSTANT\_APP, AUDIO\_CALL, GET\_PROMOTIONS, JOIN\_CHANNEL, MAKE\_AN\_APPOINTMENT, ASK\_ABOUT\_SERVICES, BOOK\_A\_CONSULTATION, GET\_A\_QUOTE, BUY\_VIA\_MESSAGE, ASK\_FOR\_MORE\_INFO, CHAT\_WITH\_US, VIEW\_PRODUCT, VIEW\_CHANNEL, GET\_IN\_TOUCH, WATCH\_LIVE\_VIDEO}>

`descriptions`

list<Object>

`text`

string

Supports Emoji

`url_tags`

string

`adlabels`

list<Object>

`translation_confidence`

int64

`asset_source`

enum {ACO\_TEXT\_LIBRARY, TEXT\_GEN\_INPUT\_TEXT, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_UNEDITED, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_MULTILINGUAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_MULTILINGUAL\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_LLM\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_LLM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_ORCHESTRATOR\_V\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_ORCHESTRATOR\_V\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_FOR\_IG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_FOR\_IG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V3\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V3\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_FOR\_FB\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_FOR\_FB\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ADS\_MANAGER\_LLM\_HEADLINE\_HEURISTICS\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ADS\_MANAGER\_LLM\_HEADLINE\_HEURISTICS\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_PPO\_ORCHESTRATOR\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_PPO\_ORCHESTRATOR\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_ALIGNER\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_ALIGNER\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_PRIMARY\_TEXT\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_PRIMARY\_TEXT\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_HISTORICAL\_ADS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_HISTORICAL\_ADS\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FREEFORM\_BRAND\_TONE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FREEFORM\_BRAND\_TONE\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_COHORT\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_COHORT\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LIGHTHOUSE\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LIGHTHOUSE\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_SP\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_SP\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_EA\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_EA\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_NON\_EN\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_NON\_EN\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_BASED\_GEN\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_BASED\_GEN\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_SEQUENTIAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_SEQUENTIAL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_MM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_MM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PPO\_TEXT\_IMAGE\_RM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PPO\_TEXT\_IMAGE\_RM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_LLAMA4\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_LLAMA4\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_GPT4O\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_GPT4O\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_HISTORICAL\_ADS\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_HISTORICAL\_ADS\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_COMPOUND\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_COMPOUND\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_GPT4O\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_GPT4O\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_COMPOUND\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_COMPOUND\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_DEEPER\_FUNNEL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_DEEPER\_FUNNEL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LLAMA4\_SEGMENT\_PROMPT\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LLAMA4\_SEGMENT\_PROMPT\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_TRANSLATION\_V1, TEXT\_FIRST\_MANUAL\_OPTION, TEXT\_AUTO\_TRANSLATION\_MT\_V1, TEXT\_AUTO\_TRANSLATION\_LLM\_V1, TEXT\_TRANSLATIONS\_LLAMA4\_V1\_TITLE, TEXT\_TRANSLATIONS\_LLAMA4\_V1\_BODY, TEXT\_AD\_HIGHLIGHT, PROMINENT\_HEADLINE\_NO\_SHOW, TEXT\_GEN\_AUTOMATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_DEEPER\_FUNNEL\_USER\_COHORT\_BATCH\_GENERATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_BRAND\_ALIGNER\_USER\_COHORT\_SEQUENTIAL\_GENERATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_BRAND\_ALIGNER\_USER\_COHORT\_BATCH\_GENERATION, TEXT\_GEN\_SUGGESTION\_MOCK\_UNEDITED, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED\_BEFORE\_AND\_AFTER\_ADD}

`tracking_tag`

list<enum {NONE, TEXT\_GEN\_LLM\_PERSONA\_EDITED\_TAG}>

`uuid`

string

Supports Emoji

`text_gen_original_text`

string

Supports Emoji

`language`

string

`target_audience`

string

`text_gen_input_text`

string

Supports Emoji

`action_type`

enum {DEFAULT\_ADD, DEFAULT\_ADD\_INTERACTION, MANUAL\_ADD, STICKY\_ADD, STICKY\_ADD\_INTERACTION}

`type`

enum {TEMPLATE}

`link_urls`

list<Object>

`website_url`

URL

`display_url`

string

`deeplink_url`

string

`carousel_see_more_url`

URL

`url_tags`

string

`adlabels`

list<Object>

`type`

enum {TEMPLATE}

`omnichannel_link_spec`

JSON or object-like arrays

`web`

JSON or object-like arrays

`url`

string

`app`

JSON or object-like arrays

`application_id`

int64

`platform_specs`

Object

`android`

Object

`app_name`

string

`package_name`

string

`url`

string

`ios`

Object

`app_name`

string

`app_store_id`

string

`url`

string

`ipad`

Object

`app_name`

string

`app_store_id`

string

`url`

string

`iphone`

Object

`app_name`

string

`app_store_id`

string

`url`

string

`object_store_urls`

list<string>

`titles`

list<Object>

`text`

string

Supports Emoji

`url_tags`

string

`adlabels`

list<Object>

`translation_confidence`

int64

`asset_source`

enum {ACO\_TEXT\_LIBRARY, TEXT\_GEN\_INPUT\_TEXT, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_DIVERSITY\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_UNEDITED, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_PARAPHRASING\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_BRAND\_IDENTITY\_V\_1\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_V\_1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_MULTILINGUAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ORCHESTRATOR\_MULTILINGUAL\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_LLM\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_LLM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_2\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_V\_1\_2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_SEMANTIC\_VARIATIONS\_SINGLE\_STAGE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_V\_1\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_BRAND\_IDENTITY\_V\_1\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_ORCHESTRATOR\_V\_1\_EDITED, TEXT\_GEN\_SUGGESTION\_GUIDANCE\_ORCHESTRATOR\_V\_1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V2\_DPO\_PPO\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_V3\_LONG\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_FOR\_IG\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_FOR\_IG\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_V2\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_APP\_DESCRIPTION\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V3\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_ORCHESTRATOR\_V3\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_LC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_FOR\_FB\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FC\_PPO\_LLAMA3\_V1\_FOR\_FB\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_LLM\_SFT\_LLAMA3\_V1\_EDITED\_BEFORE\_AND\_AFTER\_ADD, TEXT\_GEN\_SUGGESTION\_ADS\_MANAGER\_LLM\_HEADLINE\_HEURISTICS\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_ADS\_MANAGER\_LLM\_HEADLINE\_HEURISTICS\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_PPO\_ORCHESTRATOR\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_PPO\_ORCHESTRATOR\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_ALIGNER\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_ALIGNER\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_PRIMARY\_TEXT\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_PRIMARY\_TEXT\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_HISTORICAL\_ADS\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_IMPLICIT\_BRAND\_TONE\_HISTORICAL\_ADS\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FREEFORM\_BRAND\_TONE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_FREEFORM\_BRAND\_TONE\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_COHORT\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_COHORT\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LIGHTHOUSE\_LLAMA3\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LIGHTHOUSE\_LLAMA3\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_SP\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_SP\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_EA\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PURPOSEFUL\_GEN\_EA\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_NON\_EN\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_UNIFIED\_MULTILINGUAL\_NON\_EN\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_BASED\_GEN\_V1\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_BASED\_GEN\_V1\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_SEQUENTIAL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_PPO\_SEQUENTIAL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_MM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_DEEPER\_FUNNEL\_MM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PPO\_TEXT\_IMAGE\_RM\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PPO\_TEXT\_IMAGE\_RM\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_LLAMA4\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_LLAMA4\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_GPT4O\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_GPT4O\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_HISTORICAL\_ADS\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_BRAND\_HISTORICAL\_ADS\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_COMPOUND\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_V2\_COMPOUND\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_GPT4O\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_GPT4O\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_COMPOUND\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_EDITABLE\_COMPOUND\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_DEEPER\_FUNNEL\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_PERSONA\_DEEPER\_FUNNEL\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LLAMA4\_SEGMENT\_PROMPT\_UNEDITED, TEXT\_GEN\_SUGGESTION\_LLM\_LLAMA4\_SEGMENT\_PROMPT\_EDITED, TEXT\_GEN\_SUGGESTION\_LLM\_TRANSLATION\_V1, TEXT\_FIRST\_MANUAL\_OPTION, TEXT\_AUTO\_TRANSLATION\_MT\_V1, TEXT\_AUTO\_TRANSLATION\_LLM\_V1, TEXT\_TRANSLATIONS\_LLAMA4\_V1\_TITLE, TEXT\_TRANSLATIONS\_LLAMA4\_V1\_BODY, TEXT\_AD\_HIGHLIGHT, PROMINENT\_HEADLINE\_NO\_SHOW, TEXT\_GEN\_AUTOMATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_DEEPER\_FUNNEL\_USER\_COHORT\_BATCH\_GENERATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_BRAND\_ALIGNER\_USER\_COHORT\_SEQUENTIAL\_GENERATION, TEXT\_GEN\_AUTOMATION\_ALPHA\_BRAND\_ALIGNER\_USER\_COHORT\_BATCH\_GENERATION, TEXT\_GEN\_SUGGESTION\_MOCK\_UNEDITED, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED\_BEFORE\_ADD, TEXT\_GEN\_SUGGESTION\_MOCK\_EDITED\_BEFORE\_AND\_AFTER\_ADD}

`uuid`

string

Supports Emoji

`text_gen_original_text`

string

Supports Emoji

`language`

string

`target_audience`

string

`text_gen_input_text`

string

Supports Emoji

`action_type`

enum {DEFAULT\_ADD, DEFAULT\_ADD\_INTERACTION, MANUAL\_ADD, STICKY\_ADD, STICKY\_ADD\_INTERACTION}

`type`

enum {TEMPLATE}

`tracking_tag`

list<enum {NONE, TEXT\_GEN\_LLM\_PERSONA\_EDITED\_TAG}>

`captions`

list<Object>

`text`

string

`url_tags`

string

`adlabels`

list<Object>

`type`

enum {TEMPLATE}

`ad_formats`

list<enum {AUTOMATIC\_FORMAT, CAROUSEL, CAROUSEL\_IMAGE, CAROUSEL\_VIDEO, COLLECTION, SINGLE\_IMAGE, SINGLE\_VIDEO, POST}>

`groups`

list<JSON or object-like arrays>

`image_label`

Object

`video_label`

Object

`body_label`

Object

`description_label`

Object

`link_url_label`

Object

`title_label`

Object

`caption_label`

Object

`target_rules`

list<JSON or object-like arrays>

`targeting`

JSON or object-like arrays

`age_min`

int64

`age_max`

int64

`audience_network_positions`

list<enum{classic, rewarded\_video, instream\_video}>

`device_platforms`

list<enum{desktop, mobile}>

`facebook_positions`

list<enum{feed, instant\_article, instream\_video, marketplace, right\_hand\_column, search, story, suggested\_video, video\_feeds, story\_sticker, facebook\_reels\_overlay, biz\_disco\_feed, facebook\_reels, profile\_feed, profile\_reels, notification}>

`instagram_positions`

list<enum{reels, reels\_overlay, stream, story, explore, explore\_home, ig\_search, shop, profile\_feed, profile\_reels, effect\_tray}>

`messenger_positions`

list<enum{messenger\_home, story}>

`threads_positions`

list<enum{threads\_stream}>

`genders`

list<int64>

`publisher_platforms`

list<enum{facebook, instagram, audience\_network, messenger, oculus, whatsapp, threads}>

`interests`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`behaviors`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`customization_spec`

JSON or object-like arrays

`age_min`

int64

`age_max`

int64

`audience_network_positions`

list<enum{classic, rewarded\_video, instream\_video}>

`device_platforms`

list<enum{desktop, mobile}>

`facebook_positions`

list<enum{feed, instant\_article, instream\_video, marketplace, right\_hand\_column, search, story, suggested\_video, video\_feeds, story\_sticker, facebook\_reels\_overlay, biz\_disco\_feed, facebook\_reels, profile\_feed, profile\_reels, notification}>

`instagram_positions`

list<enum{reels, reels\_overlay, stream, story, explore, explore\_home, ig\_search, shop, profile\_feed, profile\_reels, effect\_tray}>

`genders`

list<int64>

`publisher_platforms`

list<enum{facebook, instagram, audience\_network, messenger, oculus, whatsapp, threads}>

`interests`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`behaviors`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`geo_locations`

JSON object

`marketing_message_channels`

list<enum{whatsapp, messenger}>

`image_label`

Object

`video_label`

Object

`body_label`

Object

`description_label`

Object

`link_url_label`

Object

`title_label`

Object

`caption_label`

Object

`call_to_action_label`

Object

`priority`

int64

`is_default`

boolean

`asset_customization_rules`

list<JSON or object-like arrays>

`targeting`

JSON or object-like arrays

`age_min`

int64

`age_max`

int64

`audience_network_positions`

list<enum{classic, rewarded\_video, instream\_video}>

`device_platforms`

list<enum{desktop, mobile}>

`facebook_positions`

list<enum{feed, instant\_article, instream\_video, marketplace, right\_hand\_column, search, story, suggested\_video, video\_feeds, story\_sticker, facebook\_reels\_overlay, biz\_disco\_feed, facebook\_reels, profile\_feed, profile\_reels, notification}>

`instagram_positions`

list<enum{reels, reels\_overlay, stream, story, explore, explore\_home, ig\_search, shop, profile\_feed, profile\_reels, effect\_tray}>

`messenger_positions`

list<enum{messenger\_home, story}>

`threads_positions`

list<enum{threads\_stream}>

`genders`

list<int64>

`publisher_platforms`

list<enum{facebook, instagram, audience\_network, messenger, oculus, whatsapp, threads}>

`interests`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`behaviors`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`customization_spec`

JSON or object-like arrays

`age_min`

int64

`age_max`

int64

`audience_network_positions`

list<enum{classic, rewarded\_video, instream\_video}>

`device_platforms`

list<enum{desktop, mobile}>

`facebook_positions`

list<enum{feed, instant\_article, instream\_video, marketplace, right\_hand\_column, search, story, suggested\_video, video\_feeds, story\_sticker, facebook\_reels\_overlay, biz\_disco\_feed, facebook\_reels, profile\_feed, profile\_reels, notification}>

`instagram_positions`

list<enum{reels, reels\_overlay, stream, story, explore, explore\_home, ig\_search, shop, profile\_feed, profile\_reels, effect\_tray}>

`genders`

list<int64>

`publisher_platforms`

list<enum{facebook, instagram, audience\_network, messenger, oculus, whatsapp, threads}>

`interests`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`behaviors`

list<JSON or object-like arrays>

`id`

int64

`name`

string

`geo_locations`

JSON object

`marketing_message_channels`

list<enum{whatsapp, messenger}>

`image_label`

Object

`video_label`

Object

`body_label`

Object

`description_label`

Object

`link_url_label`

Object

`title_label`

Object

`caption_label`

Object

`call_to_action_label`

Object

`priority`

int64

`is_default`

boolean

`optimization_type`

enum{REGULAR, LANGUAGE, PLACEMENT, BRAND, LOCALIZED\_PLACEMENTS, FORMAT\_AUTOMATION, DOF\_MESSAGING\_DESTINATION, ACO\_AUTOFLOW, MULTI\_CREATOR, UNIFIED\_PROFILE\_VISIT\_DESTINATION}

`call_to_actions`

list<Object>

`type`

enum{BOOK\_TRAVEL, CONTACT\_US, DONATE, DONATE\_NOW, DOWNLOAD, GET\_DIRECTIONS, GO\_LIVE, INTERESTED, LEARN\_MORE, LIKE\_PAGE, MESSAGE\_PAGE, RAISE\_MONEY, SAVE, SEND\_TIP, SHOP\_NOW, SIGN\_UP, VIEW\_INSTAGRAM\_PROFILE, INSTAGRAM\_MESSAGE, LOYALTY\_LEARN\_MORE, PURCHASE\_GIFT\_CARDS, PAY\_TO\_ACCESS, SEE\_MORE, TRY\_IN\_CAMERA, WHATSAPP\_LINK, GET\_IN\_TOUCH, BOOK\_NOW, CHECK\_AVAILABILITY, ORDER\_NOW, WHATSAPP\_MESSAGE, GET\_MOBILE\_APP, INSTALL\_MOBILE\_APP, USE\_MOBILE\_APP, INSTALL\_APP, USE\_APP, PLAY\_GAME, WATCH\_VIDEO, WATCH\_MORE, OPEN\_LINK, NO\_BUTTON, LISTEN\_MUSIC, MOBILE\_DOWNLOAD, GET\_OFFER, GET\_OFFER\_VIEW, BUY\_NOW, BUY\_TICKETS, UPDATE\_APP, BET\_NOW, ADD\_TO\_CART, SELL\_NOW, GET\_SHOWTIMES, LISTEN\_NOW, GET\_EVENT\_TICKETS, REMIND\_ME, SEARCH\_MORE, PRE\_REGISTER, SWIPE\_UP\_PRODUCT, SWIPE\_UP\_SHOP, PLAY\_GAME\_ON\_FACEBOOK, VISIT\_WORLD, OPEN\_INSTANT\_APP, JOIN\_GROUP, GET\_PROMOTIONS, SEND\_UPDATES, INQUIRE\_NOW, VISIT\_PROFILE, CHAT\_ON\_WHATSAPP, EXPLORE\_MORE, CONFIRM, JOIN\_CHANNEL, MAKE\_AN\_APPOINTMENT, ASK\_ABOUT\_SERVICES, BOOK\_A\_CONSULTATION, GET\_A\_QUOTE, BUY\_VIA\_MESSAGE, ASK\_FOR\_MORE\_INFO, CHAT\_WITH\_US, VIEW\_PRODUCT, VIEW\_CHANNEL, WATCH\_LIVE\_VIDEO, IMAGINE, CALL, MISSED\_CALL, CALL\_NOW, CALL\_ME, APPLY\_NOW, BUY, GET\_QUOTE, SUBSCRIBE, RECORD\_NOW, VOTE\_NOW, GIVE\_FREE\_RIDES, REGISTER\_NOW, OPEN\_MESSENGER\_EXT, EVENT\_RSVP, CIVIC\_ACTION, SEND\_INVITES, REFER\_FRIENDS, REQUEST\_TIME, SEE\_MENU, SEARCH, TRY\_IT, TRY\_ON, LINK\_CARD, DIAL\_CODE, FIND\_YOUR\_GROUPS, START\_ORDER}

Required

`value`

Object

Supports Emoji

`link`

URL

`app_link`

string

`page`

numeric string or integer

`link_format`

enum {VIDEO\_LEAD, VIDEO\_LPP, VIDEO\_NEKO, VIDEO\_NON\_LINK, VIDEO\_SHOP, WHATSAPP\_CATALOG\_ATTACHMENT}

`application`

numeric string or integer

`link_title`

string

Supports Emoji

`link_description`

string

Supports Emoji

`link_caption`

string

`product_link`

string

`get_movie_showtimes`

boolean

`sponsorship`

Object

`link`

URL

`image`

URL

`video_annotation`

Object

`annotations`

list<Object>

`start_time_in_sec`

int64

`end_time_in_sec`

int64

`link`

URL

`link_title`

string

`link_description`

string

`link_caption`

string

`image_url`

URL

`header_color`

string

`logo_url`

URL

`post_click_cta_title`

string

`post_click_description_title`

string

`offer_id`

numeric string or integer

`offer_view_id`

numeric string or integer

`advanced_data`

Object

`offer_id`

numeric string or integer

`lead_gen_form_id`

numeric string or integer

`referral_id`

numeric string or integer

`fundraiser_campaign_id`

numeric string or integer

`event_id`

numeric string or integer

`event_tour_id`

numeric string or integer

`app_destination`

enum {MESSENGER, MESSENGER\_EXTENSIONS, MESSENGER\_GAMES, LINK\_CARD, MARKETPLACE, WHATSAPP, INSTAGRAM\_DIRECT, INSTAGRAM\_LIVE\_VIDEO, FACEBOOK\_LIVE\_VIDEO}

`app_destination_page_id`

numeric string or integer

`is_canvas_video_transition_enabled`

boolean

`whatsapp_number`

string

`preinput_text`

string

`customized_message_page_cta_text`

string

`external_offer_provider_id`

numeric string or integer

`origins`

enum {COMPOSER, CAMERA}

`object_store_urls`

array<string>

`facebook_login_spec`

Object

`facebook_login_app_id`

numeric string or integer

`offer_type`

enum {NO\_OFFER, PERCENTAGE\_BASED, AMOUNT\_BASED}

`offer_pct_call_to_action`

enum {TEN}

`offer_amt_call_to_action`

enum {TEN}

`product_id`

numeric string or integer

`group_id`

numeric string or integer

`channel_id`

string

`land_on_whatsapp_catalog`

enum{1, 2}

`adlabels`

list<Object>

`autotranslate`

array<string>

`additional_data`

JSON object

`audios`

list<Object>

`type`

enum {OPTED\_OUT, RANDOM, SELECTED}

`selected_audios`

array<JSON object>

`is_audio_swap`

boolean

`app_product_page_id`

string

`promotional_metadata`

Object

`allowed_coupon_code_sources`

array<enum {PROVIDED\_BY\_MERCHANT, PROVIDED\_BY\_MERCHANT\_OFFER\_MANAGEMENT, DETECTED\_FROM\_MERCHANT\_ADS, DETECTED\_FROM\_MERCHANT\_WEBSITE, DETECTED\_FROM\_MERCHANT\_WEBSITE\_URL, AD\_CREATIVE\_PRIMARY\_TEXT, AD\_CREATIVE\_HEADLINE, AD\_CREATIVE\_DESCRIPTION, AD\_CREATIVE\_MANUAL\_COUPON\_CODES, EMAIL\_CAPTURE\_SHOPIFY, EMAIL\_CAPTURE\_NON\_SHOPIFY, EMAIL\_CAPTURE\_GENERIC\_CODE, WA\_CAPTURE\_GENERIC\_CODE}>

`allowed_promo_offer_ids`

array<numeric string>

`manual_coupon_codes`

array<string>

`is_auto_update_allowed`

boolean

`coupon_codes`

JSON object {enum {PROVIDED\_BY\_MERCHANT, PROVIDED\_BY\_MERCHANT\_OFFER\_MANAGEMENT, DETECTED\_FROM\_MERCHANT\_ADS, DETECTED\_FROM\_MERCHANT\_WEBSITE, DETECTED\_FROM\_MERCHANT\_WEBSITE\_URL, AD\_CREATIVE\_PRIMARY\_TEXT, AD\_CREATIVE\_HEADLINE, AD\_CREATIVE\_DESCRIPTION, AD\_CREATIVE\_MANUAL\_COUPON\_CODES, EMAIL\_CAPTURE\_SHOPIFY, EMAIL\_CAPTURE\_NON\_SHOPIFY, EMAIL\_CAPTURE\_GENERIC\_CODE, WA\_CAPTURE\_GENERIC\_CODE} : array<string>}

`offer_details`

array<JSON object>

`excluded_offers`

array<string>

`web_destination_spec`

Object

`url`

URL

`call_ads_configuration`

Object

`auto_response`

string

`call_destination_type`

enum{PHONE, MESSENGER, MESSENGER\_AND\_PHONE, WEBSITE\_AND\_CALL}

`callback_type`

enum{FORM, NOT\_ENABLED, MESSENGER}

`phone_number`

string

`recording_consent`

enum{TRANSCRIPTION\_ENABLED, DISABLED}

`lead_gen_configuration`

Object

`is_form_qa_enabled`

boolean

`is_work_email_enforcement_enabled`

boolean

`verification_type`

enum{NOT\_ENABLED, SMS}

`authorization_category`

enum{NONE, POLITICAL, POLITICAL\_WITH\_DIGITALLY\_CREATED\_MEDIA}

Specifies whether ad is political or not. If your ad has political content, set this to `POLITICAL`, otherwise it defaults to `null`. Your ad will be disapproved if it contains political content but not labeled `POLITICAL`. See [Facebook Advertising Policies](https://www.facebook.com/policies/ads). This field cannot be used for [Dynamic Ads](https://developers.facebook.com/docs/marketing-api/dynamic-ad).

`body`

string

The body of the ad.

Supports Emoji

`branded_content`

JSON object

branded\_content

`partners`

array<JSON object>

partners

`fb_page_id`

numeric string

fb\_page\_id

`ig_user_id`

numeric string

ig\_user\_id

`ig_asset_id`

numeric string

ig\_asset\_id

`identity_type`

enum {ADVERTISER, PARTNER\_BUSINESS, PARTNER\_CREATOR}

identity\_type

`has_create_ads_access`

boolean

has\_create\_ads\_access

`ui_version`

int64

ui\_version

`instagram_boost_post_access_token`

string

instagram\_boost\_post\_access\_token

`ad_format`

enum {0, 1, 2, 3}

ad\_format

`creator_ad_permission_type`

enum {FB\_ADS\_PERMISSION, FB\_ORGANIC\_PERMISSION, FB\_POST\_PERMISSION, IG\_ADS\_PERMISSION, IG\_ADS\_PERMISSION\_DIRECTION\_AGNOSTIC, IG\_ORGANIC\_PERMISSION, IG\_POST\_PERMISSION, SAME\_BUSINESS\_PERMISSION}

creator\_ad\_permission\_type

`is_mca_internal`

boolean

is\_mca\_internal

`promoted_page_id`

numeric string

promoted\_page\_id

`facebook_boost_post_access_token`

string

facebook\_boost\_post\_access\_token

`content_search_input`

string

content\_search\_input

`testimonial`

string

testimonial

`product_set_partner_selection_status`

enum {OPT\_IN, OPT\_OUT}

product\_set\_partner\_selection\_status

`parent_source_instagram_media_id`

numeric string

parent\_source\_instagram\_media\_id

`parent_source_facebook_post_id`

numeric string

parent\_source\_facebook\_post\_id

`testimonial_locale`

string

testimonial\_locale

`branded_content_sponsor_page_id`

numeric string or integer

ID for page representing business which runs Branded Content ads. See [Creating Branded Content Ads](/docs/marketing-api/guides/branded-content).

`bundle_folder_id`

numeric string or integer

The [Dynamic Ad's](/docs/marketing-api/dynamic-product-ads) bundle folder ID

`call_to_action`

Object

This field promotes an onsite or offsite action.

Supports Emoji

`type`

enum{BOOK\_TRAVEL, CONTACT\_US, DONATE, DONATE\_NOW, DOWNLOAD, GET\_DIRECTIONS, GO\_LIVE, INTERESTED, LEARN\_MORE, LIKE\_PAGE, MESSAGE\_PAGE, RAISE\_MONEY, SAVE, SEND\_TIP, SHOP\_NOW, SIGN\_UP, VIEW\_INSTAGRAM\_PROFILE, INSTAGRAM\_MESSAGE, LOYALTY\_LEARN\_MORE, PURCHASE\_GIFT\_CARDS, PAY\_TO\_ACCESS, SEE\_MORE, TRY\_IN\_CAMERA, WHATSAPP\_LINK, GET\_IN\_TOUCH, BOOK\_NOW, CHECK\_AVAILABILITY, ORDER\_NOW, WHATSAPP\_MESSAGE, GET\_MOBILE\_APP, INSTALL\_MOBILE\_APP, USE\_MOBILE\_APP, INSTALL\_APP, USE\_APP, PLAY\_GAME, WATCH\_VIDEO, WATCH\_MORE, OPEN\_LINK, NO\_BUTTON, LISTEN\_MUSIC, MOBILE\_DOWNLOAD, GET\_OFFER, GET\_OFFER\_VIEW, BUY\_NOW, BUY\_TICKETS, UPDATE\_APP, BET\_NOW, ADD\_TO\_CART, SELL\_NOW, GET\_SHOWTIMES, LISTEN\_NOW, GET\_EVENT\_TICKETS, REMIND\_ME, SEARCH\_MORE, PRE\_REGISTER, SWIPE\_UP\_PRODUCT, SWIPE\_UP\_SHOP, PLAY\_GAME\_ON\_FACEBOOK, VISIT\_WORLD, OPEN\_INSTANT\_APP, JOIN\_GROUP, GET\_PROMOTIONS, SEND\_UPDATES, INQUIRE\_NOW, VISIT\_PROFILE, CHAT\_ON\_WHATSAPP, EXPLORE\_MORE, CONFIRM, JOIN\_CHANNEL, MAKE\_AN\_APPOINTMENT, ASK\_ABOUT\_SERVICES, BOOK\_A\_CONSULTATION, GET\_A\_QUOTE, BUY\_VIA\_MESSAGE, ASK\_FOR\_MORE\_INFO, CHAT\_WITH\_US, VIEW\_PRODUCT, VIEW\_CHANNEL, WATCH\_LIVE\_VIDEO, IMAGINE, CALL, MISSED\_CALL, CALL\_NOW, CALL\_ME, APPLY\_NOW, BUY, GET\_QUOTE, SUBSCRIBE, RECORD\_NOW, VOTE\_NOW, GIVE\_FREE\_RIDES, REGISTER\_NOW, OPEN\_MESSENGER\_EXT, EVENT\_RSVP, CIVIC\_ACTION, SEND\_INVITES, REFER\_FRIENDS, REQUEST\_TIME, SEE\_MENU, SEARCH, TRY\_IT, TRY\_ON, LINK\_CARD, DIAL\_CODE, FIND\_YOUR\_GROUPS, START\_ORDER}

The type of the action. Not all types can be used for all ads. Check [Ads Product Guide](https://www.facebook.com/business/ads-guide) to see which type can be used for based on the `objective` of your campaign.

Required

`value`

Object

Default value: `Vec`

JSON containing the call to action data.

Supports Emoji

`link`

URL

`app_link`

string

`page`

numeric string or integer

`link_format`

enum {VIDEO\_LEAD, VIDEO\_LPP, VIDEO\_NEKO, VIDEO\_NON\_LINK, VIDEO\_SHOP, WHATSAPP\_CATALOG\_ATTACHMENT}

`application`

numeric string or integer

`link_title`

string

Supports Emoji

`link_description`

string

Supports Emoji

`link_caption`

string

`product_link`

string

`get_movie_showtimes`

boolean

`sponsorship`

Object

`link`

URL

`image`

URL

`video_annotation`

Object

`annotations`

list<Object>

`start_time_in_sec`

int64

`end_time_in_sec`

int64

`link`

URL

`link_title`

string

`link_description`

string

`link_caption`

string

`image_url`

URL

`header_color`

string

`logo_url`

URL

`post_click_cta_title`

string

`post_click_description_title`

string

`offer_id`

numeric string or integer

`offer_view_id`

numeric string or integer

`advanced_data`

Object

`offer_id`

numeric string or integer

`lead_gen_form_id`

numeric string or integer

`referral_id`

numeric string or integer

`fundraiser_campaign_id`

numeric string or integer

`event_id`

numeric string or integer

`event_tour_id`

numeric string or integer

`app_destination`

enum {MESSENGER, MESSENGER\_EXTENSIONS, MESSENGER\_GAMES, LINK\_CARD, MARKETPLACE, WHATSAPP, INSTAGRAM\_DIRECT, INSTAGRAM\_LIVE\_VIDEO, FACEBOOK\_LIVE\_VIDEO}

`app_destination_page_id`

numeric string or integer

`is_canvas_video_transition_enabled`

boolean

`whatsapp_number`

string

`preinput_text`

string

`customized_message_page_cta_text`

string

`external_offer_provider_id`

numeric string or integer

`origins`

enum {COMPOSER, CAMERA}

`object_store_urls`

array<string>

`facebook_login_spec`

Object

`facebook_login_app_id`

numeric string or integer

`offer_type`

enum {NO\_OFFER, PERCENTAGE\_BASED, AMOUNT\_BASED}

`offer_pct_call_to_action`

enum {TEN}

`offer_amt_call_to_action`

enum {TEN}

`product_id`

numeric string or integer

`group_id`

numeric string or integer

`channel_id`

string

`land_on_whatsapp_catalog`

enum{1, 2}

`categorization_criteria`

enum{brand, category, product\_type}

The [Dynamic Category Ad's](/docs/marketing-api/dynamic-product-ads) categorization criteria

`category_media_source`

enum{CATEGORY, MIXED, PRODUCTS\_COLLAGE, PRODUCTS\_SLIDESHOW}

The [Dynamic Ad's](/docs/marketing-api/dynamic-product-ads) rendering mode for category ads

`contextual_multi_ads`

JSON object

contextual\_multi\_ads

`eligibility`

array<enum {}>

eligibility

`enroll_status`

enum {OPT\_IN, OPT\_OUT}

enroll\_status

`action_metadata`

JSON object

action\_metadata

`type`

enum {DEFAULT, DEFAULT\_OPT\_IN, DEFAULT\_OPT\_OUT, DUPLICATION\_UPGRADE, DUPLICATION\_UPSELL, MANUAL, STICKY\_OPT\_IN, STICKY\_OPT\_OUT}

type

Required

`degrees_of_freedom_spec`

JSON object

Specifies the type of transformation which is enabled for the given creative

`ad_handle_type`

enum{ORIGINAL, DYNAMIC}

`degrees_of_freedom_type`

enum{DISABLED, USER\_ENROLLED\_AUTOFLOW, USER\_ENROLLED\_LWI\_ACO, USER\_ENROLLED\_NON\_DCO, USER\_ENROLLED\_IMAGE\_CROPPING\_NON\_DCO, USER\_ENROLLED, VIDEO\_TEMPLATES, FAM\_TOGGLE\_ON, FAM\_TOGGLE\_OFF, SMART\_CROP\_ELIGIBLE\_ON, SMART\_CROP\_ELIGIBLE\_OFF, SMART\_CROP\_INELIGIBLE\_ON, SMART\_CROP\_INELIGIBLE\_OFF, VCK\_MIXED\_FORMAT}

Required

`multi_media_transformation_type`

enum{ADSET\_IMAGES\_TO\_VIDEO, CAROUSEL\_IMAGES\_TO\_VIDEO}

`mobile_app_star_rating_enabled`

boolean

Default value: `false`

`image_transformation_types`

list<enum{CROPPING, ENHANCEMENT, COLOR\_FILTERING, AUTOGEN\_MIXED\_FORMAT, FB\_REELS\_IMAGE\_DEFAULT, IG\_REELS\_IMAGE\_PORTAL}>

`text_transformation_types`

list<enum{TEXT\_LIQUIDITY}>

`video_transformation_types`

list<enum{CROPPING, CROPPING\_ACF, FB\_REELS\_VIDEO\_DEFAULT, IG\_REELS\_VIDEO\_PORTAL}>

`stories_transformation_types`

list<enum{SMART\_CROP, INSTAGRAM\_PROFILE\_CARD, INSTAGRAM\_END\_CARD, INSTAGRAM\_MOBILE\_APP\_INSTALL\_CARD, PORTAL}>

`creative_features_spec`

JSON object {enum {PRODUCT\_METADATA\_AUTOMATION, PROFILE\_CARD, STANDARD\_ENHANCEMENTS\_CATALOG, VIDEO\_TO\_IMAGE} : JSON object}

`destination_set_id`

numeric string

The ID of the Product Set for a Destination Catalog that will be used to link with Travel Catalogs.

`dynamic_ad_voice`

enum{DYNAMIC, STORY\_OWNER}

Determines the Page voice to be used in [Dynamic Local Ads](/docs/marketing-api/guides/local-awareness)

`enable_direct_install`

boolean

Whether Direct Install should be enabled on supported devices.

`enable_launch_instant_app`

boolean

Whether Instant App should be enabled on supported devices.

`facebook_branded_content`

JSON object

Params required for facebook branded content.

`sponsor_page_id`

numeric string

sponsor\_page\_id

`shared_to_sponsor_status`

enum {NOT\_SHARED, ALL\_SHARED}

shared\_to\_sponsor\_status

`image_crops`

dictionary { enum{191x100, 100x72, 400x150, 600x360, 100x100, 400x500, 90x160} : <list<list<int64>>> }

Crop dimensions for the image specified. See [image crop reference](/docs/reference/ads-api/image-crops/) for more details.

`image_file`

string

Reference to a local image file to upload for use in a creative. Not to exceed 8MB in size. If `object_story_spec` or `object_story_id` is specified, this field will be ignored

`image_hash`

string

Image hash for an image you have [uploaded](/docs/marketing-api/adimage) and can be used in a creative. If `object_story_spec` or `object_story_id` is specified, this field will be ignored

`image_url`

URL

URL for the image in this ad creative. Do not use image URLs returned by Facebook. Instead you should host the image on your own servers. Facebook saves the image from your URL to your [ad account's image library](/docs/marketing-api/adimage). Images cannot exceed 8 MB. You must also provide one of these three fields: `image_file`, `image_hash`, or `image_url`.

`instagram_branded_content`

JSON object

Params required for instagram branded content.

`sponsor_id`

numeric string or integer

`sponsor_asset_id`

numeric string or integer

`instagram_permalink_url`

URL

URL for a post on Instagram you want to run as an ad. Also known as Instagram media.

`instagram_user_id`

numeric string or integer

Instagram user ID

`interactive_components_spec`

JSON object

The specification for interactive component overlay on the media.

Supports Emoji

`child_attachments`

list<JSON object>

`components`

list<JSON object>

`poll_spec`

JSON object

RequiredSupports Emoji

`option_a_text`

string

Required

`option_b_text`

string

Required

`question_text`

string

Default value: `""`

`product_tag_spec`

JSON object

`position_spec`

JSON object

`link_spec`

JSON object

`feed_media_spec`

JSON object

`product_sticker_spec`

JSON object

`cta_sticker_spec`

JSON object

`type`

enum {7, 14, 9, 4, 8, 5, 3, 12, 1, 11, 10, 2}

Required

`enroll_status`

enum {OPT\_IN, OPT\_OUT}

`components`

list<JSON object>

`poll_spec`

JSON object

RequiredSupports Emoji

`option_a_text`

string

Required

`option_b_text`

string

Required

`question_text`

string

Default value: `""`

`product_tag_spec`

JSON object

`position_spec`

JSON object

`link_spec`

JSON object

`feed_media_spec`

JSON object

`product_sticker_spec`

JSON object

`cta_sticker_spec`

JSON object

`type`

enum {7, 14, 9, 4, 8, 5, 3, 12, 1, 11, 10, 2}

Required

`enroll_status`

enum {OPT\_IN, OPT\_OUT}

`link_og_id`

string

The Open Graph (OG) ID for the link in this creative if the landing page has OG tags.

`link_url`

URL

Identify a specific landing tab on your Facebook page by the Page tab's URL. See [connection objects](/docs/reference/ads-api/connectionobjects/) for retrieving Page tab URLs. You can add [app\_data](/docs/facebook-login/manually-build-a-login-flow) parameters to the URL to pass data to a Page's tab.

`name`

string

Name of this ad creative as seen in the ad account's library.

`object_id`

int64

The Facebook object ID that is relevant to the ad. See [connection objects](/docs/reference/ads-api/connectionobjects/)

`object_story_id`

post\_id

ID of a Facebook Page post to use in an ad. You can get this ID by [querying the posts of the page](/docs/graph-api/reference/page/feed/). If this post includes an image, it should not exceed 8 MB. Facebook will upload the image from the post to your ad account's [image library](/docs/marketing-api/adimage).

`object_story_spec`

string (ObjectStorySpec)

JSON string of [AdCreativeObjectStorySpec](/docs/marketing-api/reference/ad-creative-object-story-spec/) type. Use if you want to create a new unpublished page post and turn the post into an ad. The Page ID and the content to create a new unpublished page post. Specify `link_data`, `photo_data`, `video_data`, `text_data` or `template_data` with the content.

Supports Emoji

`object_type`

string

The type of Facebook object you want to advertise. Allowed values are:  
`PAGE`  
`DOMAIN`  
`EVENT`  
`STORE_ITEM`: refers to an iTunes or Google Play store destination  
`OFFER`  
`SHARE`: from a page  
`PHOTO`  
`STATUS`: of a page  
`VIDEO`  
`APPLICATION`: app on Facebook

`object_url`

URL

URL that opens if someone clicks your link on a link ad. This URL is not connected to a Facebook page.

`page_welcome_message`

string

You can create more tailored user experiences for your ads that click to Messenger or to WhatsApp by customizing your ads' greeting message. For ads that clicks to Whatsapp, you can set the the page\_welcome\_message field under object\_story\_spec.

Note: If you are using the message received in Whatsapp to trigger any bot flows, please make sure to work with your BSP and agencies to update it so as to ensure flows aren't disrupted.

Supports Emoji

`place_page_set_id`

numeric string

The Place Page Set when objective is LOCAL\_AWARENESS. Used with Dynamic Local Ads

`platform_customizations`

JSON or object-like arrays

Use this field to specify the exact media to use on different Facebook [placements](/docs/marketing-api/targeting-specs/#placement). You can currently use this setting for images and videos. Facebook replaces the media originally defined in ad creative with this media when the ad displays in a specific placements. For example, if you define a media here for `instagram`, Facebook uses that media instead of the media defined in the ad creative when the ad appears on Instagram.

`instagram`

JSON or object-like arrays

Specify the media to display in an Instagram ad. This displays instead of the media defined in the ad creative.

`image_url`

URL

The URL of the image used for the platform specific media. Either this field or `image_hash` is required.

`image_hash`

string

The [ad image](/docs/marketing-api/reference/ad-image) used for the platform specific media. Either this field or `image_url` is required.

`image_crops`

dictionary { enum{191x100, 100x72, 400x150, 600x360, 100x100, 400x500, 90x160} : <list<list<int64>>> }

A JSON object defining crop dimensions for the image specified. See [Image Crops](/docs/marketing-api/image-crops) for more details.

`playable_asset_id`

numeric string

The ID of the playable asset in this creative.

`portrait_customizations`

JSON object

Use this field to customizations how ads look in portrait mode format example for IG Stories, Facebook Stories, IGTV, etc

`specifications`

array<JSON object>

specifications

`background_color`

JSON object

background\_color

Required

`bottom_color`

string

bottom\_color

Required

`top_color`

string

top\_color

Required

`carousel_delivery_mode`

enum {fixed\_num\_cards, optimal\_num\_cards}

carousel\_delivery\_mode

`product_set_id`

numeric string or integer

Used for [Dynamic Ad](/docs/marketing-api/dynamic-product-ads). An ID for a product set, which groups related products or other items being advertised.

`recommender_settings`

JSON object

The recommender settings that can be used to control recommendations for Dynamic Ads.

`preferred_events`

list<enum{ViewContent, Search, AddToCart, AddToWishlist, InitiateCheckout, AddPaymentInfo, Purchase, Subscribe, RecurringSubscriptionPayment, Lead, CompleteRegistration, CustomConversion, AggregateCustomConversion, Other}>

`product_sales_channel`

enum {ONLINE, IN\_STORE, OMNI}

`referral_id`

numeric string or integer

The ID of Referral Ad Configuration in this creative.

`regional_regulation_disclaimer_spec`

JSON object

regional\_regulation\_disclaimer\_spec

`taiwan_finserv`

JSON object

taiwan\_finserv

`taiwan_finserv_funder_id`

numeric string

taiwan\_finserv\_funder\_id

Required

`australia_finserv`

JSON object

australia\_finserv

`australia_finserv_beneficiary_id`

numeric string

australia\_finserv\_beneficiary\_id

Required

`australia_finserv_payer_id`

numeric string

australia\_finserv\_payer\_id

Required

`taiwan_universal`

JSON object

taiwan\_universal

`taiwan_universal_beneficiary_id`

numeric string

taiwan\_universal\_beneficiary\_id

Required

`taiwan_universal_payer_id`

numeric string

taiwan\_universal\_payer\_id

Required

`india_finserv`

JSON object

india\_finserv

`india_finserv_beneficiary_id`

numeric string

india\_finserv\_beneficiary\_id

Required

`india_finserv_payer_id`

numeric string

india\_finserv\_payer\_id

Required

`singapore_universal`

JSON object

singapore\_universal

`singapore_universal_beneficiary_id`

numeric string

singapore\_universal\_beneficiary\_id

Required

`singapore_universal_payer_id`

numeric string

singapore\_universal\_payer\_id

Required

`template_url`

URL

The product link url, which overrides the one set in [Dynamic Product Ad's](/docs/marketing-api/dynamic-product-ads) product feeds.

`template_url_spec`

string (TemplateURLSpec)

An optional structured collection of templated web and app-link descriptors that override the fallbacks that would otherwise be pulled from a Dynamic Ad\`s catalog

`android`

Object

`app_name`

string

`package`

string

`url`

string

`config`

Object

`app_id`

string

`third_party_app_id`

string

`ios`

Object

`app_name`

string

`app_store_id`

string

`url`

string

`ipad`

Object

`app_name`

string

`app_store_id`

string

`url`

string

`iphone`

Object

`app_name`

string

`app_store_id`

string

`url`

string

`windows_phone`

Object

`app_name`

string

`app_id`

string

`url`

string

`web`

Object

`url`

URL

`should_fallback`

string

`threads_user_id`

numeric string or integer

threads\_user\_id

`thumbnail_url`

URL

URL for a thumbnail image for this ad creative. You can provide dimensions for this with `thumbnail_width` and `thumbnail_height`. [See example](/docs/marketing-api/reference/ad-creative#thumbnail-example).

`title`

string

Title for a Page Likes ad, which appears in the right-hand column on Facebook.

`url_tags`

string

A set of query string parameters which will replace or be appended to urls clicked from page post ads, message of the post, and canvas app install creatives only.

`use_page_actor_override`

boolean

If `true`, we show the page actor for mobile app ads.

### Return Type

This endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.

Struct {

`id`: numeric string,

`success`: bool,

}

### Error Codes

Error

Description

100

Invalid parameter

200

Permissions error

500

Message contains banned content

1500

The url you supplied is invalid

80004

There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.

105

The number of parameters exceeded the maximum for this operation

368

The action attempted has been deemed abusive or is otherwise disallowed

194

Missing at least one required parameter

2635

You are calling a deprecated version of the Ads API. Please update to the latest version.

[](#)

## Updating

You can't perform this operation on this endpoint.

[](#)

## Deleting

You can't perform this operation on this endpoint.

[](#)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '675141479195042'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=675141479195042&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '574561515946252'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=574561515946252&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '1754628768090156'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=1754628768090156&ev=PageView&noscript=1)

!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f.\_fbq)f.\_fbq=n; n.push=n;n.loaded=!0;n.version='2.0';n.queue=\[\];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)\[0\];s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en\_US/fbevents.js'); fbq('init', '217404712025032'); fbq('track', "PageView");fbq('track', "PageView");

![](https://www.facebook.com/tr?id=217404712025032&ev=PageView&noscript=1)