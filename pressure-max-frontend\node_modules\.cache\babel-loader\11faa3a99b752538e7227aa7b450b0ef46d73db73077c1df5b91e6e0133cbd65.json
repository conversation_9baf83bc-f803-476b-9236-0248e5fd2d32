{"ast": null, "code": "'use strict';\n\nmodule.exports = function () {\n  throw new Error('ws does not work in the browser. Browser clients must use the native ' + 'WebSocket object');\n};", "map": {"version": 3, "names": ["module", "exports", "Error"], "sources": ["C:/Users/<USER>/node_modules/@supabase/realtime-js/node_modules/ws/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,MAAM,IAAIC,KAAK,CACb,uEAAuE,GACrE,kBACJ,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}