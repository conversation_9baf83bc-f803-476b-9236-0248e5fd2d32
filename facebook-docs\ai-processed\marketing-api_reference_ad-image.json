{"title": "Facebook Marketing API - Ad Image Reference", "summary": "Complete reference for uploading, managing, and using images in Facebook ad creatives through the Marketing API. Covers image upload methods, field specifications, and integration with ad creative workflows.", "content": "# Facebook Marketing API - Ad Image Reference\n\n## Overview\n\nUpload and manage images to later use in ad creative. Image formats, sizes and design guidelines depend on your type of ad. See [Ads Guide](https://www.facebook.com/business/ads-guide/) and [Image Crop](/docs/marketing-api/image-crops/) for more details.\n\nSupported image formats include `.bmp`, `.jpeg`, and `.gif`.\n\n## Basic Usage\n\n### Uploading an Image\n\n```php\nuse FacebookAds\\Object\\AdImage;\nuse FacebookAds\\Object\\Fields\\AdImageFields;\n\n$image = new AdImage(null, 'act_<AD_ACCOUNT_ID>');\n$image->{AdImageFields::FILENAME} = '<IMAGE_PATH>';\n\n$image->create();\necho 'Image Hash: '.$image->{AdImageFields::HASH}.PHP_EOL;\n```\n\n### Using Image in Ad Creative\n\n```php\nuse FacebookAds\\Object\\AdCreative;\nuse FacebookAds\\Object\\Ad;\nuse FacebookAds\\Object\\Fields\\AdCreativeFields;\nuse FacebookAds\\Object\\Fields\\AdFields;\n\n$creative = new AdCreative(null, 'act_<AD_ACCOUNT_ID>');\n$creative->setData(array(\n  AdCreativeFields::TITLE => 'My Test Creative',\n  AdCreativeFields::BODY => 'My Test Ad Creative Body',\n  AdCreativeFields::OBJECT_URL => 'https://www.facebook.com/facebook',\n  AdCreativeFields::IMAGE_HASH => '<IMAGE_HASH>',\n));\n\n$ad = new Ad(null, 'act_<AD_ACCOUNT_ID>');\n$ad->setData(array(\n  AdFields::NAME => 'My Ad',\n  AdFields::ADSET_ID => <AD_SET_ID>,\n  AdFields::CREATIVE => $creative,\n));\n$ad->create(array(\n  Ad::STATUS_PARAM_NAME => Ad::STATUS_PAUSED,\n));\n```\n\n## Reading Images\n\nImages can be specified in ad creatives by:\n- Image hash value of a previously uploaded image\n- Uploading the image at ad or ad creative creation time\n\n### Get Images by Ad Account\n\n```php\nuse FacebookAds\\Object\\AdAccount;\n\n$account = new AdAccount('act_<AD_ACCOUNT_ID>');\n$images = $account->getAdImages();\n```\n\n### Get Specific Images by Hash\n\n```php\nuse FacebookAds\\Object\\AdAccount;\n\n$account = new AdAccount('act_<AD_ACCOUNT_ID>');\n$images = $account->getAdImages(\n  array(),\n  array(\n    'hashes' => array(\n      '<IMAGE_1_HASH>',\n      '<IMAGE_2_HASH>',\n    ),\n  ));\n```\n\n## Fields\n\n| Field | Type | Description |\n|-------|------|-------------|\n| `id` | token | The ID of the image |\n| `account_id` | numeric string | The ad account that owns the image |\n| `created_time` | datetime | Time the image was created |\n| `creatives` | list<numeric string> | List of ad creative IDs using this image |\n| `hash` | string | Unique identifier hash for the image (Default field) |\n| `height` | unsigned int32 | Height of the image |\n| `is_associated_creatives_in_adgroups` | bool | Self explanatory |\n| `name` | string | Filename (max 100 characters) |\n| `original_height` | unsigned int32 | Original upload height |\n| `original_width` | unsigned int32 | Original upload width |\n| `permalink_url` | string | Permanent URL for story creatives |\n| `status` | enum | Status: ACTIVE, INTERNAL, DELETED |\n| `updated_time` | datetime | Time the image was updated |\n| `url` | string | Temporary URL (do not use in ad creative creation) |\n| `url_128` | string | Temporary URL for 128x128 resized version |\n| `width` | unsigned int32 | Width of the image |\n\n## Creating Images\n\n### Upload from Zip File\n\n```php\nuse FacebookAds\\Object\\AdImage;\nuse FacebookAds\\Object\\Fields\\AdImageFields;\n\n$images = AdImage::createFromZip('<ZIP_PATH>', 'act_<AD_ACCOUNT_ID>');\n\nforeach ($images as $image) {\n  echo $image->{AdImageFields::HASH}.PHP_EOL;\n}\n```\n\n### Upload from Bytes\n\n```bash\ncurl \\\n  -F 'bytes=iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fI...' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n\"https://graph.facebook.com/<API_VERSION>/act_<ACCOUNT_ID>/adimages\"\n```\n\n### Upload Image on Ad Create\n\n```bash\ncurl \\\n  -F 'campaign_id=<AD_SET_ID>' \\\n  -F 'creative={\"title\":\"test title\",\"body\":\"test\",\"object_url\":\"http://www.test.com\",\"image_file\":\"test.jpg\"}' \\\n  -F 'test.jpg=@test.jpg' \\\n  -F 'name=My ad' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n\"https://graph.facebook.com/<API_VERSION>/act_<ACCOUNT_ID>/ads\"\n```\n\n### Copy Images Between Accounts\n\n```bash\ncurl \\\n  -F 'copy_from={\"source_account_id\":\"<SOURCE_ACCOUNT_ID>\", \"hash\":\"02bee5277ec507b6fd0f9b9ff2f22d9c\"}' \\\n  -F 'access_token=<ACCESS_TOKEN>' \\\n\"https://graph.facebook.com/<API_VERSION>/act_<DESTINATION_ACCOUNT_ID>/adimages\"\n```\n\n### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `bytes` | Base64 UTF-8 string | Image file content in bytes format |\n| `copy_from` | JSON object | Copy image from source account: `{\"source_account_id\":\"ID\", \"hash\":\"hash\"}` |\n\n### Return Type\n\nReturns a map structure with image details including hash, URLs, dimensions, and name.\n\n## Deleting Images\n\nYou can only delete ad images **not currently being used** in an ad creative.\n\n```php\nuse FacebookAds\\Object\\AdImage;\nuse FacebookAds\\Object\\Fields\\AdImageFields;\n\n$image = new AdImage(<IMAGE_ID>, 'act_<AD_ACCOUNT_ID>');\n$image->{AdImageFields::HASH} = '<IMAGE_HASH>';\n$image->deleteSelf();\n```\n\n### Delete Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `hash` | string | Hash of the image to delete (Required) |\n\n## Error Codes\n\n| Error | Description |\n|-------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | Action deemed abusive or disallowed |\n| 2635 | Deprecated API version |\n| 80004 | Rate limit exceeded |\n\n## Notes\n\n- Images must include filename extensions (e.g., `sample.jpg`, not `sample`)\n- Temporary URLs should not be used in ad creative creation\n- Your app must have read access to source account when copying images\n- Images cannot be updated once uploaded\n- Maximum filename length is 100 characters", "keyPoints": ["Images must be uploaded before use in ad creatives and are referenced by unique hash values", "Supports multiple upload methods: direct file upload, zip files, bytes, and copying between accounts", "Images can only be deleted if not currently used in any ad creative", "Temporary URLs are provided but should not be used in ad creative creation", "Filename extensions are required and maximum filename length is 100 characters"], "apiEndpoints": ["/act_{ad_account_id}/adimages", "/act_{ad_account_id}/ads"], "parameters": ["bytes", "copy_from", "hash", "hashes", "source_account_id", "image_file", "filename"], "examples": ["PHP image upload with hash retrieval", "Using image hash in ad creative creation", "Reading images by ad account", "Reading specific images by hash", "Uploading from zip file", "Uploading via bytes with cURL", "Copying images between accounts", "Deleting unused images"], "tags": ["facebook-marketing-api", "ad-images", "image-upload", "ad-creative", "image-management", "api-reference"], "relatedTopics": ["Ad Creative", "Ads Guide", "Image Crop", "Ad Account", "Graph API", "Rate Limiting", "OAuth Access Tokens"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-image", "processedAt": "2025-06-25T15:42:35.137Z", "processor": "openrouter-claude-sonnet-4"}