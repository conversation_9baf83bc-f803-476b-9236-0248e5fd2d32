{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n  constructor(context) {\n    super(context.message);\n    this.name = 'PostgrestError';\n    this.details = context.details;\n    this.hint = context.hint;\n    this.code = context.code;\n  }\n}\nexports.default = PostgrestError;", "map": {"version": 3, "names": ["PostgrestError", "Error", "constructor", "context", "message", "name", "details", "hint", "code", "exports", "default"], "sources": ["C:\\Users\\<USER>\\node_modules\\@supabase\\postgrest-js\\src\\PostgrestError.ts"], "sourcesContent": ["/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nexport default class PostgrestError extends Error {\n  details: string\n  hint: string\n  code: string\n\n  constructor(context: { message: string; details: string; hint: string; code: string }) {\n    super(context.message)\n    this.name = 'PostgrestError'\n    this.details = context.details\n    this.hint = context.hint\n    this.code = context.code\n  }\n}\n"], "mappings": ";;;;;AAAA;;;;;AAKA,MAAqBA,cAAe,SAAQC,KAAK;EAK/CC,YAAYC,OAAyE;IACnF,KAAK,CAACA,OAAO,CAACC,OAAO,CAAC;IACtB,IAAI,CAACC,IAAI,GAAG,gBAAgB;IAC5B,IAAI,CAACC,OAAO,GAAGH,OAAO,CAACG,OAAO;IAC9B,IAAI,CAACC,IAAI,GAAGJ,OAAO,CAACI,IAAI;IACxB,IAAI,CAACC,IAAI,GAAGL,OAAO,CAACK,IAAI;EAC1B;;AAXFC,OAAA,CAAAC,OAAA,GAAAV,cAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}