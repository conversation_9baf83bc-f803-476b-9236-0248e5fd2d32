{"title": "Ad Account Delivery Estimate", "breadcrumbs": [], "content": "<div class=\"_1dyy\" id=\"u_0_l_o6\"><div class=\"_33zv _3e1u\"><div class=\"_33zw\" tabindex=\"0\" role=\"button\">On This Page<div><i class=\"img sp_zD_MvjcHflF sx_a3c10c\" alt=\"\" data-visualcompletion=\"css-img\"></i><i class=\"hidden_elem img sp_zD_MvjcHflF sx_6874ff\" alt=\"\" data-visualcompletion=\"css-img\"></i></div></div><div class=\"_5-24 hidden_elem\"><a href=\"#overview\">Ad Account Delivery Estimate</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Reading\">Reading</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#example\">Example</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#parameters\">Parameters</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#fields\">Fields</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#error-codes\">Error Codes</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Creating\">Creating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Updating\">Updating</a></div><div class=\"_5-24 hidden_elem\"><a href=\"#Deleting\">Deleting</a></div></div></div><div id=\"documentation_body_pagelet\" data-referrer=\"documentation_body_pagelet\"><div class=\"_34yh\" id=\"u_0_1_Eq\"><div data-click-area=\"main\"><div class=\"_4-u2 _57mb _1u44 _4-u8\"><div class=\"_4-u3 _5rva _mog\"><div class=\"clearfix\"><span class=\"lfloat _ohe _c24 _50f4 _50f7\"><span><span class=\"_2iem\">Graph API Version</span></span></span><div class=\"_5s5u rfloat _ohf\"><span><div class=\"_6a _6b\"><div class=\"_6a _6b uiPopover\" id=\"u_0_2_Ar\"><a role=\"button\" class=\"_42ft _4jy0 _55pi _5vto _55_p _2agf _4o_4 _p _4jy3 _517h _51sy\" href=\"#\" style=\"max-width:200px;\" aria-haspopup=\"true\" aria-expanded=\"false\" rel=\"toggle\" id=\"u_0_3_h+\"><span class=\"_55pe\">v23.0</span><span class=\"_4o_3 _3-99\"><i class=\"img sp_WbXBGqjC54o sx_514a5c\"></i></span></a></div><input type=\"hidden\" autocomplete=\"off\" name=\"\" id=\"u_0_4_ju\"></div></span></div></div></div></div><div class=\"_1xb4 _3-98\"><div class=\"_4-u2 _57mb _1u44 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h1 id=\"overview\">Ad Account Delivery Estimate</h1><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _3fw6 _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><p>Returns the delivery estimate for a given ad set configuration in this ad account. You are not able to retrieve this field for <a href=\"/docs/marketing-api/lookalike-audience-targeting#inactive\">inactive Lookalike Audiences</a>.</p>\n<div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Reading\">Reading</h2><div class=\"_844_\"><div><div><p>Delivery estimate for a given ad set configuration in this ad account.</p>\n</div><div><h3 id=\"example\">Example</h3><div class=\"_5z09\"><div class=\"_51xa _5gt2 _51xb\" id=\"u_0_5_da\"><button value=\"1\" class=\"_42ft _51tl selected _42fs\" type=\"submit\" id=\"u_0_6_e0\">HTTP</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_7_2G\">PHP SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_8_uh\">JavaScript SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_9_Ni\">Android SDK</button><button value=\"1\" class=\"_42ft _51tl selected\" type=\"submit\" id=\"u_0_a_JP\">iOS SDK</button><a role=\"button\" class=\"_42ft _51tl selected\" href=\"/tools/explorer/?method=GET&amp;path=%7Bad-account-id%7D%2Fdelivery_estimate&amp;version=v23.0\" target=\"_blank\">Graph API Explorer<i class=\"_3-99 img sp_c_epTrfICMy sx_7b2121\"></i></a></div><div class=\"_xmu\"><pre class=\"_5gt1 prettyprint prettyprinted\" id=\"u_0_b_yt\" style=\"\"><code><span class=\"pln\">GET </span><span class=\"pun\">/</span><span class=\"pln\">v23</span><span class=\"pun\">.</span><span class=\"lit\">0</span><span class=\"pun\">/{</span><span class=\"pln\">ad</span><span class=\"pun\">-</span><span class=\"pln\">account</span><span class=\"pun\">-</span><span class=\"pln\">id</span><span class=\"pun\">}/</span><span class=\"pln\">delivery_estimate HTTP</span><span class=\"pun\">/</span><span class=\"lit\">1.1</span><span class=\"pln\">\n</span><span class=\"typ\">Host</span><span class=\"pun\">:</span><span class=\"pln\"> graph</span><span class=\"pun\">.</span><span class=\"pln\">facebook</span><span class=\"pun\">.</span><span class=\"pln\">com</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_c_nN\" style=\"\"><code><span class=\"com\">/* PHP SDK v5.0.0 */</span><span class=\"pln\">\n</span><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">try</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  </span><span class=\"com\">// Returns a `Facebook\\FacebookResponse` object</span><span class=\"pln\">\n  $response </span><span class=\"pun\">=</span><span class=\"pln\"> $fb</span><span class=\"pun\">-&gt;</span><span class=\"kwd\">get</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">'/{ad-account-id}/delivery_estimate'</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"str\">'{access-token}'</span><span class=\"pln\">\n  </span><span class=\"pun\">);</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookResponseException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Graph returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\"> </span><span class=\"kwd\">catch</span><span class=\"pun\">(</span><span class=\"typ\">Facebook</span><span class=\"pln\">\\Exceptions\\FacebookSDKException $e</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n  echo </span><span class=\"str\">'Facebook SDK returned an error: '</span><span class=\"pln\"> </span><span class=\"pun\">.</span><span class=\"pln\"> $e</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getMessage</span><span class=\"pun\">();</span><span class=\"pln\">\n  </span><span class=\"kwd\">exit</span><span class=\"pun\">;</span><span class=\"pln\">\n</span><span class=\"pun\">}</span><span class=\"pln\">\n$graphNode </span><span class=\"pun\">=</span><span class=\"pln\"> $response</span><span class=\"pun\">-&gt;</span><span class=\"pln\">getGraphNode</span><span class=\"pun\">();</span><span class=\"pln\">\n</span><span class=\"com\">/* handle the result */</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_d_km\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\nFB</span><span class=\"pun\">.</span><span class=\"pln\">api</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"str\">\"/{ad-account-id}/delivery_estimate\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">function</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n      </span><span class=\"kwd\">if</span><span class=\"pln\"> </span><span class=\"pun\">(</span><span class=\"pln\">response </span><span class=\"pun\">&amp;&amp;</span><span class=\"pln\"> </span><span class=\"pun\">!</span><span class=\"pln\">response</span><span class=\"pun\">.</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n      </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">);</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_e_Dl\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">(</span><span class=\"pln\">\n    </span><span class=\"typ\">AccessToken</span><span class=\"pun\">.</span><span class=\"pln\">getCurrentAccessToken</span><span class=\"pun\">(),</span><span class=\"pln\">\n    </span><span class=\"str\">\"/{ad-account-id}/delivery_estimate\"</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">null</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"typ\">HttpMethod</span><span class=\"pun\">.</span><span class=\"pln\">GET</span><span class=\"pun\">,</span><span class=\"pln\">\n    </span><span class=\"kwd\">new</span><span class=\"pln\"> </span><span class=\"typ\">GraphRequest</span><span class=\"pun\">.</span><span class=\"typ\">Callback</span><span class=\"pun\">()</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n        </span><span class=\"kwd\">public</span><span class=\"pln\"> </span><span class=\"kwd\">void</span><span class=\"pln\"> onCompleted</span><span class=\"pun\">(</span><span class=\"typ\">GraphResponse</span><span class=\"pln\"> response</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n            </span><span class=\"com\">/* handle the result */</span><span class=\"pln\">\n        </span><span class=\"pun\">}</span><span class=\"pln\">\n    </span><span class=\"pun\">}</span><span class=\"pln\">\n</span><span class=\"pun\">).</span><span class=\"pln\">executeAsync</span><span class=\"pun\">();</span></code></pre><pre class=\"_5gt1 prettyprint prettyprinted hidden_elem\" id=\"u_0_f_hM\" style=\"\"><code><span class=\"com\">/* make the API call */</span><span class=\"pln\">\n</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">request </span><span class=\"pun\">=</span><span class=\"pln\"> </span><span class=\"pun\">[[</span><span class=\"typ\">FBSDKGraphRequest</span><span class=\"pln\"> alloc</span><span class=\"pun\">]</span><span class=\"pln\">\n                               initWithGraphPath</span><span class=\"pun\">:@</span><span class=\"str\">\"/{ad-account-id}/delivery_estimate\"</span><span class=\"pln\">\n                                      parameters</span><span class=\"pun\">:</span><span class=\"kwd\">params</span><span class=\"pln\">\n                                      </span><span class=\"typ\">HTTPMethod</span><span class=\"pun\">:@</span><span class=\"str\">\"GET\"</span><span class=\"pun\">];</span><span class=\"pln\">\n</span><span class=\"pun\">[</span><span class=\"pln\">request startWithCompletionHandler</span><span class=\"pun\">:^(</span><span class=\"typ\">FBSDKGraphRequestConnection</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">connection</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      id result</span><span class=\"pun\">,</span><span class=\"pln\">\n                                      </span><span class=\"typ\">NSError</span><span class=\"pln\"> </span><span class=\"pun\">*</span><span class=\"pln\">error</span><span class=\"pun\">)</span><span class=\"pln\"> </span><span class=\"pun\">{</span><span class=\"pln\">\n    </span><span class=\"com\">// Handle the result</span><span class=\"pln\">\n</span><span class=\"pun\">}];</span></code></pre></div></div>If you want to learn how to use the Graph API, read our <a href=\"/docs/graph-api/using-graph-api/\">Using Graph API guide</a>.</div><div><h3 id=\"parameters\">Parameters</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Parameter</th><th>Description</th></tr></thead><tbody></tbody><tbody class=\"_5m37\" id=\"u_0_g_Dm\"><tr class=\"row_0\"><td><div class=\"_yc\"><span><code>optimization_goal</code></span></div><div class=\"_yb\">enum{NONE, APP_INSTALLS, AD_RECALL_LIFT, ENGAGED_USERS, EVENT_RESPONSES, IMPRESSIONS, LEAD_GENERATION, QUALITY_LEAD, LINK_CLICKS, OFFSITE_CONVERSIONS, PAGE_LIKES, POST_ENGAGEMENT, QUALITY_CALL, REACH, LANDING_PAGE_VIEWS, VISIT_INSTAGRAM_PROFILE, VALUE, THRUPLAY, DERIVED_EVENTS, APP_INSTALLS_AND_OFFSITE_CONVERSIONS, CONVERSATIONS, IN_APP_VALUE, MESSAGING_PURCHASE_CONVERSION, SUBSCRIBERS, REMINDERS_SET, MEANINGFUL_CALL_ATTEMPT, PROFILE_VISIT, PROFILE_AND_PAGE_ENGAGEMENT, ADVERTISER_SILOED_VALUE, AUTOMATIC_OBJECTIVE, MESSAGING_APPOINTMENT_CONVERSION}</div></td><td><p class=\"_yd\"></p><div><div><p>The optimization goal that you want the estimate for. You only get action predictions for optimization goals where actions make sense. See <a href=\"/docs/marketing-api/reference/ad-campaign\">optimization_goals</a> for allowed values.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1 _5m29 _5m27\"><td><div class=\"_yc\"><span><code>promoted_object</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div><p>The promoted object for the ad set that you want an estimate for. This is the same format as when you <a href=\"/docs/marketing-api/reference/ad-campaign\">create an ad set</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>application_id</code></span></div><div class=\"_yb\">int</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Application. Usually related to mobile or canvas games being promoted on Facebook for installs or engagement</p>\n</div></div><p></p></td></tr><tr class=\"row_1-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>pixel_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook conversion pixel.  Used with offsite conversion campaigns.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>custom_event_type</code></span></div><div class=\"_yb\">enum{AD_IMPRESSION, RATE, TUTORIAL_COMPLETION, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, ADD_TO_CART, ADD_TO_WISHLIST, INITIATED_CHECKOUT, ADD_PAYMENT_INFO, PURCHASE, LEAD, COMPLETE_REGISTRATION, CONTENT_VIEW, SEARCH, SERVICE_BOOKING_REQUEST, MESSAGING_CONVERSATION_STARTED_7D, LEVEL_ACHIEVED, ACHIEVEMENT_UNLOCKED, SPENT_CREDITS, LISTING_INTERACTION, D2_RETENTION, D7_RETENTION, OTHER}</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-3 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>object_store_url</code></span></div><div class=\"_yb\">URL</div></td><td><p class=\"_yd\"></p><div><div><p>The uri of the mobile / digital store where an application can be bought / downloaded. This is platform specific. When combined with the \"application_id\" this uniquely specifies an object which can be the subject of a Facebook advertising campaign.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-4 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>offer_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of an Offer from a Facebook Page.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-5 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>page_id</code></span></div><div class=\"_yb\">Page ID</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Page</p>\n</div></div><p></p></td></tr><tr class=\"row_1-6 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_catalog_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Product Catalog. Used with\n      <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Product Ads</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-7 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_item_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the product item.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-8 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>instagram_profile_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the instagram profile id.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-9 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Product Set within an Ad Set level Product\n      Catalog. Used with\n      <a href=\"/docs/marketing-api/dynamic-product-ads\">Dynamic Product Ads</a>.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-10 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>event_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Facebook Event</p>\n</div></div><p></p></td></tr><tr class=\"row_1-11 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>offline_conversion_data_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the offline dataset.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-12 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>fundraiser_campaign_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the fundraiser campaign.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-13 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>custom_event_str</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-14 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>mcme_conversion_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a MCME conversion.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-15 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>conversion_goal_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Conversion Goal.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-16 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>offsite_conversion_event_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of a Offsite Conversion Event</p>\n</div></div><p></p></td></tr><tr class=\"row_1-17 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>boosted_product_set_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div><p>The ID of the Boosted Product Set within an Ad Set level Product\n      Catalog. Should only be present when the advertiser has\n      opted into Product Set Boosting.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-18 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_form_event_source_type</code></span></div><div class=\"_yb\">enum{inferred, offsite_crm, offsite_web, onsite_crm, onsite_crm_single_event, onsite_web, onsite_p2b_call, onsite_messaging}</div></td><td><p class=\"_yd\"></p><div><div><p>The event source of lead ads form.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-19 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_custom_event_type</code></span></div><div class=\"_yb\">enum{AD_IMPRESSION, RATE, TUTORIAL_COMPLETION, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, ADD_TO_CART, ADD_TO_WISHLIST, INITIATED_CHECKOUT, ADD_PAYMENT_INFO, PURCHASE, LEAD, COMPLETE_REGISTRATION, CONTENT_VIEW, SEARCH, SERVICE_BOOKING_REQUEST, MESSAGING_CONVERSATION_STARTED_7D, LEVEL_ACHIEVED, ACHIEVEMENT_UNLOCKED, SPENT_CREDITS, LISTING_INTERACTION, D2_RETENTION, D7_RETENTION, OTHER}</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-20 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_custom_event_str</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div><p>The event from an App Event of a mobile app,\n    not in the standard event list.</p>\n</div></div><p></p></td></tr><tr class=\"row_1-21 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>lead_ads_offsite_conversion_type</code></span></div><div class=\"_yb\">enum{default, clo}</div></td><td><p class=\"_yd\"></p><div><div><p>The offsite conversion type for lead ads</p>\n</div></div><p></p></td></tr><tr class=\"row_1-22 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>value_semantic_type</code></span></div><div class=\"_yb\">enum {VALUE, MARGIN, LIFETIME_VALUE}</div></td><td><p class=\"_yd\"></p><div><div><p>The semantic of the event value to be using for optimization</p>\n</div></div><p></p></td></tr><tr class=\"row_1-23 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>variation</code></span></div><div class=\"_yb\">enum {OMNI_CHANNEL_SHOP_AUTOMATIC_DATA_COLLECTION, PRODUCT_SET_AND_APP, PRODUCT_SET_AND_IN_STORE, PRODUCT_SET_AND_OMNICHANNEL, PRODUCT_SET_AND_PHONE_CALL, PRODUCT_SET_AND_WEBSITE, PRODUCT_SET_WEBSITE_APP_AND_INSTORE}</div></td><td><p class=\"_yd\"></p><div><div><p>Variation of the promoted object for a PCA ad</p>\n</div></div><p></p></td></tr><tr class=\"row_1-24 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>product_set_optimization</code></span></div><div class=\"_yb\">enum{enabled, disabled}</div></td><td><p class=\"_yd\"></p><div><div><p>Enum defining whether or not the ad should be optimized for the promoted product set</p>\n</div></div><p></p></td></tr><tr class=\"row_1-25 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>full_funnel_objective</code></span></div><div class=\"_yb\">enum{6, 8, 12, 14, 15, 19, 24, 26, 29, 31, 32, 35, 36, 37, 39, 41, 42, 40, 43, 44, 46}</div></td><td><p class=\"_yd\"></p><div><div><p>Enum defining the full funnel objective of the campaign</p>\n</div></div><p></p></td></tr><tr class=\"row_1-26 _5m29 hidden_elem _5m27\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>omnichannel_object</code></span></div><div class=\"_yb\">Object</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_1-26-0 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>app</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_1-26-1 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>pixel</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr><tr class=\"row_1-26-2 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel2\"><div class=\"_yc\"><span><code>onsite</code></span></div><div class=\"_yb\">array&lt;JSON object&gt;</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_1-27 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>whats_app_business_phone_number_id</code></span></div><div class=\"_yb\">numeric string or integer</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_1-28 _5m29 hidden_elem\"><td class=\"devsiteReferenceTableRowLevel1\"><div class=\"_yc\"><span><code>whatsapp_phone_number</code></span></div><div class=\"_yb\">string</div></td><td><p class=\"_yd\"></p><div><div></div></div><p></p></td></tr><tr class=\"row_2\"><td><div class=\"_yc\"><span><code>targeting_spec</code></span></div><div class=\"_yb\">Targeting object</div></td><td><p class=\"_yd\"></p><div><div><p>The targeting specification for delivery estimate. See <a href=\"/docs/marketing-api/targeting-specs\">Advanced Targeting and Placement</a>.</p>\n</div></div><p></p><div class=\"_3-8w\"><span class=\"_1vet\">Required</span></div></td></tr></tbody></table></div></div><div><h3 id=\"fields\">Fields</h3><p>Reading from this edge will return a JSON formatted result:</p><pre class=\"_3hux\"><p>{\n    \"<code>data</code>\": [],\n    \"<code>paging</code>\": {}\n}</p>\n</pre><div class=\"_3-8o\"><h4><code>data</code></h4>A list of AdAccountDeliveryEstimate nodes.</div><div class=\"_3-8o\"><h4><code>paging</code></h4>For more details about pagination, see the <a href=\"/docs/graph-api/using-graph-api/#paging\">Graph API guide</a>.</div></div><h3 id=\"error-codes\">Error Codes</h3><div class=\"_57-c\"><table class=\"_4-ss _5k9x\"><thead><tr><th>Error</th><th>Description</th></tr></thead><tbody><tr><td>100</td><td>Invalid parameter</td></tr><tr><td>190</td><td>Invalid OAuth 2.0 Access Token</td></tr><tr><td>2641</td><td>Your ad includes or excludes locations that are currently restricted</td></tr><tr><td>613</td><td>Calls to this api have exceeded the rate limit.</td></tr><tr><td>200</td><td>Permissions error</td></tr><tr><td>80004</td><td>There have been too many calls to this ad-account. Wait a bit and try again. For more info, please refer to https://developers.facebook.com/docs/graph-api/overview/rate-limiting#ads-management.</td></tr><tr><td>2635</td><td>You are calling a deprecated version of the Ads API. Please update to the latest version.</td></tr><tr><td>368</td><td>The action attempted has been deemed abusive or is otherwise disallowed</td></tr><tr><td>900</td><td>No such application exists.</td></tr><tr><td>105</td><td>The number of parameters exceeded the maximum for this operation</td></tr></tbody></table></div></div></div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Creating\">Creating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Updating\">Updating</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div><div class=\"_4-u2 _57mb _1u44 _2pig _4-u8 _3la3\"><div class=\"_4-u3 _588p\"><h2 id=\"Deleting\">Deleting</h2><div class=\"_844_\">You can't perform this operation on this endpoint.</div><div data-click-area=\"to_top_nav\"><a class=\"_2k32\" href=\"#\"><i alt=\"\" data-visualcompletion=\"css-img\" class=\"img sp_zD_MvjcHflF sx_c72b6b\"></i></a></div></div></div></div><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '675141479195042');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=675141479195042&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '574561515946252');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=574561515946252&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '1754628768090156');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=1754628768090156&amp;ev=PageView&amp;noscript=1\" /></noscript><script nonce=\"\">\n!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?\nn.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;\nn.push=n;n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;\nt.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,\ndocument,'script','https://connect.facebook.net/en_US/fbevents.js');\n\nfbq('init', '***************');\nfbq('track', \"PageView\");fbq('track', \"PageView\");</script><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&amp;ev=PageView&amp;noscript=1\" /></noscript></div></div></div>", "navigationLinks": ["/docs/marketing-apis", "/docs/marketing-api/reference/", "/docs/marketing-api/reference/ad-account/", "/docs/marketing-api/reference/ad-account/delivery_estimate", "/docs/marketing-apis/overview", "/docs/marketing-api/get-started", "/docs/marketing-api/creative", "/docs/marketing-api/bidding", "/docs/marketing-api/ad-rules", "/docs/marketing-api/audiences", "/docs/marketing-api/insights", "/docs/marketing-api/brand-safety-and-suitability", "/docs/marketing-api/best-practices", "/docs/marketing-api/troubleshooting", "/docs/marketing-api/reference", "/docs/marketing-api/reference/ad-account", "/docs/marketing-api/reference/ad-account/account_controls/", "/docs/marketing-api/reference/ad-account/activities/", "/docs/marketing-api/reference/ad-account/ad_place_page_sets/", "/docs/marketing-api/reference/ad-account/adcreatives/", "/docs/marketing-api/reference/ad-account/adimages/", "/docs/marketing-api/reference/ad-account/adlabels/", "/docs/marketing-api/reference/ad-account/adplayables/", "/docs/marketing-api/reference/ad-account/adrules_library/", "/docs/marketing-api/reference/ad-account/ads/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_reports/", "/docs/marketing-api/reference/ad-account/ads_reporting_mmm_schedulers/", "/docs/marketing-api/reference/ad-account/adsets/", "/docs/marketing-api/reference/ad-account/adspixels/", "/docs/marketing-api/reference/ad-account/advertisable_applications/", "/docs/marketing-api/reference/ad-account/advideos/", "/docs/marketing-api/reference/ad-account/agencies/", "/docs/marketing-api/reference/ad-account/applications/", "/docs/marketing-api/reference/ad-account/assigned_users/", "/docs/marketing-api/reference/ad-account/async_batch_requests/", "/docs/marketing-api/reference/ad-account/asyncadcreatives/", "/docs/marketing-api/reference/ad-account/asyncadrequestsets/", "/docs/marketing-api/reference/ad-account/broadtargetingcategories/", "/docs/marketing-api/reference/ad-account/campaigns/", "/docs/marketing-api/reference/ad-account/customaudiences/", "/docs/marketing-api/reference/ad-account/customaudiencestos/", "/docs/marketing-api/reference/ad-account/customconversions/", "/docs/marketing-api/reference/ad-account/delivery_estimate/", "/docs/marketing-api/reference/ad-account/deprecatedtargetingadsets/", "/docs/marketing-api/reference/ad-account/dsa_recommendations/", "/docs/marketing-api/reference/ad-account/impacting_ad_studies/", "/docs/marketing-api/reference/ad-account/insights/", "/docs/marketing-api/reference/ad-account/instagram_accounts/", "/docs/marketing-api/reference/ad-account/mcmeconversions/", "/docs/marketing-api/reference/ad-account/minimum_budgets/", "/docs/marketing-api/reference/ad-account/product_audiences/", "/docs/marketing-api/reference/ad-account/promote_pages/", "/docs/marketing-api/reference/ad-account/publisher_block_lists/", "/docs/marketing-api/reference/ad-account/reachestimate/", "/docs/marketing-api/reference/ad-account/reachfrequencypredictions/", "/docs/marketing-api/reference/ad-account/saved_audiences/", "/docs/marketing-api/reference/ad-account/subscribed_apps/", "/docs/marketing-api/reference/ad-account/targetingbrowse/", "/docs/marketing-api/reference/ad-account/targetingsearch/", "/docs/marketing-api/reference/ad-account/tracking/", "/docs/marketing-api/adcreative", "/docs/marketing-api/reference/ad-image", "/docs/marketing-api/generatepreview", "/docs/marketing-api/ad-preview-plugin", "/docs/marketing-api/reference/business", "/docs/marketing-api/reference/business-role-request", "/docs/marketing-api/reference/business-user", "/docs/marketing-api/currencies", "/docs/marketing-api/reference/high-demand-period", "/docs/marketing-api/image-crops", "/docs/marketing-api/reference/product-catalog", "/docs/marketing-api/reference/system-user", "/docs/marketing-api/marketing-api-changelog", "/docs/marketing-api/lookalike-audience-targeting#inactive", "/docs/marketing-api/reference/ad-campaign", "/docs/marketing-api/dynamic-product-ads", "/docs/marketing-api/targeting-specs"], "url": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/delivery_estimate/", "timestamp": "2025-06-25T15:27:24.022Z", "reprocessedAt": "2025-06-25T16:27:26.839Z", "reprocessedWith": "claude-sonnet-4"}