{"title": "Facebook Marketing API - Ad Account Publisher Block Lists", "summary": "This endpoint allows creating publisher block lists for ad accounts to control where ads are displayed. Only POST operations are supported for creating new block lists with a name parameter.", "content": "# Ad Account Publisher Block Lists\n\n*Graph API Version: v23.0*\n\n## Overview\n\nThe Publisher Block Lists endpoint allows you to manage lists of publishers where you don't want your ads to appear. This endpoint only supports creating new block lists.\n\n## Supported Operations\n\n### Reading\n❌ **Not supported** - You can't perform read operations on this endpoint.\n\n### Creating\n✅ **Supported** - Create new publisher block lists\n\n**Endpoint:** `POST /act_{ad_account_id}/publisher_block_lists`\n\nWhen posting to this edge, a [PublisherBlockList](/docs/marketing-api/reference/publisher-block-list/) will be created.\n\n#### Parameters\n\n| Parameter | Type | Description |\n|-----------|------|-------------|\n| `name` | string | Name of the block list |\n\n#### Return Type\n\nThis endpoint supports [read-after-write](/docs/graph-api/advanced/#read-after-write) and will read the node represented by `id` in the return type.\n\n```json\n{\n  \"id\": \"numeric string\"\n}\n```\n\n#### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 200 | Permissions error |\n\n### Updating\n❌ **Not supported** - You can't perform update operations on this endpoint.\n\n### Deleting\n❌ **Not supported** - You can't perform delete operations on this endpoint.", "keyPoints": ["Only POST (create) operations are supported for publisher block lists", "Requires a name parameter to create a new block list", "Returns a numeric string ID upon successful creation", "Supports read-after-write functionality", "Read, update, and delete operations are not available"], "apiEndpoints": ["POST /act_{ad_account_id}/publisher_block_lists"], "parameters": ["name (string) - Name of the block list"], "examples": [], "tags": ["Facebook Marketing API", "Publisher Block Lists", "Ad Account", "POST endpoint", "Graph API v23.0"], "relatedTopics": ["PublisherBlockList reference", "Ad Account management", "Publisher controls", "Read-after-write functionality", "Graph API advanced features"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/publisher_block_lists/", "processedAt": "2025-06-25T15:37:52.039Z", "processor": "openrouter-claude-sonnet-4"}