# Facebook Marketing API - Ad Account Custom Conversions Reference

## Summary
Complete reference documentation for managing custom conversions in Facebook Ad Accounts through the Marketing API. Covers reading, creating, and managing custom conversion events from event sources like Meta Pixel for ad optimization and measurement.

## Key Points
- Custom conversions track events from sources like Meta Pixel for ad measurement and optimization
- Reading custom conversions requires no parameters and returns paginated CustomConversion nodes
- Creating custom conversions requires only a name parameter, with many optional configuration options
- The endpoint supports various event types and action source types for flexible conversion tracking
- Update and delete operations are not supported on this endpoint

## API Endpoints
- `GET /v23.0/{ad-account-id}/customconversions`
- `POST /act_{ad_account_id}/customconversions`

## Parameters
- action_source_type
- advanced_rule
- custom_event_type
- default_conversion_value
- description
- event_source_id
- name
- rule

## Content
# Ad Account Custom Conversions

Data on custom conversion events from event sources, such as a Meta Pixel. You can query this data to measure the effectiveness of ads or use it to optimize ad delivery to target people who converted as defined by your customization and rules.

## Reading Custom Conversions

### Endpoint
```
GET /v23.0/{ad-account-id}/customconversions
```

### Parameters
This endpoint doesn't have any parameters.

### Response Format
```json
{
  "data": [],
  "paging": {}
}
```

#### Response Fields
- **data**: A list of CustomConversion nodes
- **paging**: Pagination information (see Graph API guide for details)

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/customconversions HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/customconversions',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/customconversions",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

### Error Codes (Reading)
- **200**: Permissions error
- **100**: Invalid parameter
- **80004**: Too many calls to this ad-account. Rate limiting applied.
- **190**: Invalid OAuth 2.0 Access Token
- **368**: Action deemed abusive or disallowed

## Creating Custom Conversions

### Endpoint
```
POST /act_{ad_account_id}/customconversions
```

### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `action_source_type` | enum | Action source type: app, chat, email, other, phone_call, physical_store, system_generated, website, business_messaging | No |
| `advanced_rule` | string | Advanced ruleset allowing multiple sources | No |
| `custom_event_type` | enum | Event type: ADD_PAYMENT_INFO, ADD_TO_CART, ADD_TO_WISHLIST, COMPLETE_REGISTRATION, CONTENT_VIEW, INITIATED_CHECKOUT, LEAD, PURCHASE, SEARCH, CONTACT, CUSTOMIZE_PRODUCT, DONATE, FIND_LOCATION, SCHEDULE, START_TRIAL, SUBMIT_APPLICATION, SUBSCRIBE, LISTING_INTERACTION, FACEBOOK_SELECTED, OTHER | No |
| `default_conversion_value` | float | Default conversion value (default: 0) | No |
| `description` | string | Description of the conversion | No |
| `event_source_id` | numeric string/integer | Event source ID (Pixel, offline event sets, etc.) | No |
| `name` | string | Name of the conversion | **Yes** |
| `rule` | string | Rule that events must fulfill to count as custom conversion | No |

### Return Type
Supports read-after-write and returns:
```json
{
  "id": "numeric string",
  "is_custom_event_type_predicted": "numeric string"
}
```

### Error Codes (Creating)
- **200**: Permissions error
- **100**: Invalid parameter

## Updating and Deleting

Both updating and deleting operations are **not supported** on this endpoint.

## API Version
Current version: **v23.0**

## Examples
HTTP GET request for reading custom conversions

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest example

iOS SDK FBSDKGraphRequest example

---
**Tags:** Facebook Marketing API, Custom Conversions, Ad Account, Meta Pixel, Event Tracking, API Reference  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/customconversions/  
**Processed:** 2025-06-25T16:26:55.612Z