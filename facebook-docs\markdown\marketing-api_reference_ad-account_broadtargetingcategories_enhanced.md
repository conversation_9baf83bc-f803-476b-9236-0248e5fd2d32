# Facebook Marketing API - Ad Account Broad Targeting Categories

## Summary
This endpoint allows you to retrieve broad targeting categories from a Facebook Ad Account. It provides read-only access to targeting categories that can be used for ad targeting, with an option to filter for custom categories only.

## Key Points
- Read-only endpoint for retrieving broad targeting categories from an Ad Account
- Supports filtering with custom_categories_only parameter to return only custom categories
- Returns paginated results with BroadTargetingCategories nodes
- Only GET operations are supported - no create, update, or delete functionality
- Requires proper permissions to access ad account data

## API Endpoints
- `GET /v23.0/{ad-account-id}/broadtargetingcategories`

## Parameters
- custom_categories_only (boolean) - Filter for custom categories only
- ad-account-id (path parameter) - The ID of the ad account

## Content
# Ad Account Broad Targeting Categories

The broad targeting categories endpoint provides access to targeting categories available for a specific Ad Account in the Facebook Marketing API.

## Reading

Retrieve the broad targeting categories from an Ad Account.

### Endpoint
```
GET /v23.0/{ad-account-id}/broadtargetingcategories
```

### Parameters

| Parameter | Type | Description |
|-----------|------|--------------|
| `custom_categories_only` | boolean | If `true`, returns only custom categories |

### Response Format

The response returns a JSON formatted result:

```json
{
  "data": [],
  "paging": {}
}
```

#### Response Fields

- **data**: A list of BroadTargetingCategories nodes
- **paging**: Pagination information for navigating through results

### Code Examples

#### HTTP Request
```http
GET /v23.0/{ad-account-id}/broadtargetingcategories HTTP/1.1
Host: graph.facebook.com
```

#### PHP SDK
```php
/* PHP SDK v5.0.0 */
try {
  $response = $fb->get(
    '/{ad-account-id}/broadtargetingcategories',
    '{access-token}'
  );
} catch(Facebook\Exceptions\FacebookResponseException $e) {
  echo 'Graph returned an error: ' . $e->getMessage();
  exit;
} catch(Facebook\Exceptions\FacebookSDKException $e) {
  echo 'Facebook SDK returned an error: ' . $e->getMessage();
  exit;
}
$graphNode = $response->getGraphNode();
```

#### JavaScript SDK
```javascript
FB.api(
    "/{ad-account-id}/broadtargetingcategories",
    function (response) {
      if (response && !response.error) {
        /* handle the result */
      }
    }
);
```

#### Android SDK
```java
new GraphRequest(
    AccessToken.getCurrentAccessToken(),
    "/{ad-account-id}/broadtargetingcategories",
    null,
    HttpMethod.GET,
    new GraphRequest.Callback() {
        public void onCompleted(GraphResponse response) {
            /* handle the result */
        }
    }
).executeAsync();
```

#### iOS SDK
```objc
FBSDKGraphRequest *request = [[FBSDKGraphRequest alloc]
                               initWithGraphPath:@"/{ad-account-id}/broadtargetingcategories"
                                      parameters:params
                                      HTTPMethod:@"GET"];
[request startWithCompletionHandler:^(FBSDKGraphRequestConnection *connection,
                                      id result,
                                      NSError *error) {
    // Handle the result
}];
```

### Error Codes

| Error Code | Description |
|------------|-------------|
| 200 | Permissions error |

## Limitations

- **Creating**: Not supported on this endpoint
- **Updating**: Not supported on this endpoint  
- **Deleting**: Not supported on this endpoint

This endpoint is read-only and only supports GET operations.

## Examples
HTTP GET request to retrieve broad targeting categories

PHP SDK implementation with error handling

JavaScript SDK API call

Android SDK GraphRequest implementation

iOS SDK FBSDKGraphRequest implementation

---
**Tags:** Facebook Marketing API, Ad Account, Targeting, Broad Targeting Categories, Graph API, Read-only endpoint  
**Difficulty:** intermediate  
**Content Type:** reference  
**Source:** https://developers.facebook.com/docs/marketing-api/reference/ad-account/broadtargetingcategories/  
**Processed:** 2025-06-25T16:25:34.483Z