{"title": "Facebook Marketing API - Ad Account Delivery Estimate", "summary": "The Ad Account Delivery Estimate endpoint returns delivery estimates for a given ad set configuration within an ad account. This read-only endpoint helps advertisers predict the potential reach and delivery of their ad campaigns before launching them.", "content": "# Ad Account Delivery Estimate\n\nReturns the delivery estimate for a given ad set configuration in this ad account. You are not able to retrieve this field for inactive Lookalike Audiences.\n\n## Reading\n\nDelivery estimate for a given ad set configuration in this ad account.\n\n### Endpoint\n\n```\nGET /v23.0/{ad-account-id}/delivery_estimate\n```\n\n### Required Parameters\n\n- **optimization_goal** (enum): The optimization goal for the estimate. Supported values include:\n  - `NONE`, `APP_INSTALLS`, `AD_RECALL_LIFT`, `ENGAGED_USERS`, `EVENT_RESPONSES`\n  - `IMPRESSIONS`, `LEAD_GENERATION`, `QUALITY_LEAD`, `LINK_CLICKS`\n  - `OFFSITE_CONVERSIONS`, `PAGE_LIKES`, `POST_ENGAGEMENT`, `QUALITY_CALL`\n  - `REACH`, `LANDING_PAGE_VIEWS`, `VISIT_INSTAGRAM_PROFILE`, `VALUE`\n  - `THRUPLAY`, `DERIVED_EVENTS`, `APP_INSTALLS_AND_OFFSITE_CONVERSIONS`\n  - `CONVERSATIONS`, `IN_APP_VALUE`, `MESSAGING_PURCHASE_CONVERSION`\n  - And many more...\n\n- **targeting_spec** (Targeting object): The targeting specification for delivery estimate\n\n### Optional Parameters\n\n#### Promoted Object\n- **application_id** (int): Facebook Application ID\n- **pixel_id** (numeric string): Facebook conversion pixel ID\n- **page_id** (Page ID): Facebook Page ID\n- **product_catalog_id** (numeric string): Product Catalog ID for Dynamic Product Ads\n- **event_id** (numeric string): Facebook Event ID\n- **custom_event_type** (enum): Custom event types for mobile apps\n- **object_store_url** (URL): Mobile/digital store URL\n- **offer_id** (numeric string): Facebook Page Offer ID\n- **instagram_profile_id** (numeric string): Instagram profile ID\n- **offline_conversion_data_set_id** (numeric string): Offline dataset ID\n- **fundraiser_campaign_id** (numeric string): Fundraiser campaign ID\n- **conversion_goal_id** (numeric string): Conversion Goal ID\n- **whats_app_business_phone_number_id** (numeric string): WhatsApp Business phone number ID\n\n#### Advanced Options\n- **omnichannel_object** (Object): Omnichannel configuration\n- **full_funnel_objective** (enum): Full funnel objective values (6, 8, 12, 14, 15, 19, 24, 26, 29, 31, 32, 35, 36, 37, 39, 41, 42, 40, 43, 44, 46)\n- **product_set_optimization** (enum): `enabled` or `disabled`\n- **value_semantic_type** (enum): `VALUE`, `MARGIN`, or `LIFETIME_VALUE`\n\n### Response Format\n\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\nThe `data` field contains a list of AdAccountDeliveryEstimate nodes.\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/delivery_estimate HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/delivery_estimate',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/delivery_estimate\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n### Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n| 368 | The action attempted has been deemed abusive or is otherwise disallowed |\n| 613 | Calls to this api have exceeded the rate limit |\n| 900 | No such application exists |\n| 2635 | You are calling a deprecated version of the Ads API |\n| 2641 | Your ad includes or excludes locations that are currently restricted |\n| 80004 | Too many calls to this ad-account. Wait and try again |\n\n### Limitations\n\n- **Read-only**: This endpoint only supports GET requests\n- **No Creating/Updating/Deleting**: These operations are not supported\n- **Inactive Lookalike Audiences**: Cannot retrieve estimates for inactive Lookalike Audiences", "keyPoints": ["Provides delivery estimates for ad set configurations before campaign launch", "Requires optimization_goal and targeting_spec as mandatory parameters", "Read-only endpoint - no create, update, or delete operations supported", "Cannot be used with inactive Lookalike Audiences", "Returns structured data with delivery predictions and paging information"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/delivery_estimate"], "parameters": ["optimization_goal", "targeting_spec", "promoted_object", "application_id", "pixel_id", "page_id", "product_catalog_id", "custom_event_type", "omnichannel_object", "full_funnel_objective", "value_semantic_type"], "examples": ["HTTP GET request to delivery_estimate endpoint", "PHP SDK implementation with error handling", "JavaScript SDK API call", "Android SDK GraphRequest example", "iOS SDK FBSDKGraphRequest example"], "tags": ["Facebook Marketing API", "Ad Account", "Delivery Estimate", "Campaign Planning", "Targeting", "Optimization Goals", "API Reference"], "relatedTopics": ["Ad Set Creation", "Advanced Targeting and Placement", "Dynamic Product Ads", "Lookalike Audiences", "Campaign Optimization Goals", "Graph API Usage", "Rate Limiting"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/delivery_estimate/", "processedAt": "2025-06-25T16:27:26.838Z", "processor": "openrouter-claude-sonnet-4"}