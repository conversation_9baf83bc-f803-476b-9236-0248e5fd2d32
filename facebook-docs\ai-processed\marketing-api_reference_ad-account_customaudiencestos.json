{"title": "Facebook Marketing API - Ad Account Custom Audiences Terms of Service", "summary": "This endpoint manages Terms of Service acceptance for Custom Audiences within Facebook Ad Accounts. It allows reading existing TOS status and creating new TOS acceptances for custom audience targeting features.", "content": "# Ad Account Custom Audiences Terms of Service\n\nThe Custom Audiences TOS endpoint manages Terms of Service acceptance for Custom Audiences functionality within Facebook Ad Accounts.\n\n## Reading\n\n### Endpoint\n```\nGET /v23.0/{ad-account-id}/customaudiencestos\n```\n\n### Parameters\nThis endpoint doesn't have any parameters.\n\n### Response Format\nReturns a JSON formatted result:\n```json\n{\n  \"data\": [],\n  \"paging\": {}\n}\n```\n\n#### Fields\n- **data**: A list of CustomAudiencesTOS nodes\n- **paging**: Standard pagination object for navigating results\n\n### Code Examples\n\n#### HTTP Request\n```http\nGET /v23.0/{ad-account-id}/customaudiencestos HTTP/1.1\nHost: graph.facebook.com\n```\n\n#### PHP SDK\n```php\n/* PHP SDK v5.0.0 */\ntry {\n  $response = $fb->get(\n    '/{ad-account-id}/customaudiencestos',\n    '{access-token}'\n  );\n} catch(Facebook\\Exceptions\\FacebookResponseException $e) {\n  echo 'Graph returned an error: ' . $e->getMessage();\n  exit;\n} catch(Facebook\\Exceptions\\FacebookSDKException $e) {\n  echo 'Facebook SDK returned an error: ' . $e->getMessage();\n  exit;\n}\n$graphNode = $response->getGraphNode();\n```\n\n#### JavaScript SDK\n```javascript\nFB.api(\n    \"/{ad-account-id}/customaudiencestos\",\n    function (response) {\n      if (response && !response.error) {\n        /* handle the result */\n      }\n    }\n);\n```\n\n## Creating\n\nYou can make a POST request to create new TOS acceptance records.\n\n### Endpoint\n```\nPOST /act_{ad_account_id}/customaudiencestos\n```\n\n### Parameters\n| Parameter | Type | Description | Required |\n|-----------|------|-------------|-----------|\n| business_id | string | Business identifier | No |\n| tos_id | string | Terms of Service identifier | Yes |\n\n### Return Type\nSupports read-after-write and returns:\n```json\n{\n  \"success\": bool\n}\n```\n\n## Error Codes\n\n| Error Code | Description |\n|------------|-------------|\n| 100 | Invalid parameter |\n| 190 | Invalid OAuth 2.0 Access Token |\n| 200 | Permissions error |\n\n## Limitations\n- **Updating**: Not supported on this endpoint\n- **Deleting**: Not supported on this endpoint", "keyPoints": ["Manages Terms of Service acceptance for Custom Audiences functionality", "Supports reading existing TOS status and creating new TOS acceptances", "Requires tos_id parameter for creating new TOS records", "Does not support updating or deleting operations", "Returns CustomAudiencesTOS nodes with standard pagination"], "apiEndpoints": ["GET /v23.0/{ad-account-id}/customaudiencestos", "POST /act_{ad_account_id}/customaudiencestos"], "parameters": ["business_id (string, optional)", "tos_id (string, required for POST)", "ad-account-id (path parameter)"], "examples": ["HTTP GET request example", "PHP SDK implementation", "JavaScript SDK usage", "Android SDK example", "iOS SDK example"], "tags": ["Facebook Marketing API", "Custom Audiences", "Terms of Service", "Ad Account", "Graph API"], "relatedTopics": ["Custom Audience Targeting", "Graph API pagination", "OAuth 2.0 Access Tokens", "Facebook SDK implementations", "Read-after-write operations"], "difficulty": "intermediate", "contentType": "reference", "originalUrl": "https://developers.facebook.com/docs/marketing-api/reference/ad-account/customaudiencestos/", "processedAt": "2025-06-25T16:26:26.373Z", "processor": "openrouter-claude-sonnet-4"}