{"title": "Facebook Marketing API Best Practices", "summary": "Comprehensive guide covering best practices for using the Facebook Marketing API effectively, including ad review triggers, pagination, batch requests, testing approaches, and compliance requirements. Essential reading for developers building applications with the Marketing API.", "content": "# Facebook Marketing API Best Practices\n\n## Ad Changes Triggering Ad Reviews\n\nIf you make any changes to the following scenarios, your ad will be triggered for review:\n\n- Any changes to your creative (image, text, link, video, and so on)\n- Any changes to targeting\n- Any changes of optimization goals and billing events may also trigger review\n\n**Note**: Changes to bid amount, budget, and ad set schedule will not have any effect on the review status.\n\nAdditionally, if an ad enters Ad Review with the run status of \"Paused\", then it will remain Paused upon exiting Ad Review. Otherwise, the ad will be considered Active and ready to deliver.\n\n## Pagination\n\nFor paging response data, see the [Graph API Pagination](/docs/graph-api/results).\n\n## User Information\n\nYou should store user IDs, session keys, and the ads account ID so it is easy to programmatically access them and keep them together. This is important because any calls made with an account ID belonging to one user and the session key for another user will fail with a permissions error. Any storages of user data must be done in compliance with [Facebook Platform Terms](/terms) and [Developer Policies](/devpolicy).\n\n## Suggested Bids\n\nRun frequent reports on your campaigns, as suggested bids change dynamically in response to bidding by competitors using similar targeting. Bid suggestions get updated within a few hours, depending upon the bidding of competitors.\n\n## Batch Requests\n\nMake multiple requests to the API with a single call, see:\n\n- [Multiple Requests](/docs/graph-api/making-multiple-requests)\n- [Batch Requests](/docs/reference/ads-api/batch-requests)\n\nYou can also query for multiple objects by ID as follows:\n\n```\nhttps://graph.facebook.com/<API_VERSION>?ids=[id1,id2]\n```\n\nTo query for a specific field:\n\n```\nhttps://graph.facebook.com/<API_VERSION>?ids=[id1,id2]&fields=field1,field2\n```\n\n## Check Data Changes using ETags\n\nQuickly check if the response to a request has changed since you last made it, see:\n\n- [ETags blog](/blog/post/627/)\n- [ETags Reference](/docs/reference/ads-api/etags-reference/)\n\n## Object Archive and Delete Status\n\nAd objects have two types of delete states: archived and deleted. You can query both archived and deleted objects with the object id. However, we do not return deleted objects if you request it from another object's edge.\n\nYou can have up to 5000 archived objects at any time. You should move ad objects from archived states to deleted states if you no longer need to retrieve them via edges. To learn how states work and for sample calls see [Storing Ad Objects](/docs/ads-api/best-practices/storing_adobjects).\n\n## Viewing Errors\n\nPeople make mistakes and try to create ads that are not accepted, [Error Codes](/docs/reference/ads-api/error-reference) provide reasons an API call failed. You should share some form of the error to users so they can fix their ads.\n\n## Facebook Marketing Developer Community Group\n\nJoin [Facebook Marketing Developer Community](https://www.facebook.com/groups/pmdcommunity/) group on Facebook for news and update on for Marketing API. We post items from the [Marketing API blog](/ads/blog/) to the group.\n\n## Testing\n\nSandbox mode is a testing environment to read and write Marketing API calls without delivering actual ads. See [Sandbox Mode for Developers](/ads/blog/post/2016/10/19/sandbox-ad-accounts/)\n\nTry API calls with [Graph API Explorer](/tools/explorer). You can try any API call you would like to make to the Marketing API, see [blog post](/blog/post/517/). Select your app in `App`, and grant your app `ads_management` or `ads_read` permission in `extended permissions` when you create an access token. Use `ads_read` if you only need Ads Insights API access for reporting. Use `ads_management` to read and update ads in an account.\n\nFor [development and basic access](/docs/reference/ads-api/access), configure a list of ad accounts your app is able to make API calls for, see [account list](/docs/reference/ads-api/access#standard_accounts).\n\nYou can use sandbox mode to demonstrate your app for app review. However in sandbox mode you cannot create ads or ad creative. Therefore you should use hard coded ad IDs and ad creative IDs to demonstrate your use of our API for app review.\n\n### Basic Criteria\n\n- Demonstrate value beyond Facebook's core solutions, such as [Facebook Ads Manager](https://www.facebook.com/ads/manager/)\n- Focus on business objectives, such as increase in sales. Facebook business objectives can be found [here](/docs/reference/ads-api/guides/chapter-2-objective-connections)\n\n## Policies\n\nUnderstand the API policies; Facebook has the right to audit your activity anytime:\n\n- **[Platform Terms](https://developers.facebook.com/terms)**\n- **[Developer Policies](https://developers.facebook.com/devpolicy)**\n- **[Promotion Policies](https://www.facebook.com/page_guidelines.php#promotionsguidelines)**\n- **[Data Use Policy](https://www.facebook.com/full_data_use_policy)**\n- **[Statement of Rights and Responsibilities](https://www.facebook.com/legal/terms)**\n- **[Advertising Guidelines](https://www.facebook.com/ad_guidelines.php)**\n\nBe ready to adapt quickly to changes. Most changes are [versioned](/docs/reference/ads-api/versions) and change windows are 90 days, ongoing.\n\nIn [Statement of Rights and Responsibilities](https://www.facebook.com/legal/terms), you are financially and operationally responsible for your application, its contents, and your use of the Meta Platform and the Ads API. You should manage your app's stability and potential bugs.", "keyPoints": ["Changes to ad creative, targeting, or optimization goals trigger ad reviews, but bid and budget changes do not", "Use batch requests and ETags to optimize API performance and reduce unnecessary calls", "Store user IDs, session keys, and ad account IDs together to avoid permissions errors", "Utilize sandbox mode for testing without delivering actual ads, and Graph API Explorer for trying API calls", "Comply with all Facebook policies as the platform has audit rights and you're responsible for your application"], "apiEndpoints": ["https://graph.facebook.com/<API_VERSION>?ids=[id1,id2]", "https://graph.facebook.com/<API_VERSION>?ids=[id1,id2]&fields=field1,field2"], "parameters": ["ids", "fields", "API_VERSION", "ads_management permission", "ads_read permission"], "examples": ["Query multiple objects by ID: https://graph.facebook.com/<API_VERSION>?ids=[id1,id2]", "Query specific fields: https://graph.facebook.com/<API_VERSION>?ids=[id1,id2]&fields=field1,field2"], "tags": ["best-practices", "marketing-api", "facebook-ads", "api-optimization", "testing", "compliance", "batch-requests", "pagination", "etags"], "relatedTopics": ["Graph API Pagination", "Batch Requests", "ETags", "Ad Review Process", "Sandbox Mode", "Graph API Explorer", "Erro<PERSON>", "Facebook Platform Terms", "Developer Policies", "Ads Manager"], "difficulty": "intermediate", "contentType": "guide", "originalUrl": "https://developers.facebook.com/docs/marketing-api/best-practices", "processedAt": "2025-06-25T16:15:55.832Z", "processor": "openrouter-claude-sonnet-4"}