{"title": "Conversions API", "breadcrumbs": [], "content": "<h1 id=\"conversions-api\">Conversions API</h1>\n\n<p>The Conversions API is designed to create a connection between an advertiser’s marketing data (such as website events, app events, business messaging events and offline conversions) from an advertiser’s server, website platform, mobile app, or CRM to Meta systems that optimize ad targeting, decrease cost per result and measure outcomes.</p>\n\n<p>Rather than maintaining separate connection points for each data source, advertisers are able to leverage the Conversions API to send multiple event types and simplify their technology stack. In the case of direct integrations, this entails establishing a connection between an advertiser’s server and Meta’s Conversions API endpoint.</p>\n\n<p>Server events are linked to a dataset ID and are processed like events sent using the Meta Pixel, Facebook SDK for iOS or Android, mobile measurement partner SDK, offline event set, or .csv upload. This means that server events may be used in measurement, reporting, or optimization in a similar way as other connection channels. Offline events may be used for attributed offline events measurement, offline custom audience creation or measurement.</p>\n\n<p>For optimal ad performance and measurement, we recommend that advertisers follow the <a href=\"/docs/marketing-api/conversions-api/best-practices\">Conversions API best practices</a>.</p>\n\n<h3>Recommended Steps</h3>\n\n<ol>\n<li><a href=\"/docs/marketing-api/conversions-api/get-started\"><strong>Get Started</strong></a>: Choose the integration method that works best for you, see prerequisites for using the API, and understand where to begin.</li>\n<li><a href=\"/docs/marketing-api/conversions-api/using-the-api\"><strong>Implement the API and start sending requests</strong></a>: Start making <code>POST</code> requests and learn more about dropped events, batch requests, and event transaction time.</li>\n<li><a href=\"/docs/marketing-api/conversions-api/verifying-setup\"><strong>Verify your setup</strong></a>: Confirm that we have received your events and that events are deduplicated and matched correctly.</li>\n</ol>\n\n\n<h2 id=\"documentation\">Documentation</h2>\n<table class=\"uiGrid _51mz _57v1 _5f0n\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"api-parameters\"><a href=\"/docs/marketing-api/conversions-api/parameters\">API Parameters</a></h3>\n\n<p>Required and optional parameters you can use to improve ads attribution and delivery optimization.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"payload-helper\"><a href=\"/docs/marketing-api/conversions-api/payload-helper\">Payload Helper</a></h3>\n\n<p>See how your payload should be structured when it is sent to Facebook from your server.</p>\n</td></tr><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"troubleshooting\"><a href=\"/docs/marketing-api/conversions-api/support\">Troubleshooting</a></h3>\n\n<p>Learn how to handle error codes returned by the Conversions API.</p>\n</td><td class=\"_51m-\"></td></tr></tbody></table>\n\n<h2 id=\"resources\">Resources</h2>\n<table class=\"uiGrid _51mz _57v1 _5f0n\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"meta-pixel-events\">Meta Pixel Events</h3>\n\n<p>Learn more about the Meta Pixel's <a href=\"/docs/facebook-pixel/implementation/conversion-tracking#standard-events\">Standard Events</a> and <a href=\"/docs/facebook-pixel/implementation/conversion-tracking#custom-events\">Custom Events</a>.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"business-help-center\">Business Help Center</h3>\n\n<p>From our Help Center, see <a href=\"https://www.facebook.com/business/help/2041148702652965\">About Conversions API</a> and <a href=\"https://www.facebook.com/business/help/1624255387706033\">Test Your Server Events</a>.</p>\n</td></tr><tr class=\"_51mx\"><td class=\"_51m- vTop hLeft _57v2 _2cs2\"><h3 id=\"playbook-and-webinar\">Playbook and Webinar</h3>\n\n<p>View the <a href=\"https://www.facebook.com/gms_hub/share/conversions-api-direct-integration-playbook_english.pdf\">Direct Integration Playbook for Developers (PDF)</a> and <a href=\"https://www.facebook.com/business/m/sessionsforsuccess/conversions-api\">Direct Integration Webinar for Developers</a>.</p>\n</td><td class=\"_51m- vTop hLeft _57v2 _2cs2 _51mw\"><h3 id=\"data-processing-options\"><a href=\"/docs/marketing-apis/data-processing-options\">Data Processing Options</a></h3>\n\n<p>Learn more about the Limited Data Use feature and how to implement it for Conversions API.</p>\n</td></tr></tbody></table>\n\n", "navigationLinks": ["/docs/marketing-api/conversions-api", "/docs/marketing-api/conversions-api/get-started", "/docs/marketing-api/conversions-api/using-the-api", "/docs/marketing-api/conversions-api/verifying-setup", "/docs/marketing-api/conversions-api/parameters", "/docs/marketing-api/conversions-api/parameter-builder-feature-library", "/docs/marketing-api/conversions-api/app-events", "/docs/marketing-api/conversions-api/offline-events", "/docs/marketing-api/conversions-api/business-messaging", "/docs/marketing-api/conversions-api/conversion-leads-integration", "/docs/marketing-api/conversions-api/dataset-quality-api", "/docs/marketing-api/conversions-api/deduplicate-pixel-and-server-events", "/docs/marketing-api/conversions-api/guides", "/docs/marketing-api/conversions-api/payload-helper", "/docs/marketing-api/conversions-api/best-practices", "/docs/marketing-api/conversions-api/support", "/docs/marketing-apis/data-processing-options"], "url": "https://developers.facebook.com/docs/marketing-api/conversions-api", "timestamp": "2025-06-25T15:49:47.177Z"}