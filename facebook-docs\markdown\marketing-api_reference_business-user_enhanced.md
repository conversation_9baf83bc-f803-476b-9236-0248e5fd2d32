# Facebook Marketing API - Business User Reference

## Summary
Complete reference documentation for the Business User endpoint in Facebook's Marketing API. Covers reading, creating, updating, and deleting business users, including their roles, permissions, and associated business assets.

## Key Points
- Business users can be employees or admins with different permission levels
- API access is restricted to businesses that have claimed the requesting app
- Supports full CRUD operations (Create, Read, Update, Delete)
- At least one admin must remain in Business Manager at all times
- Supports read-after-write functionality for immediate data retrieval

## API Endpoints
- `GET /v23.0/{business-user-id}`
- `POST /{business_id}/business_users`
- `POST /{business_user_id}`
- `DELETE /{business_user_id}`

## Parameters
- email
- role
- invited_user_type
- first_name
- last_name
- skip_verification_email
- finance_permission
- ip_permission
- two_fac_status

## Content
# Business User

**API Version:** v23.0

**Access Restrictions:** In Graph API v9.0, access to this endpoint was restricted. In Graph API v10.0, access has been restored to all apps, but apps can now only target businesses (or child businesses of those businesses) that have claimed them.

## Overview

Represents a business user in Facebook Business Manager. A business user can be:
- **Employee**: Can see all information in business settings and be assigned roles by business admins
- **Admin**: Can control all aspects of the business including modifying or deleting the account and adding or removing people from the employee list

## Reading Business Users

### Endpoint
```
GET /v23.0/{business-user-id}
```

### Parameters
This endpoint doesn't have any parameters.

### Fields

| Field | Type | Description | Default |
|-------|------|-------------|----------|
| `id` | numeric string | The business user's ID | ✓ |
| `business` | Business | Business user is associated with this business | ✓ |
| `email` | string | User's email as provided in Business Manager | |
| `finance_permission` | string | Financial permission role (EDITOR, ANALYST, etc.) | |
| `first_name` | string | User's first name as provided in Business Manager | |
| `ip_permission` | string | Ads right permission role (Reviewer, etc.) | |
| `last_name` | string | User's last name as provided in Business Manager | |
| `name` | string | Name of user as provided in Business Manager | ✓ |
| `pending_email` | string | Email pending verification | |
| `role` | string | Role in Business Manager (Admin, Employee, etc.) | |
| `title` | string | The title of the user in this business | |
| `two_fac_status` | string | Two-factor authentication status | |

### Edges

| Edge | Type | Description |
|------|------|-------------|
| `assigned_business_asset_groups` | Edge<BusinessAssetGroup> | Business asset groups assigned to this user |
| `assigned_pages` | Edge<Page> | Pages assigned to this user |
| `assigned_product_catalogs` | Edge<ProductCatalog> | Product catalogs assigned to this user |

## Creating Business Users

### Endpoint
```
POST /{business_id}/business_users
```

### Parameters

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `email` | string | Email of user to be added to this business | ✓ |
| `invited_user_type` | array<enum> | FB or MWA (defaults to FB if not specified) | |
| `role` | enum | Role of user (see role options below) | |

### Role Options
- `FINANCE_EDITOR`
- `FINANCE_ANALYST` 
- `ADS_RIGHTS_REVIEWER`
- `ADMIN`
- `EMPLOYEE`
- `DEVELOPER`
- `PARTNER_CENTER_ADMIN`
- `PARTNER_CENTER_ANALYST`
- `PARTNER_CENTER_OPERATIONS`
- `PARTNER_CENTER_MARKETING`
- `PARTNER_CENTER_EDUCATION`
- `MANAGE`
- `DEFAULT`
- `FINANCE_EDIT`
- `FINANCE_VIEW`

### Return Type
```json
{
  "id": "numeric string"
}
```

## Updating Business Users

### Endpoint
```
POST /{business_user_id}
```

### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `email` | string | The email of the user at this business |
| `first_name` | string | First name for this business user |
| `last_name` | string | Last name for this business user |
| `role` | enum | The role of the user at this business |
| `skip_verification_email` | boolean | Whether to skip sending verification email |

### Return Type
```json
{
  "success": true
}
```

## Deleting Business Users

### Endpoint
```
DELETE /{business_user_id}
```

### Parameters
This endpoint doesn't have any parameters.

### Return Type
```json
{
  "success": true
}
```

## Common Error Codes

| Error | Description |
|-------|-------------|
| 100 | Invalid parameter |
| 104 | Incorrect signature |
| 190 | Invalid OAuth 2.0 Access Token |
| 200 | Permissions error |
| 368 | The action attempted has been deemed abusive or is otherwise disallowed |
| 415 | Two factor authentication required |
| 457 | The session has an invalid origin |
| 613 | Calls to this api have exceeded the rate limit |
| 3914 | Cannot remove the last admin from Business Manager |

## Important Notes

- This endpoint supports read-after-write functionality
- At least one admin is required in Business Manager at all times
- Use 'MWA' for inviting users with Meta accounts managed by their organization
- Business persona emails require verification even when skipping verification emails

## Examples
GET /v23.0/{business-user-id} HTTP/1.1

POST /{business_id}/business_users with email and role parameters

Return type: {"id": "numeric string"}

Return type: {"success": true}

---
**Tags:** Facebook Marketing API, Business User, Business Manager, User Management, Permissions, CRUD Operations, Graph API
**Difficulty:** intermediate
**Content Type:** reference
**Source:** https://developers.facebook.com/docs/marketing-api/reference/business-user
**Processed:** 2025-06-25T15:45:15.078Z